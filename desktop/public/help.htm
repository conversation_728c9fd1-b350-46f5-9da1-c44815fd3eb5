<!-- <!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />

    <title></title>
  </head>

  <style>
    body::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  </style>

  <body class="root">
    <div id="app" class="app"></div>
  </body>
  <script>
    const languageMap = {
      en_US: 'en',
      es: 'es',
      fr_FR: 'fr',
      de_DE: 'de',
      it_IT: 'it',
      ja_JP: 'ja',
      ko_KR: 'ko',
      pt_PT: 'pt',
      zh_CN: 'zh',
      zh_TW: 'zh',
    };
    const getCustomerServiceLang = (lang) => {
      return languageMap[lang];
    };
    let options = {};
    const getConfig = async () => {
      const lang = await window.__E__?.store.get('USER_LANGUAGE');
      const email = await window.__E__?.store.get('EMAIL');
      options.lang = getCustomerServiceLang(lang);
      options.email = JSON.parse(email);
    };
    getConfig()
      .then(() => {
        window.__shulexConfig = { ...options };
      })
      .finally(() => {
        loadScript();
      });

    const loadScript = () => {
      const sc = document.createElement('script');
      sc.src =
        'https://apps.voc.ai/api_v2/gpt/bots/livechat/embed.js?id=21850&token=67DA21E9E4B0F84A3923EF15&noBrand=true';
      sc.async = true;
      sc.defer = true;
      document.body.appendChild(sc);
      const body = document.body;
      body.style.opacity = 0;
      const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
          for (let node of mutation.addedNodes) {
            if (node.nodeType === 1) {
              if (node.tagName.toLowerCase() === 'shulex-chatbot-lancher') {
                setTimeout(() => {
                  const launcher = document.querySelector('shulex-chatbot-lancher');
                  const shadowRoot = launcher.shadowRoot;
                  const button = shadowRoot.querySelector('#livechat_launcher_btn');
                  button.style.opacity = 0;
                  // const button = document.querySelector('shulex-chatbot-lancher #livechat_launcher_btn'); // 替换为你的按钮选择器
                  if (button) {
                    button.click(); // 模拟点击
                  }
                  body.style.opacity = 1;
                  const style = document.createElement('style');
                  style.textContent = `
              #shulex-chatbot-frame{
                width: 710px !important;
                height: 510px !important;
                opacity: 1 !important;
                top: -100px;
                left:0 !important;
                right:0 !important;
                bottom:0 !important;
              }
              #livechat_launcher_btn{
                display: none;
              }
              .pI_sEElitPL0TpBDSzpf {
                width: 710px !important;
                height: 510px !important;
              }    `;
                  shadowRoot.appendChild(style);
                }, 0);
                observer.disconnect();
                return;
              }
            }
          }
        });
      });

      // 开始观察 container 的子节点变化
      observer.observe(body, { childList: true, subtree: true });
    };
  </script>
</html> -->

<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <!-- Start of plaud Zendesk Widget script -->
    <script
      id="ze-snippet"
      src="https://static.zdassets.com/ekr/snippet.js?key=696b9176-2236-4cc6-b5b2-476bdf4fb38c"
    ></script>
    <!-- End of plaud Zendesk Widget script -->
    <title></title>
    <script>
      zE('messenger', 'open');
      // zh-cn
      // window.__E__?.store.get(LANGUAGE) || 'en_US';
      // zE('messenger:set', 'locale', 'en_US');
    </script>
  </head>

  <style>
    iframe {
      width: 710px !important;
      height: 505px !important;
      top: -65px !important;
      right: 0 !important;
      left: 0 !important;
    }
    iframe {
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
    }

    body::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  </style>

  <body class="root">
    <div id="app" class="app"></div>
  </body>
</html>
