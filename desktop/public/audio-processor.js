class AudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.config = {
      // LMS 算法参数
      filterLength: 512,         // 减小滤波器长度以提高性能
      learningRate: 0.005,       // 降低学习率以提高稳定性
      filterCoefficients: new Float32Array(512), // 滤波器系数
      inputBuffer: new Float32Array(512),        // 输入缓冲区
      bufferIndex: 0,            // 缓冲区索引
      
      // 音频处理参数
      inputGain: 0.7,           // 输入增益
      outputGain: 0.3,          // 输出增益
      noiseGate: 0.015,         // 噪声门限
      smoothingFactor: 0.8,     // 平滑因子
      
      // 状态变量
      lastOutput: 0,            // 上一次输出
      isInitialized: false,     // 初始化标志
      
      // 错误处理
      errorCount: 0,            // 错误计数
      maxErrors: 10             // 最大错误数
    };
  }

  // LMS 算法实现
  lmsFilter(input, desired) {
    try {
      // 更新输入缓冲区
      this.config.inputBuffer[this.config.bufferIndex] = input;
      
      // 计算滤波器输出
      let output = 0;
      for (let i = 0; i < this.config.filterLength; i++) {
        const bufferIndex = (this.config.bufferIndex - i + this.config.filterLength) % this.config.filterLength;
        output += this.config.filterCoefficients[i] * this.config.inputBuffer[bufferIndex];
      }
      
      // 计算误差
      const error = desired - output;
      
      // 更新滤波器系数
      for (let i = 0; i < this.config.filterLength; i++) {
        const bufferIndex = (this.config.bufferIndex - i + this.config.filterLength) % this.config.filterLength;
        this.config.filterCoefficients[i] += this.config.learningRate * error * this.config.inputBuffer[bufferIndex];
      }
      
      // 更新缓冲区索引
      this.config.bufferIndex = (this.config.bufferIndex + 1) % this.config.filterLength;
      
      return output;
    } catch (error) {
      // 错误处理
      this.config.errorCount++;
      if (this.config.errorCount > this.config.maxErrors) {
        console.error('LMS filter error:', error);
        // 重置错误计数
        this.config.errorCount = 0;
      }
      // 返回原始输入作为后备方案
      return input;
    }
  }

  // 噪声门限处理
  applyNoiseGate(sample) {
    return Math.abs(sample) < this.config.noiseGate ? 0 : sample;
  }

  // 平滑处理
  applySmoothing(current, previous) {
    return this.config.smoothingFactor * previous + (1 - this.config.smoothingFactor) * current;
  }

  process(inputs, outputs) {
    try {
      const input = inputs[0];
      const output = outputs[0];

      if (!input || !input[0]) return true;

      const channel = input[0];
      const outChannel = output[0];

      for (let i = 0; i < channel.length; i++) {
        const sample = channel[i];
        
        // 应用输入增益
        const gainedSample = sample * this.config.inputGain;
        
        // 应用 LMS 滤波器
        const filteredSample = this.lmsFilter(gainedSample, sample);
        
        // 应用噪声门限
        const gatedSample = this.applyNoiseGate(filteredSample);
        
        // 应用平滑处理
        const smoothedSample = this.applySmoothing(gatedSample, this.config.lastOutput);
        
        // 更新上一次输出
        this.config.lastOutput = smoothedSample;
        
        // 应用输出增益并写入输出通道
        outChannel[i] = smoothedSample * this.config.outputGain;
      }

      return true;
    } catch (error) {
      // 处理过程中的错误
      console.error('Audio processing error:', error);
      return true; // 继续处理
    }
  }
}

registerProcessor('audio-processor', AudioProcessor); 