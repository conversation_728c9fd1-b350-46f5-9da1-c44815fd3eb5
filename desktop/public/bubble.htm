<!doctype html>
<html>
  <head>
    <style>
      body {
        margin: 0;
        padding: 0;
        background: transparent; /* 透明背景 */
        font-family: Arial, sans-serif;
      }
      .bubble {
        background: #ffcc00; /* 气泡背景色 */
        color: #333;
        padding: 15px;
        border-radius: 10px; /* 圆角 */
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* 阴影 */
        position: relative;
        width: 260px;
        user-select: none; /* 禁止文本选中 */
        animation: fadeIn 0.3s ease-in; /* 淡入动画 */
      }
      /* 气泡三角形箭头（伪元素） */
      .bubble::after {
        content: '';
        position: absolute;
        top: -10px; /* 箭头位置 */
        left: 20px; /* 箭头水平位置 */
        border: 10px solid transparent;
        border-bottom-color: #ffcc00; /* 与气泡背景色一致 */
      }
      button {
        margin-top: 10px;
        padding: 5px 10px;
        cursor: pointer;
        border: none;
        background: #fff;
        border-radius: 5px;
      }
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <div class="bubble">
      <p>新版本已下载，请安装！</p>
      <button onclick="require('electron').ipcRenderer.send('close-bubble')">关闭</button>
    </div>
  </body>
</html>
