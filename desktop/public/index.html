<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title></title>
    <% htmlWebpackPlugin.options.styles.forEach(function(item){ %>
      <link rel="stylesheet" href=<%=item %>></script>
    <% }) %>
    
    <% htmlWebpackPlugin.options.scripts.forEach(function(item){ %>
      <script src=<%=item %>></script>
    <% }) %>
    <script>
      setTimeout(() => {
        // 获取目标元素
        const targetElement = document.body

        // 判断是否为 Windows 系统并添加类名
        if (!window.__E__?.isMac) {
          targetElement.classList.add('body-bottom');
        }else{
          targetElement.classList.add('body-top');
        }
      }, 0);
    </script>
  </head>

  <body class="root">
    <div id="app" class="app"></div>
  </body>
</html>
