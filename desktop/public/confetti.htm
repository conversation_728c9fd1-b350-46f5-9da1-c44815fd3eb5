<!doctype html>
<html>
  <body>
    <canvas id="confetti-canvas"></canvas>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js"></script>
    <script defer>
      window.onload = function () {
        const canvas = document.getElementById('confetti-canvas');
        const myConfetti = confetti.create(canvas, { resize: true, useWorker: false });
        var end = Date.now() + 1 * 800;
        const ctx = canvas.getContext('2d');
        // go Buckeyes!
        var colors = ['#93CBFF', '#ffffff', '#FF9004', 'FF2E54', 'C959DD', '5D68FF', '0894FF'];

        (function frame() {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          confetti({
            particleCount: 4,
            angle: 60,
            spread: 60,
            startVelocity: 60,
            gravity: 0.8,
            origin: { x: 0, y: 1 },
            shapes: ['circle', 'square', 'star'],
            colors: colors,
          });
          confetti({
            particleCount: 4,
            angle: 120,
            spread: 60,
            startVelocity: 60,
            gravity: 0.8,
            origin: { x: 1, y: 1 },
            shapes: ['circle', 'square', 'star'],
            colors: colors,
          });

          if (Date.now() < end) {
            requestAnimationFrame(frame);
          }
          setTimeout(() => {
            window.close();
          }, 1500);
        })();
      };
    </script>
    <style>
      #confetti-canvas {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        will-change: transform; /* 提示浏览器优化 */
      }
    </style>
  </body>
</html>
