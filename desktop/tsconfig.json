{
  "compilerOptions": {
    "baseUrl": "",
    "outDir": "dist",
    "paths": {
      "@/*": ["./src/*"],
    },
    "module": "esnext",
    "moduleResolution": "node",
    "target": "es2018",
    "jsx": "react",
    "strict": true,
    "allowJs": true,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "strictPropertyInitialization": true,
    "forceConsistentCasingInFileNames": true,
    "lib": ["dom", "es2015", "es2016", "es2017", "esnext"],
    "resolveJsonModule": true,
    "skipLibCheck": true
  },
  "typeRoots": ["src/", "./node_modules/@types"],
  "include": ["src/**/*"],
  "exclude": ["node_modules", "**/*.spec.ts", "**/*.js"]
}
