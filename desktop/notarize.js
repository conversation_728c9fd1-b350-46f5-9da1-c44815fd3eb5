const { notarize } = require('@electron/notarize');

exports.default = async function notarizing(context) {
  try {
    console.log('begin notarizing');
    const { electronPlatformName, appOutDir } = context;
    if (electronPlatformName !== 'darwin') {
      return;
    }

    const appName = context.packager.appInfo.productFilename;

    //   return await notarize({
    //     appBundleId: 'your.app.bundleId',
    //     appPath: `${appOutDir}/${appName}.app`,
    //     appleId: process.env.APPLE_ID,
    //     appleIdPassword: process.env.APPLE_APP_SPECIFIC_PASSWORD,
    //     teamId: process.env.APPLE_TEAM_ID,
    //   });
    // "notarize": {
    //   "teamId": "LXB439G292",
    //   "appleId": "<EMAIL>",
    //   "appleIdPassword": "zamw-tkbt-ckdo-zucs"
    // },
    await notarize({
      appBundleId: 'ai.plaud.desktop.plaud',
      appPath: `${appOutDir}/${appName}.app`,
      appleId: '<EMAIL>',
      appleIdPassword: 'zamw-tkbt-ckdo-zucs',
      teamId: 'W8JQQJQR29',
      opts: {
        waitForProcessing: true,
        timeout: 600000, // 10分钟超时
      },
    });
    console.log('公证完成');

    // 公证后额外验证
    const { exec } = require('child_process');
    new Promise((resolve, reject) => {
      exec(`spctl -a -vv -t exec "${appOutDir}/${appName}.app"`, (error, stdout, stderr) => {
        if (error) {
          console.error('公证验证失败:', stderr);
          reject(error);
        } else {
          console.log('公证验证成功');
          resolve();
        }
      });
    });

    // 多重验证
    await new Promise((resolve, reject) => {
      exec(
        `
        spctl -a -vv -t exec "${appOutDir}/${appName}.app" &&
        codesign -vvv --deep --strict "${appOutDir}/${appName}.app" 
      `,
        (error, stdout, stderr) => {
          if (error) {
            console.error('验证失败:', stderr);
            reject(error);
          } else {
            console.log('多重验证成功');
            resolve();
          }
        },
      );
    });
  } catch (error) {
    console.error('公证错误:', error);
    throw error;
  }
};
