import Foundation
import AVFoundation
import CoreAudio

// 1. 使用AVFoundation检测麦克风占用状态
func checkMicrophoneUsingAVFoundation() -> Bool {
    let audioSession = AVAudioSession.sharedInstance()
    var isMicInUse = false
    
    do {
        // 尝试设置音频会话类别
        try audioSession.setCategory(.playAndRecord, mode: .default)
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        
        // 如果能够成功激活会话，表示麦克风可用
        isMicInUse = false
        print("AVFoundation: 麦克风可用，未被其他应用占用")
    } catch {
        // 如果无法激活会话，可能是麦克风被占用
        isMicInUse = true
        print("AVFoundation: 麦克风被占用，错误: \(error.localizedDescription)")
    }
    
    // 确保会话不再活跃，以免影响其他应用
    do {
        try audioSession.setActive(false)
    } catch {
        print("无法停用音频会话: \(error.localizedDescription)")
    }
    
    return isMicInUse
}

// 2. 使用CoreAudio API检测麦克风占用状态（更详细的方法）
func checkMicrophoneUsingCoreAudio() -> Bool {
    var propertySize: UInt32 = 0
    var result: OSStatus = noErr
    
    // 获取所有音频设备数量
    var propsize: UInt32 = UInt32(MemoryLayout<UInt32>.size)
    var numberOfDevices: UInt32 = 0
    
    var deviceAddress = AudioObjectPropertyAddress(
        mSelector: kAudioHardwarePropertyDevices,
        mScope: kAudioObjectPropertyScopeGlobal,
        mElement: kAudioObjectPropertyElementMain)
    
    result = AudioObjectGetPropertyDataSize(
        AudioObjectID(kAudioObjectSystemObject),
        &deviceAddress,
        0,
        nil,
        &propsize)
    
    if result != noErr {
        print("获取音频设备数量时出错: \(result)")
        return false
    }
    
    numberOfDevices = propsize / UInt32(MemoryLayout<AudioDeviceID>.size)
    
    // 获取所有音频设备ID
    var deviceIDs = [AudioDeviceID](repeating: 0, count: Int(numberOfDevices))
    result = AudioObjectGetPropertyData(
        AudioObjectID(kAudioObjectSystemObject),
        &deviceAddress,
        0,
        nil,
        &propsize,
        &deviceIDs)
    
    if result != noErr {
        print("获取音频设备ID时出错: \(result)")
        return false
    }
    
    // 检查每个设备，查找输入设备（麦克风）并检查是否被使用
    for deviceID in deviceIDs {
        // 检查设备是否是输入设备
        var isInput: UInt32 = 0
        propertySize = UInt32(MemoryLayout<UInt32>.size)
        
        var inputStreamsAddress = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyStreamConfiguration,
            mScope: kAudioDevicePropertyScopeInput,
            mElement: kAudioObjectPropertyElementMain)
        
        // 检查设备是否有输入流
        if AudioObjectHasProperty(deviceID, &inputStreamsAddress) {
            var streamDescription = AudioBufferList()
            propertySize = UInt32(MemoryLayout<AudioBufferList>.size)
            
            result = AudioObjectGetPropertyData(
                deviceID,
                &inputStreamsAddress,
                0,
                nil,
                &propertySize,
                &streamDescription)
            
            if result == noErr && streamDescription.mNumberBuffers > 0 {
                isInput = 1
            }
        }
        
        // 如果是输入设备，检查是否正在使用中
        if isInput == 1 {
            // 获取设备名称
            var cfName: CFString? = nil
            propertySize = UInt32(MemoryLayout<CFString?>.size)
            
            var nameAddress = AudioObjectPropertyAddress(
                mSelector: kAudioObjectPropertyName,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain)
            
            if AudioObjectHasProperty(deviceID, &nameAddress) {
                result = AudioObjectGetPropertyData(
                    deviceID,
                    &nameAddress,
                    0,
                    nil,
                    &propertySize,
                    &cfName)
                
                let deviceName = cfName as String? ?? "未知设备"
                
                // 检查设备是否正在使用（通过检查是否被其他进程设置为默认输入设备）
                var activeAddress = AudioObjectPropertyAddress(
                    mSelector: kAudioDevicePropertyDeviceIsRunningSomewhere,
                    mScope: kAudioObjectPropertyScopeGlobal,
                    mElement: kAudioObjectPropertyElementMain)
                
                if AudioObjectHasProperty(deviceID, &activeAddress) {
                    var isActive: UInt32 = 0
                    propertySize = UInt32(MemoryLayout<UInt32>.size)
                    
                    result = AudioObjectGetPropertyData(
                        deviceID,
                        &activeAddress,
                        0,
                        nil,
                        &propertySize,
                        &isActive)
                    
                    if result == noErr && isActive == 1 {
                        print("CoreAudio: 麦克风设备 '\(deviceName)' 正在被使用")
                        return true
                    } else {
                        print("CoreAudio: 麦克风设备 '\(deviceName)' 未被使用")
                    }
                }
            }
        }
    }
    
    return false
}

// 3. 直接调用系统命令检查麦克风状态（命令行方式）
func checkMicrophoneUsingSystemCommand() -> Bool {
    let task = Process()
    task.launchPath = "/bin/bash"
    task.arguments = ["-c", "ioreg -l | grep -i 'AppleUSBAudioEngine' | grep 'IOAudioEngineState' | grep -v '0'"]
    
    let pipe = Pipe()
    task.standardOutput = pipe
    
    do {
        try task.run()
        
        let data = pipe.fileHandleForReading.readDataToEndOfFile()
        if let output = String(data: data, encoding: .utf8) {
            let isMicInUse = !output.isEmpty
            print("系统命令: 麦克风状态: \(isMicInUse ? "被占用" : "未被占用")")
            return isMicInUse
        }
    } catch {
        print("执行系统命令时出错: \(error.localizedDescription)")
    }
    
    return false
}

// 4. 获取默认输入设备并检查其运行状态
func checkDefaultInputDeviceStatus() -> Bool {
    var propertySize: UInt32 = 0
    var propertyAddress = AudioObjectPropertyAddress(
        mSelector: kAudioHardwarePropertyDefaultInputDevice,
        mScope: kAudioObjectPropertyScopeGlobal,
        mElement: kAudioObjectPropertyElementMain)
    
    // 获取默认输入设备ID
    var deviceID: AudioDeviceID = 0
    propertySize = UInt32(MemoryLayout<AudioDeviceID>.size)
    
    let status = AudioObjectGetPropertyData(
        AudioObjectID(kAudioObjectSystemObject),
        &propertyAddress,
        0,
        nil,
        &propertySize,
        &deviceID)
    
    if status != noErr {
        print("获取默认输入设备失败: \(status)")
        return false
    }
    
    // 检查设备是否在运行
    var runningAddress = AudioObjectPropertyAddress(
        mSelector: kAudioDevicePropertyDeviceIsRunningSomewhere,
        mScope: kAudioObjectPropertyScopeGlobal,
        mElement: kAudioObjectPropertyElementMain)
    
    if AudioObjectHasProperty(deviceID, &runningAddress) {
        var isRunning: UInt32 = 0
        propertySize = UInt32(MemoryLayout<UInt32>.size)
        
        let result = AudioObjectGetPropertyData(
            deviceID,
            &runningAddress,
            0,
            nil,
            &propertySize,
            &isRunning)
        
        if result == noErr {
            print("默认输入设备状态: \(isRunning == 1 ? "正在运行" : "未运行")")
            return isRunning == 1
        }
    }
    
    return false
}

// 5. 组合多种方法进行检测，提高准确性
func isMicrophoneInUse() -> Bool {
    let avFoundationResult = checkMicrophoneUsingAVFoundation()
    let coreAudioResult = checkMicrophoneUsingCoreAudio()
    let defaultDeviceResult = checkDefaultInputDeviceStatus()
    let systemCommandResult = checkMicrophoneUsingSystemCommand()
    
    print("检测结果汇总:")
    print("- AVFoundation 方法: 麦克风\(avFoundationResult ? "被占用" : "未被占用")")
    print("- CoreAudio 方法: 麦克风\(coreAudioResult ? "被占用" : "未被占用")")
    print("- 默认输入设备方法: 麦克风\(defaultDeviceResult ? "被占用" : "未被占用")")
    print("- 系统命令方法: 麦克风\(systemCommandResult ? "被占用" : "未被占用")")
    
    // 使用多数决策或优先级决策
    // 这里我们优先采用CoreAudio和默认设备的结果，辅以AVFoundation的结果
    let primaryMethodsResult = coreAudioResult || defaultDeviceResult
    
    // 如果主要方法检测到麦克风被占用，我们就认为麦克风被占用
    if primaryMethodsResult {
        return true
    }
    
    // 如果主要方法没有检测到占用，但AVFoundation检测到了，我们进一步确认
    if avFoundationResult {
        // 如果系统命令也检测到了，我们就更确信麦克风被占用
        if systemCommandResult {
            return true
        }
        // 否则，我们可能有假阳性，但仍然倾向于认为麦克风被占用
        print("警告: AVFoundation检测到麦克风占用，但其他方法未检测到，可能存在假阳性")
        return true
    }
    
    // 如果所有主要方法都没有检测到占用，我们认为麦克风未被占用
    return false
}

// 6. 创建一个用于Electron等调用的简单返回结果的函数
func getMicrophoneStatus() -> String {
    let isInUse = isMicrophoneInUse()
    return isInUse ? "in_use" : "available"
}

// 主函数 - 用于直接运行此脚本
func main() {
    let isInUse = isMicrophoneInUse()
    print("最终结论: 麦克风\(isInUse ? "正在被其他应用使用" : "当前可用")")
    
    // 简单的返回值，方便其他程序解析
    print("RESULT:\(isInUse ? "in_use" : "available")")
    
    // 可以根据检测结果执行相应操作
    if !isInUse {
        print("麦克风可用，可以开始录音...")
        // 此处添加开始录音的代码
    } else {
        print("麦克风被占用，等待中...")
        // 此处添加等待或通知用户的代码
    }
}

// 运行主函数
main()