{"name": "PLAUD", "publicPath": "./", "cdn": false, "cdnDeploy": false, "sentry": false, "project": "web", "version": "0.5.2", "description": "PLAUD Desktop", "main": "electron/main.js", "private": true, "author": "PLAUD", "license": "UNLICENSED", "scripts": {"start": "concurrently --names \"WEB,DESKTOP\" -c \"red,blue\" \"yarn start:web\" \"wait-on http://localhost:4080/index.html && yarn start:desktop\"", "start:desktop": "cross-env NODE_ENV=development electronmon .", "start:web": "nebulon start", "build": "nebulon build", "format": "nebulon format", "lint": "nebulon lint", "analyse": "nebulon analyse", "check-type": "tsc --noEmit", "copy": "nebulon copy", "test": "nebulon test", "scan": "nebulon scan", "pkg:mac": "rimraf pkg && electron-builder -m ", "pkg:win": "rimraf pkg && electron-builder -w", "pkg": "rimraf pkg && electron-builder -mw", "p:mac": "rimraf pkg && cross-env GH_TOKEN=**************************************** electron-builder -m -p always", "p:win": "rimraf pkg && cross-env GH_TOKEN=**************************************** electron-builder -w -p always", "p": "rimraf pkg && cross-env GH_TOKEN=**************************************** electron-builder -mw -p always", "clean": "electron-builder -install-app-deps ", "pub:win": "rimraf pkg && cross-env GH_TOKEN=**************************************** electron-builder -w -p always", "pub:mac": "rimraf pkg && cross-env GH_TOKEN=**************************************** electron-builder -m -p always", "pub": "rimraf pkg && cross-env GH_TOKEN=**************************************** electron-builder -mw -p always", "pkg:test": "rimraf pkg && electron-builder -m"}, "husky": {"hooks": {"pre-commit": "git add . && tsc --noEmit && lint-staged"}}, "lint-staged": {"./src/**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint", "git add ."]}, "eslintConfig": {"extends": ["./node_modules/nebulon/scripts/lint.js"]}, "prettier": "nebulon/scripts/prettier.js", "babel": {"extends": "nebulon/scripts/babel.js"}, "browserslist": "last 2 Chrome versions", "devDependencies": {"@electron/notarize": "^2.5.0", "@types/uuid": "^10.0.0", "concurrently": "^9.1.0", "electron": "33.2.1", "electron-builder": "^25.1.8", "electron-log": "^5.2.4", "electronmon": "^2.0.3", "mitt": "^3.0.1", "nebulon": "latest", "radix-vue": "^1.9.10", "vue": "^3.4.29", "vue-i18n": "9", "vue-loader": "^17.4.2", "vue-router": "^4.5.0", "wait-on": "^8.0.1", "wavesurfer.js": "^7.8.9"}, "dependencies": {"@sentry/electron": "^6.1.0", "archiver": "^7.0.1", "archiver-zip-encrypted": "^2.0.0", "auto-launch": "^5.0.6", "axios": "^1.8.4", "electron-store": "8.2.0", "electron-updater": "^6.3.9", "firebase-admin": "^13.2.0", "i18next": "^24.2.2", "i18next-fs-backend": "^2.6.0", "lodash": "^4.17.21", "sudo-prompt": "^9.2.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "host": {"ip": "", "port": 10000, "env": {}}, "build": {"appId": "ai.plaud.desktop.plaud", "productName": "PLAUD", "protocols": {"name": "PLAUD", "schemes": ["PLAUD"]}, "files": ["electron/**/*", "dist/**/*", "resources/**/*"], "extraResources": [{"from": "resources/PLAUDAudio.pkg", "to": "PLAUDAudio.pkg"}, {"from": "resources/SwitchAudioSource", "to": "SwitchAudioSource"}, {"from": "resources/SoundVolumeView.exe", "to": "SoundVolumeView.exe"}], "directories": {"output": "pkg"}, "afterSign": "./notarize.js", "mac": {"identity": "EA84EE4FBE18BDC2B47085187A47C926AE5A58BB", "notarize": false, "target": [{"target": "zip", "arch": ["x64", "arm64"]}, {"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "extendInfo": {"NSMicrophoneUsageDescription": "We need microphone access to record audio", "NSNotificationUsageDescription": "We need notification access to show notification"}, "protocols": [{"name": "PLAUD", "schemes": ["PLAUD"]}], "category": "public.app-category.utilities", "hardenedRuntime": true, "gatekeeperAssess": false}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build/logo_1024.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "allowElevation": true, "perMachine": true, "createStartMenuShortcut": true, "installerHeader": "silent.nsh"}, "asar": {"smartUnpack": true}, "publish": {"provider": "s3", "bucket": "plaud-web-download-prod", "region": "us-west-2", "acl": "public-read", "path": "/desktop/test"}}}