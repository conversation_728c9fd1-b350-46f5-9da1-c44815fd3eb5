/** @type {import('tailwindcss').Config} */

import type { Config } from 'tailwindcss';
// import { blackA, green, grass } from '@radix-ui/colors';
const config = {
  // darkMode: ['class'],
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  prefix: '',
  theme: {
    extend: {
      boxShadow: {
        1: 'var(--shadow1)',
        2: 'var(--shadow2)',
        3: 'var(--shadow3)',
      },

      colors: {
        blue1: 'var(--blue1)',
        blue2: 'var(--blue2)',
        blue3: 'var(--blue3)',

        gray1: 'var(--gray1)',
        gray2: 'hsl(218, 17%, 91%)',
        gray3: 'var(--gray3)',
        gray4: 'var(--gray4)',
        gray5: 'var(--gray5)',
        gray6: 'var(--gray6)',
        gray7: 'var(--gray7)',
        gray8: 'var(--gray8)',
        gray9: 'var(--gray9)',
        gray10: 'var(--gray10)',

        black1: 'var(--black1)',
        black2: 'var(--black2)',
        black3: 'var(--black3)',
        black4: 'var(--black4)',
        black5: 'var(--black5)',
        black6: 'var(--black6)',
        black7: 'var(--black7)',

        red1: 'rgba(241, 67, 73, 1)',
        red2: 'rgba(255, 214, 212, 1)',
        red3: 'rgba(255, 59, 48, 1)',

        green1: 'rgba(52, 199, 89, 1)',
      },

      keyframes: {
        slideDownAndFade: {
          from: { opacity: 0, transform: 'translateY(-2px)' },
          to: { opacity: 1, transform: 'translateY(0)' },
        },
        slideLeftAndFade: {
          from: { opacity: 0, transform: 'translateX(2px)' },
          to: { opacity: 1, transform: 'translateX(0)' },
        },
        slideUpAndFade: {
          from: { opacity: 0, transform: 'translateY(2px)' },
          to: { opacity: 1, transform: 'translateY(0)' },
        },
        slideRightAndFade: {
          from: { opacity: 0, transform: 'translateX(-2px)' },
          to: { opacity: 1, transform: 'translateX(0)' },
        },

        overlayShow: {
          from: { opacity: 0 },
          to: { opacity: 1 },
        },
        contentShow: {
          from: { opacity: 0, transform: 'translate(-50%, -48%) scale(0.96)' },
          to: { opacity: 1, transform: 'translate(-50%, -50%) scale(1)' },
        },
      },
      animation: {
        slideDownAndFade: 'slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        slideLeftAndFade: 'slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        slideUpAndFade: 'slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        slideRightAndFade: 'slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        overlayShow: 'overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1)',
        contentShow: 'contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1)',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
} satisfies Config;

export default config;
