if (!process.env.NODE_ENV) {
  // process.env.NODE_ENV = 'test';
  process.env.NODE_ENV = 'production';
}
const Sentry = require('@sentry/electron/main');
Sentry.init({
  dsn: 'https://<EMAIL>/2',
  environment: process.env.NODE_ENV,
  attachScreenshot: true,
  enableRendererProfiling: true,
});

const fs = require('fs');
const path = require('path');
const url = require('url');
const { exec } = require('child_process');
const {
  app,
  BrowserWindow,
  ipcMain,
  Menu,
  globalShortcut,
  systemPreferences,
  screen,
  Tray,
  Notification,
  nativeTheme,
  shell,
  dialog,
  nativeImage,
  powerMonitor,
} = require('electron');
const os = require('os');
const DailyRotateFile = require('winston-daily-rotate-file');
const { autoUpdater } = require('electron-updater');
// autoUpdater.autoDownload = false;
// autoUpdater.autoInstallOnAppQuit = true;

// const log = require("electron-log");
const winston = require('winston');
const { i18n } = require('./locales/i18n');
const {
  ERROR_FILES,
  LANGUAGE,
  AUTO_LAUNCH,
  DEVICE_NAME,
  BASE_WIDTH,
  BASE_HEIGHT,
  APPS,
  DEFUALT_APPS,
  DEFUALT_BROWSERS,
  AUTO_RECORD,
  EMAIL,
  USERID,
} = require('./constant');

const { execPromise } = require('./utils');
// const TOKEN = 'USER_TOKEN';
// const LANGUAGE = 'USER_LANGUAGE';
// const NOTIFY = 'NOTIFY';

const Store = require('electron-store');
const audioDir = path.join(app.getPath('userData'), 'recordings');
const throttle = require('lodash/throttle');
if (!fs.existsSync(audioDir)) {
  fs.mkdirSync(audioDir);
}
// 引入文件压缩加密
const { doZipAndUploadFiles, uploadFile } = require('./compressFile.js');

const admin = require('firebase-admin');

// 会议检测：引入会议检测器
const MeetingDetector = require('./meetingDetector');
// 会议检测：初始化会议检测器
let meetingDetector = null;

// 初始化 Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert({
    type: 'service_account',
    project_id: 'plaud-35e0f',
    private_key_id: '81b50a90bc6bb996bee7cff035761c5006cb8ffa',
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email: '<EMAIL>',
    client_id: '109132123674421156544',
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url:
      'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-pb2um%40plaud-35e0f.iam.gserviceaccount.com',
    universe_domain: 'googleapis.com',
  }),
});
const db = admin.firestore();
const PREFIX = 'desktop_';

ipcMain.on('log-event', async (event, data) => {
  const { type, ...params } = data;
  if (!type) {
    return;
  }

  db.collection('analytics').add({
    type: PREFIX + type,
    params,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });
});

let updateMessage = '';
const WAIT_TIME = 2000;
let defaultDevice = '';
const store = new Store();
store.delete(APPS);

if (!store.has(APPS)) {
  store.set(APPS, DEFUALT_APPS);
}
// store.delete(AUTO_RECORD);
if (!store.has(AUTO_RECORD)) {
  store.set(AUTO_RECORD, false);
}
let userToken = '';
const isDev = process.env.NODE_ENV === 'development' ? true : false;
const isDevOrTest = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';
const HOST =
  process.env.NODE_ENV === 'production'
    ? 'https://app.plaud.ai/'
    : 'https://plaud-web-dist-test.pages.dev/web9/';
let tray;
function hasProblematicParenthesis(path) {
  // 检查不成对的括号
  const left = (path.match(/\(/g) || []).length;
  const right = (path.match(/\)/g) || []).length;

  // 返回true如果:
  // 1. 有左括号但没有右括号，或者
  // 2. 有右括号但没有左括号
  return (left > 0 && right === 0) || (right > 0 && left === 0);
}
const logPath = path.join(app.getPath('userData'), 'logs');
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  format: winston.format.combine(
    // 添加时间戳
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss',
    }),
    // 格式化输出
    winston.format.printf((info) => {
      return `${info.timestamp} ${info.level}: ${JSON.stringify(info.message)}`;
    }),
  ),
  // 解决有些用户因为用户名只有单个括号导致的App没办法启动问题
  transports: hasProblematicParenthesis(logPath)
    ? [
        new winston.transports.File({
          filename: path.join(logPath, 'combined.log'),
          maxsize: 20 * 1024 * 1024, // 20MB
          maxFiles: 14,
        }),
        new winston.transports.File({
          filename: path.join(logPath, 'error.log'),
          level: 'error',
          maxsize: 20 * 1024 * 1024, // 20MB
          maxFiles: 14,
        }),
      ]
    : [
        // 常规日志轮转配置
        new DailyRotateFile({
          filename: path.join(logPath, 'log-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          auditFile: path.join(logPath, 'audit.json'),
          zippedArchive: true,
        }),

        // 错误日志单独配置
        new DailyRotateFile({
          filename: path.join(logPath, 'error-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxSize: '20m',
          maxFiles: '30d',
          zippedArchive: true,
        }),
      ],
});

// 添加开发环境控制台输出
if (isDev) {
  logger.add(new winston.transports.Console());
}
// 系统信息
logger.info(`Platform: ${os.platform()}`); // 例如：win32
logger.info(`Release: ${os.release()}`); // 例如：10.0.22621
logger.info(`Type: ${os.type()}`); // 例如：Windows_NT
logger.info(`Arch: ${os.arch()}`); // 例如：x64
logger.info(`Hostname: ${os.hostname()}`); // 主机名
logger.info(`CPUs: ${JSON.stringify(os.cpus())}`); // CPU 信息

// autoUpdater.logger = logger;
autoUpdater.setFeedURL({
  provider: 's3',
  bucket: 'plaud-web-download-prod',
  region: 'us-west-2',
  path: '/desktop/test',
});

logger.info('launch');

let autoLauncher;

let audioSwitcher;

if (process.platform === 'darwin') {
  const { AudioDeviceSwitcher } = require('./switcher');
  audioSwitcher = new AudioDeviceSwitcher({ logger });

  const AutoLaunch = require('auto-launch');
  autoLauncher = new AutoLaunch({
    name: app.getName(),
    path: process.execPath,
  });
}
let mainWindow, settingsWindow, helpWindow;
let launchUrl = null;
let clickCount = 0;

if (process.platform === 'win32') {
  app.commandLine.appendSwitch('high-dpi-support', 'true');
  // app.commandLine.appendSwitch('force-device-scale-factor', '1');

  if (!isDev) {
    const sourcePath = path.join(process.resourcesPath, 'SoundVolumeView.exe');
    const targetPath = path.join(app.getPath('userData'), 'SoundVolumeView.exe');

    if (!fs.existsSync(targetPath)) {
      fs.copyFileSync(sourcePath, targetPath);
    }
  }
}

// 开启css最新实验特性
// app.commandLine.appendSwitch('enable-experimental-web-platform-features', 'enable');
// app.commandLine.appendSwitch('enable-features', 'EnableSystemAudioCaptureApi');

// 设置一个顶部菜单栏
Menu.setApplicationMenu(null);

if (!app.isDefaultProtocolClient('PLAUD')) {
  app.setAsDefaultProtocolClient('PLAUD');
}

// Mac 下处理协议启动
if (process.platform === 'darwin') {
  // 在 will-finish-launching 阶段保存启动URL
  app.on('will-finish-launching', () => {
    app.on('open-url', (event, url) => {
      event.preventDefault();

      if (!app.isReady()) {
        // 应用未就绪时保存URL
        launchUrl = url;
      } else {
        // 应用已就绪直接处理URL
        handleProtocol(url, true);
      }
    });
  });
}
// store.set('on-boarding', 0);
const onBoarding = store.get('on-boarding');
logger.info(`初始化onBoarding:${onBoarding}`);

if (app.requestSingleInstanceLock()) {
  logger.info('before second-instance');
  app.on('second-instance', (event, arg) => {
    logger.info('second-instance');
    if (mainWindow) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      mainWindow.focus();
      showWindowAboveTray();

      logger.info('second-instance before handleProtocol');
      // 检查是否有协议参数
      handleProtocol(
        arg.find((arg) => arg.startsWith('plaud://')),
        true,
      );
    }
  });

  app.on('activate', () => {
    logger.info('activate');
    const onBoardingComplete = store.get('on-boarding');
    if (mainWindow && onBoardingComplete) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      mainWindow.focus();
      setTimeout(() => {
        showWindowAboveTray();
      }, 100);
    }
  });

  app.on('ready', () => {
    logger.info('ready');
    if (!onBoarding) {
      setTimeout(() => {
        createOnBoardingWindow();
      }, 2000);
    }
    createWindow();
    setTimeout(() => {
      createHelpsWindow();
    }, 5000);

    // 会议检测：初始化会议检测器
    setTimeout(() => {
      initMeetingDetector();
    }, 1000);

    powerMonitor.on('suspend', () => {
      // 通知渲染进程
      // mainWindow.webContents.send('system-sleep', 'suspend');
      logger.info('computer suspend');
    });
    powerMonitor.on('resume', () => {
      // 通知渲染进程
      // mainWindow.webContents.send('system-sleep', 'resume');
      logger.info('computer resume');
    });
  });
} else {
  // 限制只打开一次窗口
  app.quit();
}
let latestVersion = '';
let trayTitle = '';
let updateNotification = '';
let isRecordOrUpload = false;
let latestTipTime = 0;
// 监听更新事件
autoUpdater.on('update-available', (data) => {
  logger.info(`dataverion:${JSON.stringify(data)}`);
  latestVersion = data.version;
  logger.info('update-available');
  // '检测到新版本，开始下载'
  updateMessage = i18n.t('start_download');
  settingsWindow?.webContents?.send('update-message', updateMessage);
  if (Notification.isSupported()) {
    logger.info(`update-notification-tip`);
    updateNotification = new Notification({
      title: i18n.t('new_version_avaliable'),
      body: i18n.t('click_to_update_to') + ' ' + latestVersion,
      silent: false,
    });
    updateNotification.on('click', () => {
      logger.info(`update-notification-click`);
      if (!isRecordOrUpload) {
        autoUpdater.quitAndInstall(true, true);
        logger.info(`quitAndInstall`);
      } else {
        dialog.showMessageBox({
          type: 'question',
          buttons: [i18n.t('confirm')],
          title: i18n.t('update_title'),
          message: i18n.t('update_tip_message'),
        });
      }

      trayTitle = '';
      tray.setTitle(trayTitle, { fontType: 'monospacedDigit' });
    });
  } else {
    logger.info(`Notification Unupported`);
  }

  // autoUpdater.downloadUpdate();
  // dialog.showMessageBox({
  //   type: 'info',
  //   title: '更新提示',
  //   message: '检测到新版本，正在后台下载更新...',
  // });
});

autoUpdater.on('update-downloaded', () => {
  logger.info('update-downloaded: 下载完成，准备安装...');
  // 下载完成
  updateMessage = i18n.t('download_complete');
  settingsWindow?.webContents?.send('update-message', updateMessage);
  updateMessage = '';
  mainWindow.webContents.send('update-ready');
  tray.setImage(getIcon('update'));

  logger.info(`latestVersion:${latestVersion}`);

  if (+new Date() - latestTipTime > 1000 * 60 * 60 * 4) {
    latestTipTime = +new Date();
    logger.info(`updateNotificationShow}`);
    updateNotification.show();
  }
  // 有录音中和上传中时，不展示可更新
  if (!trayTitle) {
    logger.info(`showUpdateTitle}`);
    trayTitle = i18n.t('new_version');
    tray.setTitle(trayTitle, { fontType: 'monospacedDigit' });
  }

  // dialog
  //   .showMessageBox({
  //     type: 'question',
  //     buttons: [i18n.t('install_now'), i18n.t('later')],
  //     defaultId: 0,
  //     title: i18n.t('update_title'),
  //     message: i18n.t('update_message'),
  //   })
  //   .then((result) => {
  //     if (result.response === 0) {
  //       autoUpdater.quitAndInstall(true, true);
  //     }
  //   });
});

autoUpdater.on('checking-for-update', () => {
  // 正在检查更新
  updateMessage = `${i18n.t('checking_for_updates')}...`;
  settingsWindow?.webContents?.send('update-message', updateMessage);
  logger.info('Checking for update...');
});

autoUpdater.on('update-not-available', (info) => {
  // 已是最新版本
  updateMessage = i18n.t('already_lastest');
  settingsWindow?.webContents?.send('update-message', updateMessage);
  updateMessage = '';
  logger.info('Update not available.');
});
autoUpdater.on('error', (err) => {
  logger.error('Error in auto-updater. ' + err);
  // 更新失败
  updateMessage = i18n.t('update_failed');
  settingsWindow?.webContents?.send('update-message', updateMessage);
  setTimeout(() => {
    updateMessage = '';
    settingsWindow?.webContents?.send('update-message', updateMessage);
  }, WAIT_TIME);
});
autoUpdater.on('download-progress', (progressObj) => {
  let log_message = 'Download speed: ' + progressObj.bytesPerSecond;
  log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
  log_message = log_message + ' (' + progressObj.transferred + '/' + progressObj.total + ')';
  // 下载中
  updateMessage = `${i18n.t('downloading')}: ${Math.floor(progressObj.percent)}%`;
  settingsWindow?.webContents?.send('update-message', updateMessage);
  logger.info(log_message);
});

async function createWindow() {
  // if (process.platform === 'darwin') {
  //   app.dock.setIcon(path.join(__dirname, 'assets/images/icon-1.icns'));
  // }
  // console.log(
  //   path.join(__dirname, 'assets/images/icon-1.icns'),
  //   "path.join(__dirname, 'assets/images/icon-1.icns')",
  // );
  logger.info('createWindow');
  // if (process.platform === 'darwin') {
  //   app.dock.show();
  // }

  // for test: 记得改回去！！！
  mainWindow = new BrowserWindow({
    width: BASE_WIDTH,
    height: BASE_HEIGHT,
    resizable: isDev,
    frame: false, //关闭原生导航栏
    show: false,
    skipTaskbar: true, // 不在任务栏显示
    transparent: true, // 透明窗口
    // icon: path.join(__dirname, 'assets/images/Icon-white'), // 设置图标路径
    webPreferences: {
      nodeIntegration: true,
      webviewTag: true,
      webSecurity: false,
      contextIsolation: true,
      mediaAccessibility: true,
      preload: path.join(__dirname, 'preload.js'),
    },
  });
  if (isDev) {
    globalShortcut.register('CommandOrControl+Shift+i', function () {
      mainWindow.webContents.openDevTools();
    });
  }

  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  if (isDev) {
    mainWindow.loadURL(
      url.format({
        protocol: 'http:',
        host: `localhost:3692`,
        pathname: 'index.html',
        hash: '',
        slashes: true,
      }),
    );
  } else {
    // mainWindow.loadURL(
    //   url.format({
    //     protocol: 'http:',
    //     host: `localhost:3692`,
    //     pathname: 'index.html',
    //     hash: '',
    //     slashes: true,
    //   }),
    // );
    mainWindow.loadFile(path.join(app.getAppPath(), 'dist', 'index.html'));
  }

  if (process.platform === 'win32') {
    adjustWindowToScale();
    // 监听显示器变化（例如用户调整缩放设置）
    screen.on('display-metrics-changed', (event, display) => {
      adjustWindowToScale();
    });
  }

  // 注释 勿删
  // mainWindow.on('show', () => {
  //   mainWindow.webContents.send('visible', 'show');
  //   logger.info('mainWindow show');
  // });

  // mainWindow.on('hide', () => {
  //   mainWindow.webContents.send('visible', 'hide');
  //   logger.info('mainWindow hide');
  // });

  initTray();

  // 监听打开设置窗口的事件
  ipcMain.on('create-settings', (event, arg) => {
    logger.info(`ipc-on-create-settings:${JSON.stringify(arg)}`);
    if (arg?.type) {
      createSettingsWindow({ type: 'Ppcguide' });
      return;
    }
    createSettingsWindow();
  });

  ipcMain.on('open-settings', (event, data) => {
    logger.info(`ipc-on-open-settings`);
    openSettings(event, data);
  });

  ipcMain.on('app-exit', async () => {
    logger.info(`ipc-on-app-exit`);
    exitInterception();
  });

  ipcMain.on('store-set', (event, { key, value }) => {
    logger.info(`ipc-on-store-set:${key}, ${value}`);
    store.set(key, value);
    // logger.info(`store-set: ${JSON.stringify({ key: value })}`);
  });

  ipcMain.handle('store-get', (event, key) => {
    logger.info(`ipc-on-store-get:${key}`);
    const value = store.get(key);
    // logger.info(`store-get: ${JSON.stringify({ key: value })}`);
    return store.get(key);
  });

  ipcMain.on('store-delete', (event, key) => {
    logger.info(`ipc-on-store-delete:${key}`);
    store.delete(key);
    logger.info(`store-delete: ${key}`);
  });

  ipcMain.handle('get-version', (event, key) => {
    logger.info(`ipc-on-get-version`);
    return app.getVersion();
  });
  const onCheckUpdate = () => {
    if (updateMessage) return;
    autoUpdater.checkForUpdates();
    // 正在检查更新
    updateMessage = `${i18n.t('checking_for_updates')}...`;
    settingsWindow?.webContents?.send('update-message', updateMessage);
  };

  ipcMain.on('check-update', onCheckUpdate);

  if (launchUrl) {
    handleProtocol(launchUrl);
    launchUrl = null;
    logger.info(`store-handleProtocol: launchUrl`);
  }

  // 处理协议启动
  handleProtocol(process.argv.find((arg) => arg.startsWith('PLAUD://')));

  checkMicrophonePermission();

  ipcMain.on('change-token', async (event, data) => {
    logger.info(`change-token: ${data}`);
    if (data && contextMenu[6].key === 'sign_in') {
      contextMenu.splice(6, 1, signOutMenu);
    } else if (!data && contextMenu[6].key === 'sign_out') {
      contextMenu.splice(6, 1, loginMenu);
    }
    settingsWindow?.webContents?.send('change-token', data);
    mainWindow?.webContents?.send('change-token', data);
    logger.info(`meetingDetector?.getDetectStatus():${meetingDetector?.getDetectStatus()}`);
    if (!data && meetingDetector?.getDetectStatus()) {
      meetingDetector.stop();
      logger.info('login status change, auto record stop');
      // 通知UI更新状态
      mainWindow?.webContents.send('mic-process-activity', false);
    } else {
      // 如果自动录音开着，并且没在检测中，则开启
      if (data && store.get(AUTO_RECORD) && !meetingDetector?.getDetectStatus()) {
        meetingDetector.start();
        logger.info('login status change, auto record start');
      }
    }
  });

  ipcMain.handle('switch-to-device', async (event, data) => {
    logger.info(`switch-to-device: ${data}`);
    if (!data) {
      return;
    }
    return await audioSwitcher.switchToDevice(data);
  });

  ipcMain.on('default-device', async (event, data) => {
    logger.info(`default-device: ${data}`);
    if (!data) {
      return;
    }
    defaultDevice = data;
  });

  // ipcMain.handle('restore-audio-device', async () => {
  //   await audioSwitcher.restoreOriginalDevice();
  // });

  // ipcMain.handle('get-original-device', async () => {
  //   return await audioSwitcher.getOriginalDevice();
  // });

  ipcMain.on('set-icon', async (event, type) => {
    tray.setImage(getIcon(type));
    logger.info(`set-icon: ${type}`);
  });

  ipcMain.on('set-log', async (event, message) => {
    logger.info(`set-log: ${message}`);
  });

  // ipcMain.on('set-height', async (event, height) => {
  //   let [width] = mainWindow.getSize();
  //   // mainWindow.setSize(width, height);
  //   // logger.info(`set-height: ${height}`);
  //   // if (process.platform === 'win32') {
  //   //   showWindowAboveTray();
  //   // }
  // });

  // ipcMain.handle('list-audio-devices', async () => {
  //   return audioSwitcher.listDevices();
  // });

  // 检查开机启动状态
  // ipcMain.handle('check-auto-launch', async () => {
  //   checkAutoLaunch();
  // });

  ipcMain.handle('get-single-audio', async (event, data) => {
    try {
      const buffer = await readAsArrayBuffer(data.filePath);
      return buffer;
    } catch (e) {
      console.log(e);
    }
  });

  ipcMain.handle('get-audios', async () => {
    try {
      logger.info(`get-audios start`);
      const files = store.get(ERROR_FILES, {});
      const result = [];
      for await (const file of Object.values(files)) {
        console.log(file);
        if (!fs.existsSync(file.filePath)) {
          delete files[file.id];
          store.set(ERROR_FILES, files);
          continue;
        }

        // const buffer = await readAsArrayBuffer(file.filePath);
        result.push({ ...file });
      }

      logger.info(`get-audios end`);
      console.log(result, 'result222');
      return result;
    } catch (e) {
      console.log(e, 'eeeee');
    }
  });

  ipcMain.on('open-audio', async (event, data = {}) => {
    const { buffer, id, name } = data;
    logger.info(`open-audio start: ${JSON.stringify({ id, name })}`);
    let filePath = path.join(audioDir, `${id}.webm`);
    shell.showItemInFolder(filePath);
    logger.info(`open-audio end: ${JSON.stringify({ id, name })}`);
  });
  ipcMain.on('save-audio', async (event, data = {}) => {
    const { buffer, id, name } = data;
    logger.info(`save-audio start: ${JSON.stringify({ id, name })}`);
    if (!buffer || !id || !name) {
      logger.info(`save-audio error: ${JSON.stringify({ id, name })}`);
      return;
    }
    const filePath = path.join(audioDir, `${id}.webm`);
    const files = store.get(ERROR_FILES, {});
    if (files[id] && fs.existsSync(files[id].filePath)) {
      return;
    }

    fs.writeFileSync(filePath, Buffer.from(buffer));
    files[id] = { filePath, name, id };
    store.set(ERROR_FILES, files);
    logger.info(`save-audio end: ${JSON.stringify({ id, name })}`);
  });

  ipcMain.on('delete-audio', async (event, id) => {
    logger.info(`delete-audio start: ${id}`);
    const files = store.get(ERROR_FILES, {});
    let filePath = path.join(audioDir, `${id}.webm`);
    if (files[id]) {
      if (files[id].filePath) {
        filePath = files[id].filePath;
      }

      delete files[id];
      store.set(ERROR_FILES, files);
    }
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    logger.info(`delete-audio end: ${id}`);
  });

  // 切换开机启动状态
  ipcMain.handle('toggle-auto-launch', toggleAutoLaunch);
  ipcMain.handle('get-update-message', () => {
    return updateMessage;
  });

  ipcMain.on('send-notification', (event, args) => {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: args.title,
        body: args.body,
        silent: false,
      });
      notification.on('click', () => {
        shell.openExternal(HOST);
      });

      notification.show();
      logger.info(`send-notification`);
    } else {
      logger.info(`Notification Unupported`);
    }
  });

  let lng = store.get(LANGUAGE);
  if (!lng) {
    lng = app.getLocale().replace('-', '_');
    if (lng === 'zh_HK') {
      lng = 'zh_TW';
    }
    store.set(LANGUAGE, lng);
  }
  i18n.changeLanguage(lng);
  logger.info(`init Language: ${lng}`);

  // initTray();
  // 已经展示过onBoarding了，则直接展示主页面
  // console.log(onBoarding, 'onBoarding');
  if (onBoarding) {
    setTimeout(showWindowAboveTray, 500);
  }

  autoUpdater.checkForUpdates();
  // 每24小时天检查一次更新
  setInterval(
    () => {
      autoUpdater.checkForUpdates();
    },
    1000 * 60 * 60 * 24,
  );
  logger.info('auto checkForUpdates');
  if (!store.has(AUTO_LAUNCH)) {
    toggleAutoLaunch(null, true);
  }

  setTimeout(() => {
    installAudio();
  }, 0);
}

let onBoardingWindow = null;
async function createOnBoardingWindow() {
  logger.info('create onboadring window');
  onboardingWindow = new BrowserWindow({
    width: 800,
    height: 480,
    frame: true, //关闭原生导航栏
    show: false,
    skipTaskbar: true, // 不在任务栏显示
    transparent: isMac ? true : false,
    // 设置标题栏样式为隐藏，但保留系统按钮（交通灯）
    titleBarStyle: 'hidden',
    // 确保窗口背景是透明的
    backgroundColor: '#00000000', // 完全透明的背景色
    // 允许窗口内容覆盖标题栏区域
    titleBarOverlay: isMac
      ? true
      : {
          color: '#341710',
          symbolColor: 'white',
        },
    // macOS 特定的设置，确保交通灯按钮可见
    frame: true,
    fullscreenable: false, //不让用户全屏
    fullscreen: false, // 确保不以全屏启动
    // 可选：限制其他窗口操作
    maximizable: false, // 禁用最大化
    resizable: true, // 禁止调整窗口大小
    minimizable: false,
    // icon: path.join(__dirname, 'assets/images/Icon-white'), // 设置图标路径
    webPreferences: {
      nodeIntegration: true,
      webviewTag: true,
      webSecurity: false,
      contextIsolation: true,
      mediaAccessibility: true,
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  if (isDev) {
    onboardingWindow.loadURL(
      url.format({
        protocol: 'http:',
        host: `localhost:3692`,
        pathname: 'index.html',
        hash: '',
        slashes: true,
        query: {
          name: 'onBoarding',
        },
      }),
    );
    onboardingWindow.webContents.openDevTools();
  } else {
    onboardingWindow.loadFile(path.join(app.getAppPath(), 'dist', 'index.html'), {
      query: {
        name: 'onBoarding',
      },
    });
  }

  setTimeout(() => {
    onboardingWindow.show();
    onboardingWindow.focus();
  }, 1000);
  store.set(AUTO_LAUNCH, await checkAutoLaunch());
  onboardingWindow.on('closed', () => {
    logger.info('onboadring closed');
    onboardingWindow = null;
    store.set('on-boarding', 1);
    if (!helpWindow?.isDestroyed()) {
      mainWindow?.webContents?.send('onboarding-close');
    }
    setTimeout(() => {
      if (mainWindow) {
        showWindowAboveTray();
      }
    }, 1000);
  });
}

function clearOnboarding() {
  store.set('on-boarding', 0);
  exitInterception();
}

function clearOnboardingAndSigout() {
  store.set('on-boarding', 0);
  logoutInterception();
  exitInterception();
}

let confettiWindow;

function createConfettiWindow() {
  logger.info('create confetti window');
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;
  confettiWindow = new BrowserWindow({
    width: width,
    height: height,
    frame: false, // 无边框
    transparent: true, // 透明背景
    alwaysOnTop: true, // 置顶显示
    show: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
  });
  if (isDev) {
    confettiWindow.loadURL(
      url.format({
        protocol: 'http:',
        host: `localhost:3692`,
        pathname: 'confetti.htm',
        hash: '',
        slashes: true,
      }),
    );
    // confettiWindow.webContents.openDevTools();
  } else {
    confettiWindow.loadFile(path.join(app.getAppPath(), 'dist', 'confetti.htm'));
  }
  confettiWindow.on('closed', () => {
    logger.info('confettiWindow closed');
    confettiWindow = null;
  });
}

ipcMain.on('onboarding-finished', () => {
  logger.info('onboarding finished');
  onboardingWindow.hide();
  store.set('on-boarding', 1);
  if (!confettiWindow) {
    createConfettiWindow();
  }
  mainWindow?.webContents?.send('onboarding-close');
  setTimeout(() => {
    onboardingWindow = null;
    showWindowAboveTray();
  }, 3000);
});

//初始化的时候,需要获取下PPC状态
ipcMain.on('get-ppc', (event, data) => {
  tray.setImage(getIcon(`${data ? 'off' : 'ppc-off'}`));
  logger.info(`get ppc ${JSON.stringify(data)}`);
});

ipcMain.on('set-ppc', (event, data) => {
  logger.info(`set ppc ${JSON.stringify(data)}`);
  if (!data) {
    mainWindow?.webContents?.send('request-recordOrUpload-status-ppc');
  } else {
    mainWindow?.webContents?.send('ppc-change', data);
    settingsWindow?.webContents?.send('ppc-change', data);
    setTimeout(() => {
      tray.setImage(getIcon());
    }, 1000);
  }
});

function showMicPermissonDialog() {
  logger.info(`show mic permission dialog`);
  dialog
    .showMessageBox({
      type: 'warning',
      buttons: [i18n.t('open_system_setting'), i18n.t('not_record_audio')],
      defaultId: 0,
      cancelId: 1,
      message: i18n.t('mic_open_title'),
      detail: i18n.t('mic_open_tip'),
      icon,
    })
    .then((result) => {
      if (result.response === 0) {
        openSettings('', 'microphone');
      }
    });
}

ipcMain.on('request-media-permissions', () => {
  logger.info(`request media permissions`);
  showMicPermissonDialog();
});

ipcMain.on('send-recordOrUpload-status-ppc', (event, status) => {
  logger.info(`request media permissions ${JSON.stringify(status)}`);
  if (status) {
    dialog
      .showMessageBox({
        type: 'warning',
        buttons: [i18n.t('confirm')],
        defaultId: 0,
        cancelId: 0,
        title: i18n.t('close_ppc_tip'),
        message: i18n.t('signout_message'),
        icon,
      })
      .then((result) => {
        // ppcDialog = false;
      });
  } else {
    mainWindow?.webContents?.send('ppc-change', false);
    settingsWindow?.webContents?.send('ppc-change', false);
    setTimeout(() => {
      tray.setImage(getIcon('ppc-off'));
    }, 1000);
  }
});

const getRecordTime = throttle((event, data) => {
  logger.info(`get-record-time: ${JSON.stringify(data)}`);
  const dataArr = data.split('');
  if (dataArr.length > 0 && dataArr[0] === '0' && dataArr[1] === '0') {
    let title = '';
    title = dataArr.slice(3).join('');
    trayTitle = title;
    tray.setTitle(title, { fontType: 'monospacedDigit' });
    isRecordOrUpload = true;
  } else if (data) {
    trayTitle = data;
    tray.setTitle(data, { fontType: 'monospacedDigit' });
    isRecordOrUpload = true;
  } else if (latestVersion) {
    trayTitle = i18n.t('new_version');
    tray.setTitle(trayTitle, { fontType: 'monospacedDigit' });
    tray.setImage(getIcon('update'));
    isRecordOrUpload = false;
  } else {
    trayTitle = '';
    tray.setTitle(trayTitle, { fontType: 'monospacedDigit' });
    isRecordOrUpload = false;
  }
}, 800);
//初始化的时候,需要获取下PPC状态
ipcMain.on('get-record-time', getRecordTime);

ipcMain.on('on-update', (event, status) => {
  logger.info('on-update');
  try {
    trayTitle = '';
    tray.setTitle(trayTitle, { fontType: 'monospacedDigit' });
    autoUpdater.quitAndInstall(true, true);
  } catch (e) {
    logger.info('on-update-error' + JSON.stringify(e));
  }
});

ipcMain.on('on-update-confirm', (event, status) => {
  logger.info('on-update-confirm');
  dialog.showMessageBox({
    type: 'question',
    buttons: [i18n.t('confirm')],
    title: i18n.t('update_title'),
    message: i18n.t('update_tip_message'),
  });
});

ipcMain.on('show_dock', (event, data) => {
  logger.info('show dock');
  if (process.platform === 'darwin') {
    if (data) {
      app.dock.show();
      return;
    }
    app.dock.hide();
  }
});

const createHelpsWindow = () => {
  logger.info('create helps window');
  helpWindow = new BrowserWindow({
    width: isMac ? 710 : 725,
    height: 440,
    frame: true, //关闭原生导航栏
    show: false,
    // skipTaskbar: true, // 不在任务栏显示
    transparent: false, // 透明窗口
    fullscreenable: false, //不让用户全屏
    fullscreen: false, // 确保不以全屏启动
    // 可选：限制其他窗口操作
    maximizable: false, // 禁用最大化
    resizable: false, // 禁止调整窗口大小
    // icon: path.join(__dirname, 'assets/images/Icon-white'), // 设置图标路径
    webPreferences: {
      nodeIntegration: true,
      webviewTag: true,
      webSecurity: false,
      contextIsolation: true,
      mediaAccessibility: true,
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  if (isDev) {
    helpWindow.loadURL(
      url.format({
        protocol: 'http:',
        host: `localhost:3692`,
        pathname: 'help.htm',
        hash: '',
        slashes: true,
      }),
    );
    // helpWindow.webContents.openDevTools();
  } else {
    helpWindow.loadFile(path.join(app.getAppPath(), 'dist', 'help.htm'));
  }
};

ipcMain.on('open-help', (event, data) => {
  logger.info('open help');
  if (!helpWindow?.isDestroyed()) {
    helpWindow.show();
  } else {
    createHelpsWindow();
    helpWindow.show();
  }
});

// 监听文件日志压缩加密上传
ipcMain.on('start-zip', () => {
  logger.info('start zip');
  // console.log('start-zip is invoked');
  doZipAndUploadFiles(settingsWindow, userToken);
});

// 监听文件日志retry上传
ipcMain.on('start-upload', (event, path) => {
  logger.info('start upload');
  // console.log('start-upload is invoked');
  uploadFile(path, userToken);
});
// 获取用户信息userid，写入日志
ipcMain.on('user-info', (event, data) => {
  logger.info(`userid: ${data?.data_user?.id}`);
  user_info = data;
  if (user_info?.data_user?.email) {
    store.set(EMAIL, JSON.stringify(user_info?.data_user?.email));
  }
  if (user_info?.data_user?.id) {
    store.set(USERID, JSON.stringify(user_info?.data_user?.id));
  }
});

// 获取用户信息userToken
ipcMain.on('user-token', (event, data) => {
  logger.info('user token');
  userToken = data;
});

// 打开制定路径的文件夹
ipcMain.on('open-folder', (event, path) => {
  logger.info('open folder');
  if (!fs.existsSync(path)) {
    console.error('File does not exist:', path);
    return;
  }
  shell.showItemInFolder(path);
});
// 处理自定义协议
function handleProtocol(str, loaded) {
  logger.info(`enter handleProtocol, str: ${str}, loaded: ${loaded}`);
  if (!str) {
    return;
  }
  // 解析协议中的参数
  const parsedUrl = url.parse(str, true);
  const { token, auto, position } = parsedUrl.query;
  logger.info(`token, auto, token: ${str}, auto: ${loaded}, position: ${position}`);
  if (!token) {
    return;
  }
  // if (!mainWindow) {
  //   global.protocolData = {
  //     token,
  //     auto,
  //   };
  //   return;
  // }

  // 等待窗口加载完成后发送登录信息
  if (loaded && (position !== 'onboarding' || (position === 'onboarding' && !onboardingWindow))) {
    logger.info(`enter loaded token:${token}`);
    // store.set(TOKEN, token);
    showWindowAboveTray();
    store.set('on-boarding', 1);
    mainWindow?.webContents?.send('login-from-protocol', { auto, token });
  } else {
    logger.info(`enter no loaded`);
    if (position === 'onboarding' && onboardingWindow) {
      onboardingWindow.webContents.send('login-from-protocol', { auto, token, position });
      onboardingWindow.focus();
    } else {
      mainWindow?.webContents?.once('did-finish-load', () => {
        logger.info(`did-finish-load token:${token}`);
        // store.set(TOKEN, token);
        showWindowAboveTray();
        mainWindow?.webContents?.send('login-from-protocol', { auto, token });
      });
    }
  }
}

async function createSettingsWindow(query = {}) {
  logger.info('create setting window');
  // store.set(AUTO_LAUNCH, await checkAutoLaunch());
  // 如果设置窗口已经存在，则聚焦
  if (settingsWindow) {
    settingsWindow.focus();
    settingsWindow.show();
    if (query.type) {
      settingsWindow?.webContents?.send('switch-tab', query.type);
    }
    return;
  }

  // 创建新的设置窗口
  settingsWindow = new BrowserWindow({
    title: i18n.t('preference'),
    width: 720,
    height: 440,
    minWidth: 720,
    minHeight: 440,
    // parent: mainWindow, // 设置主窗口为父窗口
    center: true, // 居中显示
    resizable: true, // 禁止调整大小（可选）
    // minimizable: true, // 禁止最小化（可选）
    // modal: true, // 模态窗口（可选，锁定主窗口）
    frame: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
    },
  });
  if (isDev) {
    settingsWindow.webContents.openDevTools();
  }

  if (isDev) {
    settingsWindow.loadURL(
      url.format({
        protocol: 'http:',
        host: `localhost:3692`,
        pathname: 'index.html',
        hash: 'setting',
        slashes: true,
        query,
      }),
    );
    // mainWindow.loadFile(path.join(app.getAppPath(), 'dist', 'index.html'));
  } else {
    // settingsWindow.loadURL(
    //   url.format({
    //     protocol: 'http:',
    //     host: `localhost:3692`,
    //     pathname: 'index.html',
    //     hash: 'setting',
    //     slashes: true,
    //   }),
    // );

    settingsWindow.loadFile(path.join(app.getAppPath(), 'dist', 'index.html'), {
      hash: '#/setting',
      query,
    });
  }

  ipcMain.on('update-settings', (event, settings) => {
    logger.info(`update settings: ${settings}`);
    // 通知第一个窗口设置信息已更新
    mainWindow?.webContents?.send('settings-updated', settings);
    if (settings.type === 'USER_LANGUAGE') {
      setTimeout(() => {
        settingsWindow.setTitle(i18n.t('preference'));
      }, 100);
    }
  });

  ipcMain.on('close-preference', () => {
    logger.info(`close preference`);
    settingsWindow.close();
  });

  // 窗口关闭时清除引用
  settingsWindow.on('closed', () => {
    logger.info(`settings close`);
    settingsWindow = null;
  });
}

function logoutInterception() {
  logger.info(`logout interception`);
  mainWindow?.webContents?.send('request-recordOrUpload-status-login');
}

// 检查是否已设置开机启动
function checkAutoLaunchEnabled() {
  logger.info(`check auto luanch enable`);
  const loginSettings = app.getLoginItemSettings();
  return loginSettings.openAtLogin;
}

// 启用开机启动
function enableAutoLaunch() {
  logger.info(`enable auto launch`);
  if (process.platform === 'darwin' || process.platform === 'win32') {
    app.setLoginItemSettings({
      openAtLogin: true,
      // macOS 特定选项
      // openAsHidden: true, // 以隐藏方式启动
      // name: app.getName(),
      // path: process.platform === 'darwin' ? process.execPath : app.getPath('exe'), // 应用程序路径
    });
  }
}

// 禁用开机启动
function disableAutoLaunch() {
  logger.info(`disable auto launch`);
  if (process.platform === 'darwin' || process.platform === 'win32') {
    app.setLoginItemSettings({
      openAtLogin: false,
    });
  }
}

// 在主进程中设置 IPC 通信

function openSettings(event, type) {
  logger.info(`open settings：${type}`);
  const platform = os.platform();
  if (platform === 'win32') {
    if (type === 'notification') {
      shell.openExternal('ms-settings:notifications');
    }
    if (type === 'microphone') {
      shell.openExternal('ms-settings:privacy-microphone');
    }
    if (type === 'sound') {
      shell.openExternal('ms-settings:sound');
    }
  } else if (platform === 'darwin') {
    if (type === 'notification') {
      exec('open "x-apple.systempreferences:com.apple.preference.notifications"');
    }
    if (type === 'microphone') {
      exec('open "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone"');
    }
    if (type === 'sound') {
      exec('open "x-apple.systempreferences:com.apple.preference.sound"');
    }
  }
}

ipcMain.on('check-mic-permission', async (event, status) => {
  logger.info(`check mic permission`);
  console.log('check-mic-permission');
  checkMicrophonePermission();
});
let isCheckMicPermission = false;
// 检查麦克风权限
async function checkMicrophonePermission() {
  const status = await systemPreferences.getMediaAccessStatus('microphone');
  logger.info(`check mic permission: ${status}`);
  if (status === 'not-determined' || status === 'denied') {
    if (!isCheckMicPermission) {
      // 请求麦克风权限
      const granted = await systemPreferences.askForMediaAccess('microphone');
      isCheckMicPermission = true;
      return granted;
    } else {
      // showMicPermissonDialog();
      openSettings('', 'microphone');
    }
  }

  return status === 'granted';
}

async function isBlackHoleInstalled() {
  try {
    const output = await execPromise(`system_profiler SPAudioDataType | grep ${DEVICE_NAME}`);

    return output.includes(DEVICE_NAME);
  } catch {
    return false;
  }
}

async function installAudio() {
  if (process.platform != 'darwin' || (await isBlackHoleInstalled())) {
    return;
  }
  logger.info('installAudio');
  const installerPath = path.join(
    isDev ? path.join(__dirname, '../resources') : process.resourcesPath,
    `${DEVICE_NAME}.pkg`,
  );
  const options = { name: app.getName() };
  logger.info(installerPath);
  logger.info(app.getName());

  require('sudo-prompt').exec(
    `installer -pkg "${installerPath}" -target /`,
    options,
    (error, stdout, stderr) => {
      if (error) {
        logger.error(
          `installAudio error: ${typeof error === 'string' ? error : JSON.stringify(error)}`,
        );
        return;
      }
      logger.info('installAudio successfully');
    },
  );
}

function uninstallAudio() {
  logger.info('uninstallAudio');
  const options = { name: app.getName() };
  require('sudo-prompt').exec(
    'rm -rf /Library/Audio/Plug-Ins/HAL/BlackHole2ch.driver',
    options,
    (error, stdout, stderr) => {
      if (error) {
        console.error('Error uninstalling uninstallAudio:', error);
        return;
      }
      console.log('uninstallAudio uninstalled successfully!');
    },
  );
}

function getIcon(type = 'off') {
  console.log(type, 'getIcontype');
  logger.info(`get icon ${type}`);
  // 运行 AppleScript 获取菜单栏背景颜色

  // let theme = nativeTheme.shouldUseDarkColors ? 'dark' : 'light';
  // let theme = getMenuBarColor();
  // console.log(theme, 'theme');

  let path;

  // 检测PPC状态并设置图标
  if (type === 'ppc-off') {
    // PPC 没开启则展示PPC的图标
    if (process.platform === 'darwin') {
      path = __dirname + `/assets/images/Icon-ppcTemplate.png`;
    } else {
      path = __dirname + `/assets/images/Icon-ppc.ico`;
    }
  } else if (type === 'update') {
    if (process.platform === 'darwin') {
      // Mac会自动进行明暗颜色转换处理
      path = __dirname + `/assets/images/IconUpdateTemplate.png`;
    } else {
      path = __dirname + `/assets/images/Icon-${type}.ico`;
    }
  } else {
    if (process.platform === 'darwin') {
      // Mac会自动进行明暗颜色转换处理
      path = __dirname + `/assets/images/IconTemplate.png`;
    } else {
      path = __dirname + `/assets/images/Icon-${type}.ico`;
    }
  }
  return path;
}
const loginMenu = {
  key: 'sign_in',
  label: 'sign_in',
  click: () => {
    shell.openExternal(`${HOST}?from=desktop`);
  },
};

const signOutMenu = {
  key: 'sign_out',
  label: 'sign_out',
  click: () => {
    // store.delete(TOKEN);
    logoutInterception();
  },
};
// 获取当前操作系统
const isMac = process.platform === 'darwin';

const iconPath = path.join(__dirname, 'assets/images/icon-white.png'); // 替换为你的图标路径
const icon = nativeImage.createFromPath(iconPath);

let contextMenu = [
  {
    key: 'about',
    label: 'about',
    click: () => {
      createSettingsWindow({ type: 'About' });
    },
  },
  {
    key: 'check_update',
    label: 'check_update',
    click: () => {
      autoUpdater.checkForUpdates();
      logger.info('Manual checkForUpdates');
      createSettingsWindow({ type: 'About' });
    },
  },
  {
    key: 'preference',
    label: 'preference',
    accelerator: isMac ? 'Command+,' : 'Ctrl+,', // 快捷键提示
    click: () => {
      createSettingsWindow();
    },
  },
  { type: 'separator' }, // 分隔线
  {
    key: 'concat_us',
    label: 'concat_us',
    click: () => {
      createHelpsWindow();
      helpWindow.show();
    },
  },
  { type: 'separator' }, // 分隔线
  loginMenu,
  { type: 'separator' }, // 分隔线
  {
    key: 'quit_plaud',
    label: 'quit_plaud',
    accelerator: isMac ? 'Command+Q' : 'Ctrl+Q',
    click: async () => {
      exitInterception();
    },
  },
];

if (isDevOrTest) {
  contextMenu.push({
    key: 'clearOnboarding',
    label: 'clearOnboarding',
    click: async () => {
      clearOnboarding();
    },
  });
}

if (isDevOrTest) {
  contextMenu.push({
    key: 'clearOnboardingAndSigout',
    label: 'clearOnboardingAndSigout',
    click: async () => {
      clearOnboardingAndSigout();
    },
  });
}
// logoutInterception

ipcMain.on('send-recordOrUpload-status', async (event, status) => {
  logger.info(`send record or upload status: ${JSON.stringify(status)}`);
  if (status) {
    dialog
      .showMessageBox({
        type: 'warning',
        buttons: [i18n.t('confirm'), i18n.t('cancel')],
        defaultId: 1,
        cancelId: 1,
        title: i18n.t('exit_tip'),
        message: i18n.t('exit_message'),
        icon,
      })
      .then(async (result) => {
        if (result.response === 0) {
          execQuit();
        }
      });
  } else {
    execQuit();
  }
});

ipcMain.on('delete-upload-item', (event, task) => {
  logger.info(`delete upload item: ${JSON.stringify(task)}`);
  dialog
    .showMessageBox({
      type: 'question',
      title: i18n.t('delete_tip'),
      message: i18n.t('delete_message'),
      buttons: [i18n.t('confirm'), i18n.t('cancel')],
      defaultId: 1, // 默认选中“取消”
      cancelId: 1, // 点击 X 或取消视为“取消”
      icon,
    })
    .then((result) => {
      if (result.response === 0) {
        // 用户点击“确认”
        // 完成后通知渲染进程
        mainWindow?.webContents?.send('upload-item-deleted', task);
      }
    });
});

ipcMain.on('send-recordOrUpload-status-login', async (event, status) => {
  console.log(status, 'statusstatusstatus');
  logger.info(`send record or upload status login: ${JSON.stringify(status)}`);
  if (status) {
    dialog
      .showMessageBox({
        type: 'warning',
        buttons: [i18n.t('confirm')],
        defaultId: 0,
        cancelId: 0,
        title: i18n.t('signout_tip'),
        message: i18n.t('signout_message'),
        icon,
      })
      .then((result) => {});
  } else {
    execLogout();
  }
});

ipcMain.on('change-audio-setting', (event, data) => {
  logger.info(`change audio setting: ${JSON.stringify(data)}`);
  mainWindow?.webContents?.send('set-audio-setting', data);
});

// change-audio-record

ipcMain.on('change-audio-record', (event, data) => {
  logger.info(`change audio record: ${JSON.stringify(data)}`);
  settingsWindow?.webContents?.send('set-audio-record', data);
});

const execLogout = () => {
  logger.info(`exec logout`);
  contextMenu.splice(6, 1, loginMenu);
  if (!mainWindow.isDestroyed()) {
    mainWindow?.webContents?.send('sign-out-exec');
  }
  if (!settingsWindow.isDestroyed()) {
    settingsWindow?.webContents?.send('sign-out-exec');
  }
};

// 防止已经被关闭的窗口再触发
ipcMain.on('sign-out', (event, settings) => {
  logger.info(`sign out`);
  logoutInterception();
});

const execQuit = async () => {
  logger.info(`exit quit`);
  if (process.platform === 'darwin' && defaultDevice) {
    await audioSwitcher.switchToDevice(defaultDevice);
  }
  app.quit();
};

const exitInterception = () => {
  logger.info(`exit interception`);
  if (contextMenu[6].key === 'sign_in') {
    execQuit();
    return;
  }
  mainWindow?.webContents?.send('request-recordOrUpload-status');
};

// store.onDidChange(TOKEN, (newValue, oldValue) => {
//   if (!oldValue && newValue) {
//     contextMenu.splice(4, 1, signOutMenu);
//   } else if (oldValue && !newValue) {
//     contextMenu.splice(4, 1, loginMenu);
//   }
// });

store.onDidChange(LANGUAGE, (newValue, oldValue) => {
  logger.info(`on did change ${newValue}, ${oldValue}`);
  console.log(newValue, oldValue, 'Language');
  i18n.changeLanguage(newValue);
});

let menuInstance;
let isMenuOpen = false;

function initTray() {
  logger.info(`init Tray`);
  tray = new Tray(getIcon());

  tray.setToolTip('PLAUD.AI');
  // let count = 1;
  // setInterval(() => {
  //   count++;
  //   tray.setTitle(`88:88:88`);
  // }, 1000);
  // tray.setContextMenu(contextMenu);
  // 注册快捷键
  const preferencesShortcut = isMac ? 'Command+shift+,' : 'Ctrl+,';

  globalShortcut.register(preferencesShortcut, () => {
    if (isMac) {
      if (isMenuOpen) {
        tray.popUpContextMenu(null);
        setTimeout(() => {
          createSettingsWindow();
        }, 0);
      }
    } else {
      if (
        (mainWindow && mainWindow.isVisible()) ||
        isMenuOpen ||
        (settingsWindow && settingsWindow.isVisible())
      ) {
        createSettingsWindow();
      }
    }
  });
  const exitShortcut = isMac ? 'Command+shift+Q' : 'Ctrl+Q';
  globalShortcut.register(exitShortcut, () => {
    if (isMac) {
      if (isMenuOpen) {
        tray.popUpContextMenu(null);
        setTimeout(() => {
          exitInterception();
        }, 0);
      }
    } else {
      if (
        (mainWindow && mainWindow.isVisible()) ||
        isMenuOpen ||
        (settingsWindow && settingsWindow.isVisible())
      ) {
        exitInterception();
      }
    }
  });
  tray.on('right-click', () => {
    logger.info(`Tray right-click`);
    let onBoardingComplete = store.get('on-boarding');
    logger.info(`Tray right-click: ${JSON.stringify(onBoardingComplete)}`);
    if (!onBoardingComplete) {
      return;
    }
    contextMenu.forEach((menu) => {
      if (menu.key === 'preference') {
        menu.label = i18n.t(menu.key) + '...';
      } else {
        menu.label = i18n.t(menu.key);
      }
    });
    menuInstance = Menu.buildFromTemplate(contextMenu);
    // 监听菜单即将显示的事件
    menuInstance.on('menu-will-show', () => {
      isMenuOpen = true;
    });

    // 监听菜单即将关闭的事件
    menuInstance.on('menu-will-close', () => {
      isMenuOpen = false;
    });

    tray.popUpContextMenu(menuInstance);
  });
  tray.addListener('click', function () {
    let onBoardingComplete = store.get('on-boarding');
    if (!onBoardingComplete) {
      return;
    }
    // if (!mainWindow || mainWindow?.isDestroyed()) {
    //   mainWindow = createWindow();
    //   return;
    // }

    logger.info(`Tray click`);
    if (mainWindow?.isVisible()) {
      mainWindow.hide(); // 如果窗口可见，则隐藏
    } else {
      clickCount++;

      showWindowAboveTray(); // 否则显示窗口在托盘上方// 如果窗口不可见，则显示
      if (settingsWindow) {
        settingsWindow.show();
        settingsWindow.focus();
        // mainWindow.show();
        // mainWindow.focus();
      }
      if (clickCount % 3 === 2) {
        autoUpdater.checkForUpdates();
      }

      mainWindow?.webContents?.send('daily-activity');
    }
  });
}
let isInitializing = true;
// 点击非窗口的其他区域，则关闭此窗口
app.on('browser-window-blur', () => {
  logger.info(`browser-window-blur-entry`);
  if (isInitializing) {
    isInitializing = false;
    return;
  }
  logger.info(`browser-window-blur-execute`);
  if (mainWindow?.isVisible() && !settingsWindow && !confettiWindow) {
    mainWindow.hide(); // 如果窗口可见，则隐藏
  }
});

function showWindowAboveTray() {
  logger.info(`showWindowAboveTray`);
  // 获取托盘位置最近的显示器
  const trayBounds = tray.getBounds();
  const { x, y, width } = trayBounds;
  const display = screen.getDisplayNearestPoint({ x, y });
  // const scaleFactor = display.scaleFactor; // 获取显示器的缩放因子
  // 获取主窗口的当前大小
  // const panelWidth = mainWindow.getSize()[0] / scaleFactor;
  const panelWidth = mainWindow?.getSize()[0];
  const panelHeight = mainWindow?.getSize()[1];
  const windowBounds = mainWindow?.getBounds(); // 获取窗口尺寸
  const workArea = display.workArea;

  let newX;
  if (x + panelWidth <= workArea.x + workArea.width) {
    // 左侧有足够空间，左对齐
    newX = x;
  } else {
    // 左侧空间不足，右对齐
    newX = x + width - panelWidth;
  }
  // 判断托盘是否在屏幕底部（Windows & Linux 通常托盘在底部）
  if (y > workArea.height / 2) {
    newY = Math.round(y - panelHeight);
  } else {
    // 托盘在顶部（macOS 通常在顶部）
    newY = Math.round(y + 22);
  }

  mainWindow?.setPosition(Math.floor(newX), newY);
  mainWindow?.setBounds({
    width: windowBounds.width,
    height: windowBounds.height,
  });

  mainWindow?.show();
  mainWindow?.focus();

  if (process.platform === 'win32') {
    adjustWindowToScale();
  }
}

// 检查扬声器权限(macOS)
async function checkAudioPermission() {
  const status = await systemPreferences.getMediaAccessStatus('audio');
  if (status === 'not-determined') {
    const granted = await systemPreferences.askForMediaAccess('audio');
    return granted;
  }
  return status === 'granted';
}

async function checkAutoLaunch() {
  if (process.platform === 'darwin') {
    const reuslt = await autoLauncher.isEnabled();
    logger.info(`mac check-auto-launch: ${reuslt}`);
    return reuslt;
  }

  try {
    let result = checkAutoLaunchEnabled();
    logger.info(`check-auto-launch: ${reuslt}`);
    return result;
  } catch (err) {
    console.error('toggleAutoLaunch:', err);
    return 1;
  }
}

async function toggleAutoLaunch(event, enable) {
  logger.info(`toggleAutoLaunch start: ${enable}`);
  if (process.platform === 'darwin') {
    try {
      if (enable) {
        autoLauncher.enable();
      } else {
        autoLauncher.disable();
      }
      const result = await autoLauncher.isEnabled();
      logger.info(`toggleAutoLaunch end: ${result}`);
      store.set(AUTO_LAUNCH, result);
      return result;
    } catch (err) {
      logger.error(`toggleAutoLaunch: ${err}`);
      store.set(AUTO_LAUNCH, false);
      return false;
    }
  }

  try {
    if (enable) {
      enableAutoLaunch();
    } else {
      disableAutoLaunch();
    }
    const result = checkAutoLaunchEnabled();
    logger.info(`toggleAutoLaunch end: ${result}`);
    store.set(AUTO_LAUNCH, result);
    return result;
  } catch (err) {
    logger.error(`toggleAutoLaunch: ${err}`);
    store.set(AUTO_LAUNCH, false);
    return false;
  }
}
async function readAsArrayBuffer(filePath) {
  const buffer = await fs.promises.readFile(filePath); // 读取文件，返回 Node.js Buffer
  return buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength); // 转换为 ArrayBuffer
}

// 调整窗口和内容的缩放
function adjustWindowToScale() {
  // const primaryDisplay = screen.getPrimaryDisplay();
  // let scaleFactor = primaryDisplay.scaleFactor * 0.7;
  // scaleFactor = 1; // 最小缩放因子为 1, 最大缩放因子为 2
  // console.log('当前系统缩放因子:', scaleFactor);
  // logger.info(`当前系统缩放因子:: ${scaleFactor}`);
  // 动态调整窗口大小（可选）
  // const baseWidth = 280; // 设计时的基准宽度
  // const baseHeight = 300; // 设计时的基准高度
  mainWindow?.setSize(BASE_WIDTH, BASE_HEIGHT);

  // 调整渲染内容的缩放
  mainWindow?.webContents?.setZoomFactor(1);
  logger.info('adjustWindowToScale end');
}

function initMeetingDetector() {
  logger.info('initMeetingDetector');
  if (meetingDetector) {
    return;
  }

  meetingDetector = new MeetingDetector({
    checkInterval: process.platform === 'darwin' ? 4000 : 2000,
    logger: logger,
    onStatusChange: (isActive) => {
      logger.info(`Meeting status changed: ${isActive}`);
      mainWindow?.webContents.send('mic-process-activity', isActive);
    },
    store,
  });

  if (store.get(AUTO_RECORD)) {
    meetingDetector.start();
  }
}

store.onDidChange(AUTO_RECORD, (newValue, oldValue) => {
  if (!meetingDetector) {
    initMeetingDetector();
  }

  if (newValue) {
    meetingDetector.start();
    logger.info('Auto record enabled, meeting detector started');
  } else {
    meetingDetector.stop();
    logger.info('Auto record disabled, meeting detector stopped');
    // 通知UI更新状态
    mainWindow?.webContents.send('mic-process-activity', false);
  }
});

// 检查辅助功能权限
async function checkAccessibilityPermission() {
  if (process.platform !== 'darwin') {
    return true; // 非 macOS，无需权限
  }
  return systemPreferences.isTrustedAccessibilityClient(true);
}

ipcMain.on('open-auto-record', async (event, data) => {
  const accessiBility = await checkAccessibilityPermission();
  logger.info(`accessiBility: ${accessiBility}`);
  if (accessiBility) {
    settingsWindow?.webContents?.send('open-auto-record-switch', data);
  }
});
