const fs = require('fs');
const path = require('path');
const { app, ipcMain, Notification, shell } = require('electron');
const archiver = require('archiver');
const archiverZipEncrypted = require('archiver-zip-encrypted');
const axios = require('axios');
const { getFormattedTimestamp } = require('./utils');
const { i18n } = require('./locales/i18n');

// 注册 zip-encrypted 格式
archiver.registerFormat('zip-encrypted', archiverZipEncrypted);

// 指定日志目录路径
const logPath = path.join(app.getPath('userData'), 'logs');
// const outputPath = path.join(app.getPath('userData'), 'logsZip');
const outputPath = app.getPath('downloads');

let settingsWindow;
const limitSize = 500;
// 读取日志目录下的所有文件并添加到归档中
function addFilesToArchive(archive) {
  fs.readdir(logPath, (err, files) => {
    if (err) {
      throw err;
    }

    files.forEach((file) => {
      const filePath = path.join(logPath, file);
      const stats = fs.statSync(filePath);
      // console.log('stats!!!:', stats);
      if (stats.isFile()) {
        archive.file(filePath, { name: file });
      }
    });

    // 完成归档
    archive.finalize();
  });
}

// 上传文件到服务端
async function uploadFile(filePath, userToken) {
  // console.log('uploadFile-filePath:', filePath);
  // console.log('uploadFile-userToken:', userToken);
  // 获取文件信息
  const stats = fs.statSync(filePath);
  const fileSize = stats.size;
  if (fileSize > limitSize * 1024 * 1024) {
    // 发送超出限制信息到渲染进程
    settingsWindow?.webContents?.send('upload-progress', {
      type: 'error',
      data: {
        filePath,
        error: '文件大小超出500MB限制，无法上传',
      },
      message: '文件大小超出500MB限制，无法上传',
    });
  }
  console.log('fileSize&&:', fileSize);
  const formData = new FormData();
  const fileStream = fs.createReadStream(filePath);
  formData.append('file', fileStream);

  try {
    const response = await axios.post('https://great-giant.com/file/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'Content-Length': fs.statSync(filePath).size,
      },
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        // console.log(`上传进度: ${percentCompleted}%`);
        // 发送上传进度信息到渲染进程
        settingsWindow?.webContents?.send('upload-progress', {
          type: 'progress',
          value: percentCompleted,
          message: `上传进度: ${percentCompleted}%`,
        });
      },
    });

    // console.log('上传完成:', response.data);
    // 发送上传完成信息到渲染进程
    settingsWindow?.webContents?.send('upload-progress', {
      type: 'done',
      value: 100,
      message: '上传完成',
    });
    throw error;
  } catch (error) {
    // console.error('上传失败:', error);
    // 发送上传失败信息到渲染进程
    settingsWindow?.webContents?.send('upload-progress', {
      type: 'error',
      data: {
        filePath,
        error,
      },
      message: '上传失败',
    });
  }
}

// 压缩文件并上传到服务器
function doZipAndUploadFiles(customWindow, userToken) {
  settingsWindow = customWindow;
  // 确保输出目录存在，如果不存在则创建
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
  }

  // 生成压缩文件的名称
  const timestamp = getFormattedTimestamp();
  const zipFileName = `plaud_log_${timestamp}.zip`;
  const outputFilePath = path.join(outputPath, zipFileName);

  // 创建输出文件流
  const output = fs.createWriteStream(outputFilePath);

  // 创建 archiver 实例
  const archive = archiver('zip-encrypted', {
    zlib: { level: 9 }, // 设置压缩级别
    encryptionMethod: 'aes256', // 设置加密方法
    password: 'plaud2025', // 设置压缩文件密码
  });

  // 监听 archiver 的完成事件
  output.on('close', () => {
    // console.log(`压缩完成: ${archive.pointer()} 字节`);
    // console.log(`压缩文件已保存到: ${outputFilePath}`);
    // 发送完成事件到渲染进程
    settingsWindow?.webContents?.send('zip-progress', {
      type: 'done',
      value: 100,
      path: outputFilePath,
      message: `压缩完成: ${archive.pointer()} 字节`,
    });
    // 上传文件到服务端
    // uploadFile(outputFilePath, userToken);

    // 下载成功的通知
    // downloadAndNotify(outputFilePath);
  });

  // 监听 archiver 的错误事件
  archive.on('error', (err) => {
    console.log('archiver error:', err);
    throw err;
  });

  // 监听 archiver 的进度事件
  archive.on('progress', (progress) => {
    const { processedBytes, totalBytes } = progress.fs;
    const progressPercentage = Math.round((processedBytes / totalBytes) * 100);
    // console.log(`压缩进度: ${progressPercentage}%`);
    // 发送进度信息到渲染进程
    settingsWindow?.webContents?.send('zip-progress', {
      type: 'progress',
      value: progressPercentage,
      message: `压缩进度: ${progressPercentage}%`,
    });
  });

  // 将输出流管道到 archiver 实例
  archive.pipe(output);

  // 读取日志目录下的所有文件并添加到归档中
  addFilesToArchive(archive);
}

// 下载到系统 download 目录
function downloadAndNotify(path) {
  if (!fs.existsSync(path)) {
    console.error('File does not exist:', path);
    return;
  }
  let downloadNotify = notify({
    title: i18n.t('下载完成'),
    body: i18n.t('文件已成功下载到指定目录。'),
  });
  downloadNotify.show();
  downloadNotify.on('click', () => {
    shell.showItemInFolder(path);
  });
}
// 发送通知
function notify(options) {
  if (Notification.isSupported()) {
    return new Notification({
      title: '',
      body: '',
      silent: false,
      ...options,
    });
  }
}

module.exports = {
  doZipAndUploadFiles,
  uploadFile,
};
