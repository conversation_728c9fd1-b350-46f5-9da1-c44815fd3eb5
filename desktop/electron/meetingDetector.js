const { exec } = require('child_process');
const path = require('path');
const util = require('util');
const { throttle } = require('lodash');
const { APPS, DEFUALT_BROWSERS, AUTO_RECORD } = require('./constant');
const { app } = require('electron');

const execPromise = util.promisify(exec);

class MeetingDetector {
  constructor(options = {}) {
    this.options = {
      checkInterval: 3000,
      logger: console,
      onStatusChange: null,
      ...options,
    };

    this.isMac = process.platform === 'darwin';
    this.isActive = false;
    this.timer = null;
    this.customDetectionFunctions = {
      lark: {
        mac: async (detector) => {
          const logger = detector.options.logger;
          const ironProcessCmd = `ps -A -o comm | grep -i 'Lark Helper (Iron)'`;
          const { stdout: ironProcessOut } = await execPromise(ironProcessCmd).catch(() => ({
            stdout: '',
          }));
          if (ironProcessOut) {
            logger.info(`【会议检测-Mac】检测到Lark会议进程: Lark Helper (Iron)`);
            return true;
          }
          return false;
        },
        windows: null,
        store: {
          meetingNameCache: null,
        },
      },
      zoom: {
        mac: async (detector) => {
          const logger = detector.options.logger;
          const cptHostCmd = `ps -A -o comm | grep -i 'CptHost'`;
          const { stdout: cptHostOut } = await execPromise(cptHostCmd).catch(() => ({
            stdout: '',
          }));
          if (cptHostOut) {
            logger.info(`【会议检测-Mac】检测到Zoom会议进程: CptHost`);
            return true;
          }
          return false;
        },
        windows: null,
        store: {
          meetingNameCache: null,
        },
      },
      teams: {
        mac: async (detector) => {
          const logger = detector.options.logger;
          const windowTitleCmd = `osascript -e 'tell application "System Events" to get name of windows of process "Microsoft Teams"'`;
          const { stdout: windowTitleOut } = await execPromise(windowTitleCmd).catch(() => ({
            stdout: '',
          }));
          logger.info(`【会议检测-Mac】Teams窗口标题: ${windowTitleOut}`);
          const windowKeywords = ['会议', '会議', '會議', 'Meeting'];
          // 会议关键词列表
          const meetingKeywords = [
            '开会',
            'Meet',
            'ミーティング',
            '開會',
            '日历',
            'Calendar',
            'カレンダー',
            '行事曆',
            '聊天',
            'Chat',
            'チャット',
          ];
          // 按行分割输出（可能有多个窗口）
          const windowTitles = windowTitleOut.split('\n').filter((line) => line.trim());
          for (const title of windowTitles) {
            // 检查窗口标题是否符合三段式格式
            const parts = title.split('|').map((part) => part.trim());
            // 检查窗口标题是否包含会议关键词
            if (windowKeywords.some((keyword) => parts[0].includes(keyword))) {
              logger.info(`【会议检测-Mac】检测到Teams会议窗口标题: ${title}`);
              return true;
            }
            // 检查窗口标题是否符合三段式格式
            if (parts.length === 3) {
              // 检查第三段是否为 "Microsoft Teams"
              if (parts[2] === 'Microsoft Teams') {
                // 检查第二段是否符合 "Microsoft Teams, X" 格式
                const secondPart = parts[1];
                if (secondPart.startsWith('Microsoft Teams, ')) {
                  // 提取第二段中的关键词部分
                  const keyword = secondPart.substring('Microsoft Teams, '.length);
                  // 检查是否包含会议名称缓存
                  if (this.customDetectionFunctions.teams.store.meetingNameCache === keyword) {
                    return true;
                  }
                  // 检查是否包含会议关键词
                  if (meetingKeywords.includes(keyword)) {
                    this.customDetectionFunctions.teams.store.meetingNameCache = parts[0]; // 最稳定的标题呈现形式获取窗口标题
                    return true;
                  }
                }
              }
            }
          }
          logger.info(`【会议检测-Mac】未检测到Teams会议窗口标题`);
          this.customDetectionFunctions.teams.store.meetingNameCache = null; // 清空窗口标题缓存
          return false;
        },
        windows: null,
        store: {
          meetingNameCache: null, // 会议名称缓存，用于跨job检测，有时候窗口标题的呈现形式是“Microsoft Teams, 会议名称”
        },
      },
    };
  }

  // 启动检测
  start() {
    if (this.timer) {
      this.options.logger.info('【会议检测】检测已在运行，重新启动');
      this.stop();
    }

    this.options.logger.info('【会议检测】开始监测会议活动');
    this.checkActivity = throttle(this._checkActivity.bind(this), this.options.checkInterval);
    this.timer = setInterval(this.checkActivity, this.options.checkInterval);
    // 立即执行一次检测
    this.checkActivity();
    return this;
  }

  // 停止检测
  stop() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      this.options.logger.info('【会议检测】停止监测会议活动');
    }
    return this;
  }
  // 获取检测状态
  getDetectStatus() {
    return this.timer;
  }

  // 获取当前会议状态
  getStatus() {
    return this.isActive;
  }

  // 核心检测逻辑
  async _checkActivity() {
    try {
      this.options.logger.info('【会议检测】开始检测周期...');

      // 根据平台检测会议状态
      const activeMeeting = this.isMac
        ? await this._checkMacActivity()
        : await this._checkWindowsActivity();

      // 检查状态是否发生变化
      const statusChanged = activeMeeting !== this.isActive;
      this.isActive = activeMeeting;

      const statusMessage = this.isActive ? '检测到会议进行中 ✓' : '未检测到会议活动 ✗';
      const logPrefix = statusChanged ? '状态变化 => ' : '状态未变：';
      this.options.logger.info(`【会议检测】${logPrefix}${statusMessage}`);

      // 触发回调的条件：状态变化 或 会议正在进行中
      const shouldTriggerCallback = statusChanged || this.isActive;

      if (shouldTriggerCallback && typeof this.options.onStatusChange === 'function') {
        this.options.onStatusChange(this.isActive);
      }

      return this.isActive;
    } catch (error) {
      this.options.logger.error(`【会议检测】检测出错：${error}`);
      return false;
    }
  }

  // macOS 检测逻辑
  async _checkMacActivity() {
    try {
      this.options.logger.info('【会议检测-Mac】检测到音频活动，正在检查会议进程');
      const apps = this.options.store.get(APPS)?.filter((app) => {
        return app.mac && app.enabled;
      });
      this.options.logger.info(`checkMacActivity apps: ${JSON.stringify(apps)}`);
      if (!apps || apps?.length === 0) {
        this.options.logger.info(`未开启任何APP`);
        return false;
      }

      // 检查会议软件进程
      for (const app of apps) {
        const processPattern = app.macProcessNames.join('|');
        this.options.logger.info(
          `【会议检测-Mac】检查应用: ${app.name}, 进程模式: ${processPattern}`,
        );
        const psCmd = `ps -A -o comm | grep -iE '${processPattern}'`;
        const { stdout: psOut } = await execPromise(psCmd).catch(() => ({ stdout: '' }));
        if (psOut) {
          this.options.logger.info(`【会议检测-Mac】发现进程: ${app.name}`);

          // 优先执行自定义检测
          const customDetection = this.customDetectionFunctions[app.key]?.mac;
          if (typeof customDetection === 'function') {
            this.options.logger.info(`【会议检测-Mac】执行自定义检测`);
            const customResult = await customDetection(this);
            if (customResult) {
              this.options.logger.info(`【会议检测-Mac】通过自定义检测确认 ${app.name} 会议活动`);
              return true;
            }
          }
        }
      }

      this.options.logger.info('【会议检测-Mac】未匹配到会议应用');
      return false;
    } catch (error) {
      this.options.logger.error(`【会议检测-Mac】检测出错: ${error}`);
      return false;
    }
  }

  // Windows 检测逻辑
  async _checkWindowsActivity() {
    let stdout;
    try {
      // 检查音频设备状态
      // const autoRecordEnabled = this.options.store.get(AUTO_RECORD);
      // if (!autoRecordEnabled) {
      //   // this.options.onStatusChange(false);
      //   return false;
      // }
      let apps = this.options.store.get(APPS);
      if (!apps || apps?.length === 0) {
        this.options.logger.info(`未开启任何APP`);
        return false;
      }
      // this.options.logger.info(`【会议检测】已加载默认应用：${JSON.stringify(apps)}`);
      if (apps.some((item) => item.key === 'browser' && item.enabled)) {
        apps = apps.concat(DEFUALT_BROWSERS);
      }
      // let soundPath = path.join(process.resourcesPath, 'SoundVolumeView.exe');
      let soundPath = path.join(app.getPath('userData'), 'SoundVolumeView.exe');
      if (process.env.NODE_ENV === 'development') {
        soundPath = path.join(__dirname, '../resources', 'SoundVolumeView.exe');
      }

      stdout = await execPromise(`${soundPath} /stab ""`, { encoding: 'utf16le' });
      const lines = stdout.stdout.split('\r\n').filter((line) => line.trim());
      const headers = lines[0].split('\t'); // 第一行是表头
      const pidIndex = headers.indexOf('Process ID');
      const stateIndex = headers.indexOf('Device State');
      const directionIndex = headers.indexOf('Direction');
      const micProcesses = [];
      let tempData = {};
      lines
        .slice(1) // 跳过表头
        .map((line) => {
          const parts = line.split('\t');
          const pid = parseInt(parts[pidIndex]);
          const name = parts[0]?.toLowerCase() || '';
          const direction = parts[directionIndex];
          if (
            !isNaN(pid) &&
            parts[stateIndex] === 'Active' &&
            direction === 'Capture' &&
            !micProcesses.includes(pid) &&
            apps.some((app) => app.enabled && name.includes(app.win))
          ) {
            micProcesses.push(pid);
            tempData[pid] = name;
            this.options.logger.info(`checkMicProcessActivity: ${JSON.stringify(tempData)}`);
          }
        });
      return micProcesses.length > 0;
    } catch (error) {
      this.options.logger.error(`【会议检测-Win】检测出错: ${JSON.stringify(error)}`);
      this.options.logger.error(`【会议检测-Win】检测出错: ${JSON.stringify(error.stderr)}`);
      this.options.logger.error(`【会议检测-Win】检测出错: ${JSON.stringify(stdout)}`);
      return false;
    }
  }
}

module.exports = MeetingDetector;
