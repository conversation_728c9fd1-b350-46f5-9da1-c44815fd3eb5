const { i18n } = require('./locales/i18n');

const DEVICE_NAME = 'PLAUDAudio';

const TOKEN = 'USER_TOKEN';

const LANGUAGE = 'USER_LANGUAGE';

const NOTIFY = 'NOTIFY';

const AUTO_LAUNCH = 'AUTO_LAUNCH';

const ERROR_FILES = 'ERROR_FILES';
const APPS = 'APPS';
const EMAIL = 'EMAIL';
const USERID = 'USER_ID';

const DEFUALT_BROWSERS = [
  { key: 'chrome', win: 'chrome', mac: 'google chrome helper', enabled: true },
  { key: 'edge', win: 'edge', mac: 'microsoft edge helper', enabled: true },
  // TODO:
  { key: 'firefox', win: 'firefox', mac: 'firefox', enabled: true },
  { key: 'safari', win: 'safari', mac: 'com.apple.webkit.gpu', enabled: true },
  { key: 'opera', win: 'opera', mac: 'opera helper', enabled: true },
  { key: 'arc', win: 'arc', mac: 'browser helper', enabled: true },
];

const DEFUALT_APPS = [
  {
    name: 'Google Meet / Zoom / Microsoft Teams in Browser',
    key: 'browser',
    win: 'browser',
    // mac: 'browser',
    icon: 'web',
    enabled: true,
    desc:
      process.platform === 'darwin' ? i18n.t('browser_support_mac') : i18n.t('browser_support_win'),
  },
  {
    name: 'Zoom Desktop',
    key: 'zoom',
    win: 'zoom',
    mac: 'zoom',
    icon: 'zoom',
    enabled: true,
    processNames: ['zoom.us', 'zoomus', 'cpthost'],
    macProcessNames: ['zoom.us', 'CptHost'],
  },
  {
    name: 'Microsoft Teams Desktop',
    key: 'teams',
    win: 'teams',
    mac: 'msteams',
    // mac: 'microsoft teams modulehost',
    icon: 'teams',
    enabled: true,
    processNames: ['teams.exe', 'microsoft teams'],
    macProcessNames: ['Microsoft Teams', 'Teams'],
  },
  {
    name: 'Slack Huddles',
    key: 'slack',
    win: 'slack',
    // mac: 'slack helper',
    icon: 'slack',
    enabled: true,
  },
  {
    name: 'Cisco Webex Desktop',
    key: 'webex',
    win: 'webex',
    // mac: 'webexhelper',
    icon: 'webex',
    enabled: true,
  },
  {
    name: 'Lark Desktop',
    key: 'lark',
    win: 'lark',
    mac: 'lark',
    icon: 'lark',
    enabled: true,
    processNames: ['lark'],
    macProcessNames: ['lark'],
  },
  { name: '', key: 'feishu', win: 'feishu', icon: '', enabled: true },
];

const BASE_WIDTH =
  process.env.NODE_ENV === 'development' ? 1000 : process.platform === 'darwin' ? 280 : 280;

const BASE_HEIGHT = 600;
const AUTO_RECORD = 'AUTO_RECORD';

module.exports = {
  DEVICE_NAME,
  TOKEN,
  LANGUAGE,
  ERROR_FILES,
  AUTO_LAUNCH,
  BASE_WIDTH,
  BASE_HEIGHT,
  APPS,
  DEFUALT_APPS,
  AUTO_RECORD,
  DEFUALT_BROWSERS,
  EMAIL,
  USERID,
};
