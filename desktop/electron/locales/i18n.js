const i18n = require('i18next');
const backend = require('i18next-fs-backend');

// On Mac, the folder for resources isn't
// in the same directory as Linux/Windows);
// https://www.electron.build/configuration/contents#extrafiles
const path = require('path');
i18n.use(backend).init({
  backend: {
    loadPath: path.join(__dirname, 'json/{{lng}}.json'),
  },
  debug: false,
  lng: 'en_US',
  fallbackLng: 'en_US',
});

module.exports = { i18n };
