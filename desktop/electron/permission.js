const { app, systemPreferences, dialog } = require('electron');

class SystemAudioPermissionManager {
  constructor() {}

  // 主动触发系统音频权限请求
  async requestSystemAudioAccess() {
    if (process.platform !== 'darwin') return true;

    try {
      const { desktopCapturer } = require('electron');

      // 先显示提示对话框
      const { response } = await dialog.showMessageBox({
        type: 'info',
        title: '需要系统录音权限',
        message:
          '为了录制系统声音，我们需要您授予录音权限。\n接下来会打开系统设置，请点击"录屏"，然后在列表中勾选本应用。',
        buttons: ['好的', '取消'],
        defaultId: 0,
        cancelId: 1,
      });

      if (response === 0) {
        // 触发系统权限对话框
        await desktopCapturer.getSources({
          types: ['screen'],
          thumbnailSize: { width: 1, height: 1 },
          fetchWindowIcons: true,
        });

        // 打开系统偏好设置的安全性与隐私面板
        await systemPreferences.openSystemPreferences('security', 'Privacy_ScreenCapture');
        return true;
      }

      return false;
    } catch (error) {
      console.error('请求系统音频权限失败:', error);
      return false;
    }
  }

  // 检查系统音频权限状态
  checkSystemAudioAccess() {
    if (process.platform !== 'darwin') return true;

    try {
      // macOS 10.15+ 使用新API
      if (systemPreferences.getMediaAccessStatus) {
        const status = systemPreferences.getMediaAccessStatus('screen');
        return status === 'granted';
      }
      return true;
    } catch (error) {
      console.error('检查系统音频权限失败:', error);
      return false;
    }
  }
}
module.exports = { SystemAudioPermissionManager };
