const path = require('path');
const { execFile } = require('child_process');
const util = require('util');
const execFileAsync = util.promisify(execFile);
const { DEVICE_NAME } = require('./constant');

class AudioDeviceSwitcher {
  constructor({ logger }) {
    this.logger = logger;
    this.originalDevice;
    let audioPath = path.join(process.resourcesPath, 'SwitchAudioSource');
    if (process.env.NODE_ENV === 'development') {
      audioPath = path.join(__dirname, '../resources', 'SwitchAudioSource');
    }
    this.switchAudioPath = audioPath;
    // this.switchAudioPath = '/usr/local/bin/SwitchAudioSource';

    // 重置一遍默认设备
    // this.switchToDefaultDevice();
  }

  // 获取当前音频设备
  async getCurrentDevice() {
    try {
      const { stdout } = await execFileAsync(this.switchAudioPath, ['-t', 'output', '-c']);
      const result = stdout.trim();
      this.logger.info(`getCurrentDevice: ${result}`);
      return stdout.trim();
    } catch (error) {
      this.logger.error('获取当前音频设备失败:', error);
      throw error;
    }
  }

  // // 获取原音频设备
  // getOriginalDevice() {
  //   try {
  //     return this.originalDevice;
  //   } catch (error) {
  //     this.logger.error('getOriginalDevice:', error);
  //     throw error;
  //   }
  // }

  // 切换到指定设备
  async switchToDevice(deviceName) {
    if (!deviceName) {
      return;
    }
    try {
      await execFileAsync(this.switchAudioPath, ['-t', 'output', '-s', deviceName]);
      this.logger.info(`switchToDevice successfully: ${deviceName}`);
    } catch (error) {
      this.logger.error(`switchToDevice失败: ${error}`);
      throw error;
    }
  }

  // 重置设备为默认设备
  async switchToDefaultDevice() {
    try {
      const currentDevice = await this.getCurrentDevice();
      if (currentDevice === DEVICE_NAME) {
        const list = await this.listDevices();

        list.some(async (device) => {
          // 默认取第一个不是 虚拟设备 的设备
          if (device != DEVICE_NAME && !device.includes('BlackHole')) {
            // this.originalDevice = device;
            await this.switchToDevice(device);

            return true;
          }
          return false;
        });
        // console.log(this.originalDevice, list, '111111111111111111111111111');
        // if (this.originalDevice != DEVICE_NAME) {
        //   await this.switchToDevice(this.originalDevice);
        //   this.logger.info(`switchToDefaultDevice: ${this.originalDevice}`);
        // }
      }
      // else if (currentDevice) {
      //   this.originalDevice = currentDevice;
      // }
    } catch (error) {
      this.logger.error(`switchToDefaultDevice: ${error}`);
      throw error;
    }
  }

  // 切换到 虚拟设备
  // async switchToVirtualDevice() {
  //   try {
  //     // 保存当前设备以便后续恢复
  //     // this.originalDevice = await this.getCurrentDevice();
  //     await this.switchToDevice(DEVICE_NAME);
  //     this.logger.info(`switchToVirtualDevice: originalDevice is ${this.originalDevice}`);
  //   } catch (error) {
  //     this.logger.error(`切换到 虚拟设备 失败: ${error}`);
  //     throw error;
  //   }
  // }

  // 恢复到原始设备
  // async restoreOriginalDevice() {
  //   try {
  //     if (!this.originalDevice) {
  //       return;
  //     }
  //     if (this.originalDevice === DEVICE_NAME) {
  //       this.switchToDefaultDevice();
  //       return;
  //     }
  //     await this.switchToDevice(this.originalDevice);
  //     this.logger.info('已恢复到原始设备:', this.originalDevice);
  //   } catch (error) {
  //     this.logger.error(`恢复原始设备失败: ${error}`);
  //     throw error;
  //   }
  // }

  // 列出所有可用的音频设备
  async listDevices() {
    try {
      const { stdout } = await execFileAsync(this.switchAudioPath, ['-t', 'output', '-a']);
      return stdout.split('\n').filter(Boolean);
    } catch (error) {
      this.logger.error(`获取设备列表失败: ${error}`);
      throw error;
    }
  }
}

module.exports = {
  AudioDeviceSwitcher,
};
