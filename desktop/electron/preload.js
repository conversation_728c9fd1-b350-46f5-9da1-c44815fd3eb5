const { contextBridge, shell, ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');

contextBridge.exposeInMainWorld('__E__', {
  isMac: process.platform === 'darwin',
  env: process.env.NODE_ENV,
  fs,
  shell,
  ipcRenderer: {
    send(event, ...args) {
      ipcRenderer.send(event, ...args);
    },
    on(event, callback) {
      ipcRenderer.on(event, (evt, ...args) => {
        callback(evt, ...args);
      });
    },
    invoke(event, ...args) {
      return ipcRenderer.invoke(event, ...args);
    },
    removeAllListeners(event) {
      ipcRenderer.removeAllListeners(event);
    },
    remove(event, callback) {
      ipcRenderer.removeListener(event, callback);
    },
  },
  store: {
    set: (key, value) => ipcRenderer.send('store-set', { key, value }),
    get: (key) => ipcRenderer.invoke('store-get', key),
    delete: (key) => ipcRenderer.send('store-delete', key),
  },
});
