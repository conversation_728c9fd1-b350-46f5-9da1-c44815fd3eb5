<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, toRaw } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import fromatDate from '@/utils/format-date';
import { PopoverContent, PopoverPortal, PopoverRoot, PopoverTrigger } from 'radix-vue';
import { DEFAULT_BITS_PER_SECOND, DEVICE_NAME } from '@/constants/audio';
import { NOTIFY, RECORD_TIME } from '@/constants/user';
import { debounce, throttle } from 'lodash-es';
import UploadQueue from '@/utils/upload-queue';
import { getDevices, requestMediaPermissions } from '@/utils/devices';
import Progress from './progress.vue';
import formatTime from '@/utils/format-time';
import AudioDetector from '@/utils/audio-detector2';
import { inputConfig, outputConfig } from './config';
import { useRoute } from 'vue-router';
import { emitter } from '@/utils/event-bus';
import { i18n } from '@/locales/i18n';
const { t } = i18n.global;
import { log } from '@/utils/firebase';
import { UPLOAD_HISTORY, TOKEN } from '@/constants/user';
import { fixWebMHeader } from '@/utils/convert';
const isMac = window.__E__?.isMac;
const route = useRoute();
let name = '';
let startTime = 0;
let totalTime = 0;
let timer: any = null;
let audio: any;
let isSwitching = false;
let isStartOrStopHover = ref(false);
let defaultOutputDeviceName = '';
let isPPCopened = ref(false);
let uploadHistory = ref([]);
isPPCopened.value =
  localStorage.getItem('ON_PPC') === 'false' || localStorage.getItem('ON_PPC') === null
    ? false
    : true;

let opend: any = ref(false);
let inputVolume = ref(0);
let outputVolume = ref(0);
let audioContext: any;
let mixedStream;
let date = new Date().getTime();
let inputDetector = new AudioDetector({
  fftSize: 2048,
  smoothingTimeConstant: 0.8,
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true,
});

let outputDetector = new AudioDetector({
  fftSize: 2048,
  smoothingTimeConstant: 0.8,
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true,
});

let isRecordClicked = ref(false);
const emit = defineEmits(['record']);

const showInDock = !(localStorage.getItem('SHOWDOCK') === '0');
window.__E__?.ipcRenderer.send('show_dock', showInDock);

const autoCount = ref(0);

const startInputStream = async (type) => {
  try {
    if (inputDeviceId.value) {
      inputStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          deviceId: { exact: inputDeviceId.value },
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          channelCount: 1,
          sampleRate: 48000,
          sampleSize: 16,
        },
      });
      inputDetector.init(inputStream);
      inputVolume.value = 0;
      inputDetector.startVolumeMonitor((data) => {
        inputVolume.value = data;
      });
    }
  } catch (e) {
    console.error('startInputStream error:', e);
    if (e?.message?.includes('Permission')) {
      if (type === 'auto' && autoCount.value === 0) {
        window.__E__?.ipcRenderer.send('request-media-permissions');
        autoCount.value++;
      }

      if (!(type === 'auto')) {
        window.__E__?.ipcRenderer.send('request-media-permissions');
      }
    }
  }
};

const startOutputStream = async () => {
  try {
    if (outputChecked.value) {
      let constraints: any = {
        audio: {
          mandatory: {
            chromeMediaSource: 'desktop',
          },
        },
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
          },
        },
      };
      if (isMac) {
        isSwitching = true;
        try {
          await window.__E__?.ipcRenderer.invoke('switch-to-device', DEVICE_NAME);
        } catch (e) {
        } finally {
          setTimeout(() => {
            isSwitching = false;
          }, 1000);
        }

        constraints = {
          audio: {
            deviceId: { exact: virtualSpeaker.deviceId },
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            channelCount: 1,
            sampleRate: 48000,
            sampleSize: 16,
          },
        };
      }

      outputStream = await navigator.mediaDevices.getUserMedia(constraints);

      if (isMac) {
        audioElement.srcObject = outputStream;
        audioElement.setSinkId(defaultOutputDeviceId);
      }

      outputDetector.init(outputStream);
      outputVolume.value = 0;
      outputDetector.startVolumeMonitor((data) => {
        outputVolume.value = data;
      });
    }
  } catch (e) {
    console.error('startStream:', e);
  }
};

let tasks: any = ref([]);

const onProgress = (task: any) => {
  tasks.value.some((item) => {
    if (item.id === task.id) {
      item.progress = task.progress;
      return true;
    }
    return false;
  });
};

const onSendNotification = debounce(() => {
  window.__E__?.ipcRenderer.send('send-notification', {
    title: t('success_title'),
    body: t('success_desc'),
  });
}, 5000);
window.__E__?.store.get(UPLOAD_HISTORY).then((res) => {
  uploadHistory.value = res || [];
});
const onComplete = async (task: any) => {
  tasks.value = tasks.value.filter((item: any) => item.id !== task.id);
  if (task.status === 'completed') {
    if (task.retry) {
      window.__E__?.ipcRenderer.send('delete-audio', task.id);
    }
    if (localStorage.getItem(NOTIFY) !== '0') {
      onSendNotification();
    }
  }
  log({ type: 'upload_success' });
  if (task.status === 'completed') {
    if (uploadHistory.value.length === 10) {
      uploadHistory.value.pop();
      uploadHistory.value.unshift(task.name);
    } else {
      uploadHistory.value.unshift(task.name);
    }
    window.__E__?.store.set(UPLOAD_HISTORY, toRaw(uploadHistory.value));
  }
};

const onError = (task: any, error) => {
  if (task.status != 'failed') {
    return;
  }

  // 更新任务状态
  tasks.value.some((item) => {
    if (item.id === task.id) {
      item.status = task.status;
      window.__E__?.ipcRenderer.send('save-audio', {
        id: item.id,
        name: item.name,
        buffer: item.buffer,
      });
      return true;
    }
    return false;
  });
  log({ type: 'upload_fail' });
};

let uploadQueue: any;

let devices = ref<any>({ output: [], input: [] });

let inputDeviceId = ref('');
let outputChecked = ref(true);

// 录音状态： 0 未授权 1 已授权 2 录音中 3  暂停
let status = ref(0);
let virtualSpeaker: any;
let defaultOutputDeviceId = '';

const onStartRecord = () => {
  if (!isPPCopened.value) {
    isRecordClicked.value = true;
    emit('record', isRecordClicked.value);
    // 添加一个延时，在动画结束后移除类名
    setTimeout(() => {
      isRecordClicked.value = false;
      emit('record', isRecordClicked.value);
    }, 300); // 300ms后移除类名，与动画时长一致
    return;
  }
  onStart();
  log({ type: 'record_manual' });
};

const onInputSelect = async (audio: any = {}) => {
  if (inputDeviceId.value === audio.deviceId) {
    return;
  }

  window.__E__?.ipcRenderer.send('change-audio-record', { ...audio });
  console.log(status.value, 'status.value');
  if (status.value === 1) {
    inputDeviceId.value = audio.deviceId;
    // stopInputStream();
    if (audio.deviceId) {
      // startInputStream();
    }
  }
  if ((status.value === 2 || status.value === 3) && (audio.deviceId || outputChecked.value)) {
    inputDeviceId.value = audio.deviceId;
    // 减去100 ms误差
    clearInterval(timer);
    timer = null;

    totalTime = totalTime + +new Date() - startTime - 100;
    stopRecording();
    stopInputStream();

    if (inputDeviceId.value) {
      await startInputStream();
    }

    onStart(true);
  }
  localStorage.setItem('DEFAULT_DEVICE', audio.deviceId || '');
  opend.value = false;
};

const onOutputCheck = async () => {
  if (status.value === 0) {
    return;
  }

  if (status.value === 1) {
    outputChecked.value = !outputChecked.value;
    if (outputChecked.value) {
      // startOutputStream();
    } else {
      // stopOutputStream();
    }
  }

  if ((status.value === 2 || status.value === 3) && inputDeviceId.value) {
    outputChecked.value = !outputChecked.value;
    // 减去100 ms误差
    clearInterval(timer);
    timer = null;
    totalTime = totalTime + +new Date() - startTime - 100;
    stopRecording();
    if (outputChecked.value) {
      await startOutputStream();
    } else {
      stopOutputStream();
    }
    onStart(true);
  }
};

let mediaRecorder: any;

function stopRecording() {
  window.__E__?.ipcRenderer.send('set-log', `stopRecording`);
  window.__E__?.ipcRenderer.send(
    'set-log',
    `stopRecording-mediaRecorder.state: ${mediaRecorder?.state}`,
  );
  window.__E__?.ipcRenderer.send('set-log', `stopRecording-timer: ${timer}`);
  if (mediaRecorder?.state === 'recording') {
    mediaRecorder?.stop();
  }
}

function stopInputStream() {
  if (!inputStream) {
    return;
  }
  inputStream.getTracks().forEach((track) => track.stop());
  inputStream = null;

  inputDetector.dispose();
  inputVolume.value = 0;
}

function stopOutputStream() {
  if (!outputStream) {
    return;
  }
  outputStream.getTracks().forEach((track) => track.stop());
  outputStream = null;
  if (isMac) {
    audioElement.pause();

    isSwitching = true;
    try {
      if (isMac && defaultOutputDeviceName) {
        window.__E__?.ipcRenderer.invoke('switch-to-device', defaultOutputDeviceName);
      }
    } catch (e) {
    } finally {
      setTimeout(() => {
        isSwitching = false;
      }, 1000);
    }
  }
  outputDetector.dispose();
  outputVolume.value = 0;
}

let inputStream: any;
let outputStream: any;
let recordedChunks: Blob[] = [];
let recordedTime = ref('00:00:00');
let audioElement: any;

if (isMac) {
  audioElement = document.createElement('audio');
  audioElement.setAttribute('autoplay', 'autoplay');
  // audioElement.volume = 1;
}

async function onStartTiming() {
  window.__E__?.ipcRenderer.send('set-log', `onstartTiming: ${timer}`);
  recordedTime.value = formatTime('hh:mm:ss', totalTime + +new Date() - startTime);
  window.__E__.ipcRenderer.send('get-record-time', recordedTime.value);
  // 大于4个半小时,先上传已经录的，再继续开始录 4.5 * 60 * 60 * 1000 + 500
  // if (totalTime + +new Date() - startTime > 10000) {
  if (totalTime + +new Date() - startTime > 18000600) {
    // onStop();
    clearInterval(timer);
    timer = null;
    mediaRecorder.stop();
    ouUpload(recordedChunks.slice(), name);
    recordedTime.value = '00:00:00';
    window.__E__.ipcRenderer.send('get-record-time', recordedTime.value);
    totalTime = 0;

    setTimeout(() => {
      name = fromatDate('yyyy-MM-dd hh:mm:ss', +new Date());
      recordedChunks = [];
      mediaRecorder.start(1000);
    }, 0);
  }
}
const onStart = async (isSwitch = false, type = '') => {
  if ((status.value === 2 || status.value === 3) && !isSwitch) {
    return;
  }
  if (status.value == 0 || (!inputDeviceId.value && !outputChecked.value)) {
    return;
  }
  if (!isSwitch) {
    await Promise.all([startInputStream(type), startOutputStream(type)]);
  }

  const audioTracks = [];
  if (inputStream) {
    audioTracks.push(...inputStream.getAudioTracks());
  }

  if (outputStream) {
    audioTracks.push(...outputStream.getAudioTracks());
  }

  if (audioTracks.length === 0) {
    return;
  }

  if (status.value === 1) {
    recordedChunks = [];
    status.value = 2;
    recordedTime.value = '00:00:00';
    totalTime = 0;
    mediaRecorder = null;
    name = fromatDate('yyyy-MM-dd hh:mm:ss', +new Date());
  }

  audioContext = new AudioContext({
    sampleRate: 48000,
    latencyHint: 'interactive',
  });

  let audioProcessor;
  // 初始化音频处理工作器
  try {
    const workletUrl = '/audio-processor.js';
    await audioContext.audioWorklet.addModule(workletUrl);
    audioProcessor = new AudioWorkletNode(audioContext, 'audio-processor');
    audioProcessor.connect(audioContext.destination);
  } catch (error) {
    console.error('加载音频处理工作器失败:', error);
    // 使用 ScriptProcessor 作为后备方案
    const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
    scriptProcessor.onaudioprocess = (e) => {
      const input = e.inputBuffer.getChannelData(0);
      const output = e.outputBuffer.getChannelData(0);

      for (let i = 0; i < input.length; i++) {
        const sample = input[i];
        // 简单的噪声门限和增益控制
        if (Math.abs(sample) < 0.01) {
          output[i] = 0;
        } else {
          output[i] = sample * 0.5;
        }
      }
    };
    scriptProcessor.connect(audioContext.destination);
    audioProcessor = scriptProcessor;
  }

  mixedStream = audioContext.createMediaStreamDestination();

  // 创建混合器
  const mixer = audioContext.createGain();
  mixer.gain.value = 1.0;
  mixer.connect(mixedStream);

  // 输入流处理
  if (inputStream) {
    const inputSource = audioContext.createMediaStreamSource(inputStream);
    inputSource.connect(mixer);
  }

  // 输出流处理
  if (outputStream) {
    const outputSource = audioContext.createMediaStreamSource(outputStream);
    outputSource.connect(mixer);
  }

  if (isMac) {
    // 在混合后的流上应用回声消除
    const mixedSource = audioContext.createMediaStreamSource(mixedStream.stream);
    mixedSource.connect(audioProcessor);
    audioProcessor.connect(audioContext.destination);

    // 设置音频处理器参数
    if (audioProcessor.port) {
      audioProcessor.port.postMessage({
        type: 'init',
        config: {
          mode: 'mixed',
          threshold: -50,
          attack: 0.001,
          release: 0.1,
          echoGain: 0.3,
        },
      });
    }
  }

  mediaRecorder = new MediaRecorder(mixedStream.stream, {
    mimeType: 'audio/webm',
    audioBitsPerSecond: 192000,
  });

  mediaRecorder.ondataavailable = (event) => {
    recordedChunks.push(event.data);
  };

  mediaRecorder.onerror = (error) => {
    console.log(error, 'onerroronerror');
    onStop();
  };

  mediaRecorder.onstart = () => {
    startTime = +new Date();
    clearInterval(timer);
    timer = null;
    timer = setInterval(onStartTiming, 200);
  };

  mediaRecorder.onpause = () => {
    totalTime = totalTime + +new Date() - startTime - 100;
    clearInterval(timer);
    timer = null;
  };

  mediaRecorder.onresume = () => {
    startTime = +new Date();
    clearInterval(timer);
    timer = null;
    timer = setInterval(() => {
      recordedTime.value = formatTime('hh:mm:ss', totalTime + +new Date() - startTime);
      window.__E__.ipcRenderer.send('get-record-time', recordedTime.value);
    }, 200);
  };
  console.log(recordedChunks, 'recordedChunks');

  mediaRecorder.start(1000);
  window.__E__?.ipcRenderer.send('set-icon', 'on');
  if (inputStream && outputStream) {
    log({ type: 'record_sound_all' });
  }

  if (inputStream && !outputStream) {
    log({ type: 'record_sound_close_audio' });
  }

  if (!inputStream && outputStream) {
    log({ type: 'record_sound_close_mic' });
  }
};

const ouUpload = async (chunks: Blob[], name: string) => {
  console.log(chunks, 'chunks-ouUpload');
  const file = new Blob(chunks, {
    type: mediaRecorder.mimeType,
  });
  const buffer = await file.arrayBuffer();
  const uint8Array = new Uint8Array(buffer);
  let newBuffer;
  const res = await fixWebMHeader(uint8Array);

  newBuffer = Buffer.from(res);

  const taskInfo = {
    id: uuidv4(),
    name,
    buffer: newBuffer,
    progress: 0,
    status: 'pending',
  };

  tasks.value.push(taskInfo);
  uploadQueue.addTask(taskInfo);
};

const onRetry = (task) => {
  task.status = 'pending';
  task.progress = 0;
  task.retry = true;
  uploadQueue.addTask(task);
};

const onDelete = (task) => {
  window.__E__.ipcRenderer.send('delete-upload-item', { ...task });
};

const confirmDelete = (task) => {
  onComplete(task);
  window.__E__.ipcRenderer.send('delete-audio', task.id);
};

const onStop = () => {
  isStartOrStopHover.value = false;
  clearInterval(timer);
  timer = null;
  window.__E__?.ipcRenderer.send('set-icon', 'off');

  stopRecording();
  // 小于两秒不上传
  if (!RECORD_TIME.includes(recordedTime.value)) {
    ouUpload(recordedChunks.slice(), name);
  }
  stopInputStream();
  stopOutputStream();

  // 关闭 AudioContext
  if (audioContext && audioContext.state !== 'closed') {
    audioContext.close();
  }
  status.value = 1;
  recordedChunks = [];
  recordedTime.value = '00:00:00';
  // window.__E__.ipcRenderer.send('get-record-time', '');
  totalTime = 0;
  mediaRecorder = null;
  log({ type: 'end_record' });
};

const handlePause = () => {
  if (isAuto.value) {
    return;
  }
  onPause();
};

const onPause = () => {
  if (mediaRecorder?.state === 'recording') {
    mediaRecorder?.pause();
  }
  status.value = 3;
};

const handleResume = () => {
  if (isAuto.value) {
    return;
  }
  onResume();
};

const onResume = () => {
  if (mediaRecorder?.state === 'paused') {
    mediaRecorder?.resume();
  }
  status.value = 2;
};

const onAutoRecordClick = () => {
  if (!isAuto.value) {
    return;
  }
  if (status.value === 2) {
    onPause();
  } else if (status.value === 3) {
    onResume();
  }
};

async function init() {
  window.__E__?.ipcRenderer.send('set-log', `record page init`);
  const data = await getDevices();
  virtualSpeaker = data.virtualSpeaker;
  devices.value.input = data.inputDevices;
  devices.value.output = data.outputDevices;
  if (devices.value.input.length > 0) {
    inputDeviceId.value = data.defaultInputDeviceId;
    status.value = 1;
    // await startInputStream();
  }

  if (devices.value.output.length > 0) {
    if (isMac && virtualSpeaker.deviceId) {
      log({ type: 'install_virtual_device' });
      // let index = 0;
      // // let originalDevice = await window.__E__?.ipcRenderer.invoke('get-original-device');
      // index = devices.value.output.findIndex(
      //   (device: any) => device.label.split(' (')[0] === originalDevice,
      // );
      defaultOutputDeviceId = data.defaultOutputDeviceId;
      defaultOutputDeviceName = data.defaultOutputDeviceName;
      window.__E__?.ipcRenderer.send('default-device', defaultOutputDeviceName);

      if (data.isVirtualDefault) {
        console.log(defaultOutputDeviceName, 'defaultOutputDeviceName');
        await window.__E__?.ipcRenderer.invoke('switch-to-device', defaultOutputDeviceName);
        // await window.__E__?.ipcRenderer.invoke('switch-to-virtual-device');
      }

      // // 获取一次设备 解决 第一次播放无声音的问题
      // let stream: any = await navigator.mediaDevices.getUserMedia({
      //   audio: {
      //     deviceId: { exact: virtualSpeaker.deviceId },
      //   },
      // });
      // console.log(defaultOutputDeviceId, 'defaultOutputDeviceId1');
      // audioElement.setSinkId(defaultOutputDeviceId);
      // audioElement.srcObject = stream;
      // setTimeout(() => {
      //   audioElement.pause();
      //   stream?.getTracks().forEach((track) => track.stop());
      //   stream = null;
      //   window.__E__?.ipcRenderer.invoke('switch-to-device', defaultOutputDeviceName);
      // }, 0);
    }
    status.value = 1;
    // await startOutputStream();
  }

  if (status.value === 1) {
    log({ type: 'grant_mic_permission' });
    // await startStream();
  }

  window.__E__?.ipcRenderer.send(
    'set-log',
    `status.value:${status.value},route.query.auto:${route.query.auto},isPPCopened.value:${isPPCopened.value} `,
  );
  // web跳转过来时，自动开始录音
  if (status.value === 1 && route.query.auto === '1' && isPPCopened.value && date) {
    window.__E__?.ipcRenderer.send('set-log', `init-login-from-protocol`);
    isAuto.value = false;
    onStart();
    log({ type: 'record_from_web' });
  }
  if (status.value === 0) {
    const result = await requestMediaPermissions();
    if (result) {
      init();
    }
  }
}

const watchRecordOrUpload = computed(() => {
  const isUpload = tasks.value.some((item) => {
    return item.status === 'pending';
  });
  let token = localStorage.getItem(TOKEN);
  // 没登录态时，上传一定会失败
  if (!(status.value === 2 || status.value === 3) && isUpload && token) {
    window.__E__.ipcRenderer.send('get-record-time', t('uploading'));
  }
  if (!(status.value === 2 || status.value === 3) && !isUpload) {
    window.__E__.ipcRenderer.send('get-record-time', '');
  }
  return status.value === 2 || (status.value === 3 && isUpload);
});

const startOrStopIconName = computed(() => {
  if (isStartOrStopHover.value) {
    return 'stop-hover';
    // return status.value === 2 ? 'stop-hover' : 'start-hover';
  }
  return 'stop';
  // return status.value === 2 ? 'stop' : 'start';
});

const keydownHandler = (event) => {
  const modifier = isMac ? event.metaKey : event.ctrlKey;
  if (modifier && event.key === ',') {
    event.preventDefault();
    console.log('快捷键 Command+, 或 Ctrl+, 被按下');
    openSettings();
  } else if (modifier && event.key === 'q') {
    event.preventDefault();
    console.log('快捷键 Command+Q 或 Ctrl+q 被按下');
    window.__E__?.ipcRenderer.send('app-exit');
  }
};

const setupKeyboardShortcuts = () => {
  document.addEventListener('keydown', keydownHandler);
};
const removeKeyboardShortcuts = () => {
  document.removeEventListener('keydown', keydownHandler);
};
const openSettings = () => {
  window.__E__?.ipcRenderer.send('create-settings');
};

const onDeviceChange = debounce(async (event) => {
  console.log('onDeviceChange', isSwitching);
  // return;
  if (isMac) {
    if (isSwitching) {
      return;
    }
  }

  const data = await getDevices();

  devices.value.input = data.inputDevices;

  devices.value.output = data.outputDevices;
  if (isMac) {
    defaultOutputDeviceId = data.defaultOutputDeviceId;
    defaultOutputDeviceName = data.defaultOutputDeviceName;
    window.__E__?.ipcRenderer.send('default-device', defaultOutputDeviceName);

    if (data.isVirtualDefault && status.value != 2 && status.value != 3) {
      await window.__E__?.ipcRenderer.invoke('switch-to-device', defaultOutputDeviceName);
    }

    if (outputChecked.value && (status.value === 2 || status.value === 3)) {
      console.log(defaultOutputDeviceId, 'defaultOutputDeviceId');

      audioElement.setSinkId(defaultOutputDeviceId);

      // audioElement
      //   .setSinkId(defaultOutputDeviceId)
      //   .then(() => {
      //     console.log('音频输出已设置为:', defaultOutputDeviceId);
      //     console.log(outputStream.getAudioTracks());
      //     // 确保音频播放
      //     audioElement.play().catch((error) => {
      //       console.error('播放音频失败:', error);
      //     });
      //     console.log('暂停状态:', audioElement, '结束状态:', audioElement.ended);
      //   })
      //   .catch((error) => {
      //     console.error('设置 sinkId 失败:', error);
      //   });
    }
  }

  if (status.value === 2 || status.value === 3) {
    if (!inputDeviceId.value) {
      return;
    }
    if (data.inputDevices.find((item) => item.deviceId === inputDeviceId.value)) {
      return;
    }
    inputDeviceId.value = data.defaultInputDeviceId;

    // 减去100 ms误差
    clearInterval(timer);
    timer = null;
    totalTime = totalTime + +new Date() - startTime - 100;
    stopRecording();
    stopInputStream();
    if (inputDeviceId.value) {
      await startInputStream();
    }
    onStart(true);
  }

  if (status.value != 2 && status.value != 3 && inputDeviceId.value) {
    inputDeviceId.value = data.defaultInputDeviceId;
  }
}, 100);

let isAuto = ref(false);

const autoStart = throttle((event, data) => {
  if (!isPPCopened.value) {
    return;
  }
  if (data) {
    if (status.value != 1) {
      return;
    }
    window.__E__?.ipcRenderer.send('set-log', `auto start ,onStart`);
    onStart(false, 'auto');
    isAuto.value = true;
    log({ type: 'record_auto_start' });
  } else {
    window.__E__?.ipcRenderer.send('set-log', `auto start , data: false, isAuto: ${isAuto.value}`);
    window.__E__?.ipcRenderer.send('set-log', `auto start , data: false, status: ${status.value}`);
    if (isAuto.value && (status.value === 2 || status.value === 3)) {
      onStop();
      isAuto.value = false;
      log({ type: 'record_auto_end' });
    }
  }
}, 2000);
const recording = () => {
  const isUpload = tasks.value.some((item) => {
    return item.status === 'pending';
  });
  return status.value === 2 || status.value === 3 || isUpload;
};

const onUpdate = () => {
  if (recording()) {
    window.__E__?.ipcRenderer.send('on-update-confirm');
  } else {
    window.__E__?.ipcRenderer.send('on-update');
  }
};

const processActivity = (event, data) => {
  const token = localStorage.getItem(TOKEN);
  window.__E__?.ipcRenderer.send(
    'set-log',
    `on-mic-process-activity,token:${token},data:${data},timer:${timer},status.value:${status.value},date:${date}`,
  );

  // 只有登录态存在的时候,且没在自动录音的时候，才自动开始
  // 加date的原因是：退出登录后，点击登录再从WEB回来后，这里会触发两次，之前的本应该onmounted的页面的定时器没被彻底销毁
  if (data) {
    if (token && !timer && date) {
      autoStart(event, data);
    }
  } else {
    autoStart(event, data);
  }
};

onMounted(async () => {
  window.__E__?.ipcRenderer.send('set-log', `onMounted_open_desktop`);
  log({ type: 'open_desktop' });
  isMac && setupKeyboardShortcuts();
  window.__E__?.ipcRenderer.on('login-from-protocol', async (event, data) => {
    if (data.auto === '1' && status.value === 1 && isPPCopened.value && date) {
      window.__E__?.ipcRenderer.send('set-log', `onMounted-login-from-protocol`);
      // await Promise.all([startInputStream(), startOutputStream()]);

      isAuto.value = false;
      onStart();
      log({ type: 'record_from_web' });
    }
  });

  window.__E__?.ipcRenderer.on('request-recordOrUpload-status', (event, data) => {
    window.__E__?.ipcRenderer.send('send-recordOrUpload-status', recording());
  });

  window.__E__?.ipcRenderer.on('request-recordOrUpload-status-login', (event, data) => {
    window.__E__?.ipcRenderer.send('send-recordOrUpload-status-login', recording());
  });

  window.__E__?.ipcRenderer.on('request-recordOrUpload-status-ppc', (event, data) => {
    window.__E__?.ipcRenderer.send('send-recordOrUpload-status-ppc', recording());
  });

  window.__E__?.ipcRenderer.on('ppc-change', (event, data) => {
    isPPCopened.value = data;
  });

  window.__E__?.ipcRenderer.on('upload-item-deleted', (event, data) => {
    confirmDelete(data);
  });

  window.__E__?.ipcRenderer.on('set-audio-setting', (event, data) => {
    onInputSelect(data);
  });

  // window.__E__?.ipcRenderer.on('change-token', (event, data) => {
  //   console.log('change-tokenchange-tokenchange-token');
  //   // 如果此时登录态失效，需要将文件保存到本地
  //   if (!data) {
  //     if (isAuto.value) {
  //       // 自动录音停止
  //       autoStart('', false);
  //     } else {
  //       // 手动录音停止
  //       onStop();
  //     }
  //   }
  // });

  // 注释 勿删
  // window.__E__?.ipcRenderer.on('visible', async (event, data) => {
  //   window.__E__?.ipcRenderer.send('set-log', `visible: ${data} ${status.value}`);
  //   if (data === 'show') {
  //     if (status.value === 1) {
  //       startOutputStream();
  //       startInputStream();
  //     }
  //   } else if (data === 'hide') {
  //     if (status.value === 1) {
  //       stopInputStream();
  //       stopOutputStream();
  //       // if (isMac) {
  //       //   if (outputChecked.value) {
  //       //     audioElement.pause();
  //       //     window.__E__?.ipcRenderer.invoke('restore-audio-device');
  //       //   }
  //       // }
  //     }
  //   }
  // });
  try {
    await init();
  } catch (e) {
    console.error('init', e);
  }
  uploadQueue = new UploadQueue({
    onProgress,
    onComplete,
    onError,
  });

  const data = await window.__E__?.ipcRenderer.invoke('get-audios');
  data.forEach((item) => {
    const taskInfo = {
      progress: 0,
      status: 'pending',
      retry: true,
      ...item,
    };

    tasks.value.push(taskInfo);
    uploadQueue.addTask(taskInfo);
  });
  // tasks.value = data;

  navigator.mediaDevices.ondevicechange = onDeviceChange;

  window.__E__?.ipcRenderer.on('mic-process-activity', processActivity);

  emitter.on('on-update', onUpdate);
});

const onFolder = (task) => {
  window.__E__?.ipcRenderer.send('open-audio', { ...task });
};

onUnmounted(() => {
  window.__E__?.ipcRenderer.send('set-log', `record page: onUnmounted`);
  if (audio) {
    audio.pause();
    audio = null;
  }

  isMac && removeKeyboardShortcuts();

  // stopRecording();
  // stopInputStream();
  // stopOutputStream();
  // mediaRecorder = null;

  if (isAuto.value) {
    // 自动录音停止
    // autoStart('', false);
    if (isAuto.value && (status.value === 2 || status.value === 3)) {
      onStop();
      isAuto.value = false;
      log({ type: 'record_auto_end' });
    }
    window.__E__?.ipcRenderer.send('set-log', `record page: onUnmounted - autostart-false`);
  } else {
    // 手动录音停止
    onStop();
    window.__E__?.ipcRenderer.send('set-log', `record page: onUnmounted - onstop`);
  }
  window.__E__.ipcRenderer.send('get-record-time', '');
  mediaRecorder = null;
  // 关闭 AudioContext
  if (audioContext && audioContext.state !== 'closed') {
    audioContext.close();
  }
  clearInterval(timer);
  timer = null;
  date = null;
  window.__E__?.ipcRenderer.remove('mic-process-activity', processActivity);

  emitter.off('on-update', onUpdate);
});

let isHistoryExpanded = ref(false);

const toggleHistory = () => {
  isHistoryExpanded.value = !isHistoryExpanded.value;
};
</script>

<template>
  <div>
    <div class="flex h-[52px] items-center px-4 py-2 pb-3 relative microphone gap-3">
      <span class="absolute top-[-100px]"> {{ watchRecordOrUpload }}</span>
      <PopoverRoot :open="opend" @update:open="(value) => (opend = value)">
        <PopoverTrigger>
          <div
            class="flex items-center w-[118px] h-8 px-2 py-1 border border-gray9 rounded-md bg-white outline-none hover:bg-gray3"
          >
            <div
              class="record-mic-container flex-1 flex items-center gap-1.5 h-8 cursor-pointer relative"
            >
              <svg-icon
                :name="inputDeviceId ? inputConfig.icon : inputConfig.disableIcon"
                class="w-6 h-6 text-black7"
              />
              <div
                v-if="!opend"
                class="record-mic absolute left-1/2 -translate-x-1/2 rounded-md text-[12px] z-[999]"
              >
                {{ t('record_mic') }}
              </div>

              <div v-if="status === 2" class="flex gap-1">
                <div
                  v-for="index in 10"
                  class="w-[3px] h-3 rounded-sm bg-gray6"
                  :class="{ 'bg-green1': index < inputVolume }"
                ></div>
              </div>
              <div v-else class="flex gap-1">
                <div v-for="index in 10" class="w-[3px] h-3 rounded-sm bg-[#F2F4F7]"></div>
              </div>
            </div>
          </div>
          <!-- <svg-icon
              :name="opend ? 'arrow-up' : 'arrow-down'"
              class="w-7 h-8 py-2 px-1.5 hover:bg-gray3 active:bg-gray2 cursor-pointer"
              :class="{
                'bg-gray3': opend,
              }"
            /> -->
        </PopoverTrigger>

        <PopoverPortal>
          <PopoverContent
            :side-offset="6"
            align="start"
            class="z-[99999] flex max-h-[210px] overflow-y-auto w-[240px] flex-col overflow-hidden rounded-lg bg-white py-2 px-1.5 shadow-1 device-container"
          >
            <div class="flex items-center">
              <button type="button" class="group text-sm flex-1">
                <div
                  class="custom-radio flex p-2 cursor-pointer rounded hover:bg-gray3 active:bg-gray2"
                  :class="inputDeviceId ? 'transparent-radio' : ''"
                >
                  <input
                    id="record-switch"
                    :checked="!inputDeviceId"
                    name="record-switch"
                    type="radio"
                    class="cursor-pointer"
                  />
                  <label
                    @click="onInputSelect()"
                    for="record-switch"
                    class="cursor-pointer text-left"
                    >{{ t('not_record_mic') }}
                  </label>
                </div>
              </button>
            </div>
            <div class="flex border-t border-t-gray9 w-full my-2"></div>

            <button
              v-for="(audio, index) in devices[inputConfig.type]"
              :key="index"
              type="button"
              class="group text-sm"
            >
              <div
                class="custom-radio flex p-2 cursor-pointer rounded hover:bg-gray3 active:bg-gray2"
                @click="onInputSelect(audio)"
                :class="!inputDeviceId ? 'transparent-radio' : ''"
              >
                <input
                  :id="audio.deviceId"
                  :checked="inputDeviceId === audio.deviceId"
                  name="radio"
                  type="radio"
                  class="cursor-pointer"
                />
                <label :for="audio.value" class="cursor-pointer text-left">{{ audio.label }}</label>
              </div>
            </button>
          </PopoverContent>
        </PopoverPortal>
      </PopoverRoot>
      <div
        class="flex items-center w-[118px] h-8 px-2 py-1 border border-gray9 rounded-md bg-white outline-none hover:bg-gray3"
      >
        <div
          class="record-audio-container flex-1 flex justify-center gap-1.5 items-center h-8 cursor-pointer relative"
          @click="onOutputCheck()"
        >
          <svg-icon
            :name="outputChecked ? outputConfig.icon : outputConfig.disableIcon"
            class="w-6 h-6 text-black7"
          />

          <div class="record-audio absolute rounded-md text-[12px] z-[999]">
            {{ t('record_audio') }}
          </div>

          <div v-if="status === 2" class="flex gap-1">
            <div
              v-for="index in 10"
              class="w-[3px] h-3 rounded-sm bg-gray6"
              :class="{ 'bg-green1': index < outputVolume }"
            ></div>
          </div>
          <div v-else class="flex gap-1">
            <div v-for="index in 10" class="w-[3px] h-3 rounded-sm bg-[#F2F4F7]"></div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="tasks.length > 0" class="flex border-t border-t-gray9 w-full"></div>
    <div class="px-4 select-none py-3" v-if="tasks.length > 0">
      <div class="text-gray7 text-[12px] font-semibold pb-[6px]">{{ t('upload_queue') }}</div>
      <div class="max-h-[90px] overflow-y-auto overflow-x-hidden device-container">
        <div
          class="flex items-center text-black3 gap-1.5 h-9 rounded-md p-1.5 pl-0"
          v-for="task in tasks"
        >
          <svg-icon name="file" class="w-6 h-6" />
          <span class="flex-1 text-[14px] whitespace-nowrap">{{ task.name }}</span>

          <div class="flex items-center" v-if="task.status === 'failed'">
            <div class="file-container relative">
              <svg-icon
                name="folder"
                class="w-6 h-6 p-1 rounded-lg hover:bg-gray3 active:bg-gray2 cursor-pointer"
                @click="onFolder(task)"
              />
              <div class="button-icon absolute rounded-md text-[12px] z-[999] left-6">
                {{ isMac ? t('show_in_finder') : t('show_in_explorer') }}
              </div>
            </div>
            <div class="file-container relative">
              <svg-icon
                name="delete"
                class="w-6 h-6 p-1.5 rounded-lg hover:bg-gray3 active:bg-gray2 cursor-pointer"
                @click="onDelete(task)"
              />
              <div class="button-icon absolute rounded-md text-[12px] z-[999]">
                {{ t('delete') }}
              </div>
            </div>
            <div class="file-container relative">
              <svg-icon
                name="retry"
                class="w-6 h-6 p-1 rounded-lg hover:bg-gray3 active:bg-gray2 cursor-pointer"
                @click="onRetry(task)"
              />
              <div class="button-icon absolute rounded-md text-[12px] z-[999]">
                {{ t('retry') }}
              </div>
            </div>
          </div>
          <Progress v-else :progress="task.progress" />
        </div>
      </div>
    </div>
    <div v-if="uploadHistory.length > 0" class="flex border-t border-t-gray9 w-full"></div>
    <div class="px-4 select-none py-3" v-if="uploadHistory.length > 0">
      <div class="flex items-center justify-between cursor-pointer" @click="toggleHistory">
        <div class="text-gray7 text-[12px] font-semibold">{{ t('upload_history') }}</div>
        <svg-icon :name="isHistoryExpanded ? 'arrow-down' : 'arrow-right'" class="w-4 h-4" />
      </div>

      <div
        v-if="isHistoryExpanded"
        class="device-container overflow-y-auto overflow-x-hidden upload-history max-h-[150px]"
      >
        <div
          class="flex items-center text-black3 gap-1.5 h-9 rounded-md p-1.5 pl-0"
          v-for="history in uploadHistory"
        >
          <svg-icon name="file" class="w-6 h-6" />
          <span class="flex-1 text-[14px] whitespace-nowrap">{{ history }}</span>
        </div>
      </div>
    </div>
    <div v-if="isPPCopened" class="flex border-t border-t-gray9 w-full"></div>
    <div
      class="h-15 flex items-center justify-center pt-2.5 pb-3 select-none relative bottom-container"
      :class="!isPPCopened ? 'bg-[#F2F4F7]' : 'bg-gray2'"
    >
      <div
        v-if="status === 1 || status === 0"
        @click="onStartRecord"
        class="flex p-1.5 h-10 w-full mx-[30px] gap-1.5 items-center justify-center rounded-[20px] bg-black3 text-white font-semibold text-[14px]"
        :class="{
          'opacity-40 cursor-not-allowed': status === 0 || (!inputDeviceId && !outputChecked),
          'opacity-30': !isPPCopened,
          'bottom-button cursor-pointer': isPPCopened,
        }"
      >
        <svg-icon name="record" class="w-6 h-6" />
        {{ t('start_recording') }}
      </div>

      <div v-else-if="status === 2 || status === 3" class="flex flex-col items-center">
        <div
          class="flex p-1 h-10 mx-[30px] w-[220px] items-center justify-center rounded-[20px] bg-white text-white font-semibold"
          :class="{
            'gap-1': isAuto,
            'gap-3': !isAuto,
            'hover:bg-[#F2F4F7]': isAuto,
            'cursor-pointer': isAuto,
          }"
          @click="onAutoRecordClick"
        >
          <div class="resume-container relative">
            <svg-icon
              v-if="status === 2"
              name="pause"
              class="w-8 h-8 rounded-full cursor-pointer"
              :class="{ 'hover:bg-[rgba(241,67,73,0.2)]': !isAuto }"
              @click="handlePause"
            />
            <svg-icon
              v-if="status === 3"
              name="resume"
              class="w-8 h-8 rounded-full cursor-pointer"
              :class="{ 'hover:bg-[#E4E7EC]': !isAuto }"
              @click="handleResume"
            />
            <div v-if="!isAuto" class="button-icon absolute rounded-md text-[12px] z-[999]">
              {{ status === 2 ? t('pause') : t('start') }}
            </div>
          </div>
          <div
            v-if="status === 2"
            class="flex items-center text-red1 gap-2"
            :class="{ 'flex-1 justify-center': !isAuto, 'w-[80px] justify-start': isAuto }"
          >
            <div class="font-semibold text-[14px]">{{ recordedTime }}</div>
          </div>
          <div
            v-else
            class="flex items-center text-[#1F1F1F]"
            :class="{ 'flex-1 justify-center': !isAuto, 'w-[80px] justify-start': isAuto }"
          >
            <div class="font-semibold text-[14px]">{{ recordedTime }}</div>
          </div>
          <div class="stop-container relative" v-if="!isAuto">
            <svg-icon
              :name="startOrStopIconName"
              class="w-8 h-8 cursor-pointer stop-icon rounded-full"
              @click="onStop"
              @mouseenter="isStartOrStopHover = true"
              @mouseleave="isStartOrStopHover = false"
            />
            <div class="button-icon-right absolute rounded-md text-[12px] z-[999]">
              {{ t('stop') }}
            </div>
          </div>
        </div>
        <div v-if="isAuto" class="text-[14px] text-[#858C9B] mt-1.5 max-w-[220px] text-center">
          {{ t('auto_stop_when_meetings_end') }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.custom-radio {
  // padding-left: 10px;
}
/* 隐藏原生的 Radio 按钮 */
.custom-radio input[type='radio'] {
  display: none;
}

/* 自定义外观 */
.custom-radio label {
  display: flex;
  position: relative;
  padding-left: 20px;
}

.custom-radio label::before {
  position: absolute;
  left: 0;
  top: 3px;
  content: '';
  width: 14px !important;
  height: 14px;
  margin-right: 8px;
  border: 1px solid var(--gray9);
  border-radius: 100%;
  background-color: #fff;
  transition: all 0.3s;
}

/* 勾选时的样式 */
.custom-radio input[type='radio']:checked + label::before {
  background-color: var(--black3);
  border-color: var(--black3);
}

.transparent-radio input[type='radio']:checked + label::before {
  background-color: #fff;
  border-color: var(--gray9);
}

.custom-radio input[type='radio']:checked + label::after {
  content: '';
  position: absolute;
  top: 7px;
  left: 4px;
  width: 6px;
  height: 6px;
  background-color: #fff;
  border-radius: 100%;
  transform: scale(1);
  transition: all 0.3s;
}
.bottom-container {
  border-top: 1px solid #e4e7ec;
  background: #f2f4f7;
}

.bottom-button:hover {
  background:
    linear-gradient(0deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 100%), #1f1f1f;
}

.device-container::-webkit-scrollbar {
  /* 隐藏默认的滚动条 */
  -webkit-appearance: none;
}

.device-container::-webkit-scrollbar:vertical {
  /* 设置垂直滚动条宽度 */
  width: 4px;
}

.device-container::-webkit-scrollbar-thumb {
  /* 滚动条的其他样式定制，注意，这个一定也要定制，否则就是一个透明的滚动条 */
  border-radius: 8px;
  border: 2px solid #d0d5dd;
  background-color: #d0d5dd;
}

.record-mic {
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
  white-space: nowrap;
  top: 110%;
}
.record-mic-container:hover .record-mic {
  opacity: 1;
  visibility: visible;
  border-radius: 4px;
  background: #344054;
  padding: 2px 8px;
  color: white;
  white-space: nowrap;
}

.record-audio {
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
  white-space: nowrap;
  top: 110%;
  right: 10%;
}

.record-audio-container:hover .record-audio {
  opacity: 1;
  visibility: visible;
  border-radius: 4px;
  background: #344054;
  padding: 2px 8px;
  color: white;
  white-space: nowrap;
}

.button-icon {
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
  white-space: nowrap;
  top: 60%;
  left: 80%;
}

.button-icon-right {
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
  white-space: nowrap;
  top: 60%;
  right: 80%;
}

.resume-container:hover .button-icon {
  opacity: 1;
  visibility: visible;
  border-radius: 4px;
  background: #344054;
  padding: 2px 8px;
  color: white;
  white-space: nowrap;
}

.stop-container:hover .button-icon-right {
  opacity: 1;
  visibility: visible;
  border-radius: 4px;
  background: #344054;
  padding: 2px 8px;
  color: white;
  white-space: nowrap;
}

.file-container .button-icon {
  bottom: 0;
  left: auto;
  top: auto;
  right: 100%;
  max-width: 160px;
  white-space: nowrap; /* 禁止文本换行，确保单行显示 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.file-container:hover .button-icon {
  opacity: 1;
  visibility: visible;
  border-radius: 4px;
  background: #344054;
  padding: 2px 8px;
  color: white;
  white-space: nowrap;
}
</style>
