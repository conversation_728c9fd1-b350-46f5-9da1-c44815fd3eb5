<template>
  <svg class="w-5 h-5" viewBox="0 0 100 100">
    <!-- 背景圆环 -->
    <circle cx="50" cy="50" r="45" class="stroke-gray2 fill-transparent" stroke-width="10" />

    <!-- 进度圆环 -->
    <circle
      cx="50"
      cy="50"
      r="45"
      class="stroke-black3 fill-transparent animate-spin origin-center"
      stroke-width="10"
      stroke-dasharray="283"
      :stroke-dashoffset="dashOffset"
      style="transition: stroke-dashoffset 0.5s ease"
    ></circle>

    <g clip-path="url(#clip0_268_6019)">
      <path
        d="M49.9889 72.9246C49.1065 72.9246 48.3844 72.6439 47.8229 72.0823C47.2773 71.5207 47.0046 70.7826 47.0046 69.8681V41.3238L47.3174 32.9724L49.0744 33.9832L41.4931 42.5754L36.0538 47.9665C35.781 48.2393 35.4681 48.4559 35.1151 48.6163C34.7621 48.7768 34.369 48.857 33.9358 48.857C33.1175 48.857 32.4356 48.5842 31.8901 48.0387C31.3445 47.4932 31.0718 46.8032 31.0718 45.9689C31.0718 45.1666 31.3847 44.4446 32.0104 43.8028L47.7747 27.9904C48.0635 27.7016 48.4005 27.4769 48.7856 27.3165C49.1867 27.156 49.5878 27.0758 49.9889 27.0758C50.4061 27.0758 50.8072 27.156 51.1923 27.3165C51.5934 27.4769 51.9384 27.7016 52.2272 27.9904L67.9915 43.8028C68.6173 44.4446 68.9302 45.1666 68.9302 45.9689C68.9302 46.8032 68.6574 47.4932 68.1119 48.0387C67.5663 48.5842 66.8844 48.857 66.0661 48.857C65.6329 48.857 65.2318 48.7768 64.8627 48.6163C64.5097 48.4559 64.1969 48.2393 63.9241 47.9665L58.4848 42.5754L50.8794 33.9832L52.6604 32.9724L52.9733 41.3238V69.8681C52.9733 70.7826 52.6925 71.5207 52.131 72.0823C51.5854 72.6439 50.8714 72.9246 49.9889 72.9246Z"
        fill="#1F1F1F"
      />
    </g>
    <defs>
      <clipPath id="clip0_268_6019">
        <rect width="100" height="100" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100,
  },
});

// 计算圆环偏移量
const dashOffset = computed(() => {
  const circumference = 283; // 2 * π * 45
  return circumference - (circumference * props.progress) / 100;
});
</script>
