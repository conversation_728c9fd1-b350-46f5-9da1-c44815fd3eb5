export function renderStreams(
  wavesurfer: any,
  inputStream: MediaStream | null,
  outputStream: MediaStream | null,
) {
  // 如果两个流都为空，直接返回
  if (!inputStream && !outputStream) {
    console.warn('No input or output stream provided');
    return () => {};
  }

  const audioContext = new AudioContext();
  const analyser = audioContext.createAnalyser();
  analyser.fftSize = 2048;

  // 创建目标节点
  const destination = audioContext.createMediaStreamDestination();

  // 合并流的函数
  const connectStream = (stream: MediaStream | null) => {
    if (stream) {
      const source = audioContext.createMediaStreamSource(stream);
      source.connect(analyser);
      source.connect(destination);
    }
  };

  // 连接输入和输出流
  connectStream(inputStream);
  connectStream(outputStream);

  const bufferLength = analyser.frequencyBinCount;
  const dataArray = new Float32Array(bufferLength);
  const sampleDuration = bufferLength / audioContext.sampleRate;

  let animationId: number;

  const drawWaveform = () => {
    // 获取时域数据
    analyser.getFloatTimeDomainData(dataArray);

    // 加载波形
    wavesurfer.load('', [dataArray], sampleDuration);

    // 继续动画
    animationId = requestAnimationFrame(drawWaveform);
  };

  // 开始绘制
  drawWaveform();

  // 返回停止函数
  return () => {
    if (animationId) {
      cancelAnimationFrame(animationId);
    }

    // 断开所有连接
    analyser.disconnect();
    destination.stream.getTracks().forEach((track) => track.stop());
  };
}
