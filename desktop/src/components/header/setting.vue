<script setup lang="ts">
import { HOST } from '@/constants/config';
import { i18n } from '@/locales/i18n';
const { t } = i18n.global;
import { log } from '@/utils/firebase';
import { emitter } from '@/utils/event-bus';
import { ref } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: '',
  },
});
const showUpdate = ref(false);
const onWeb = (url = HOST) => {
  log({ type: 'goto_web' });
  window.__E__?.shell.openExternal(url);
};

const onSetting = () => {
  window.__E__?.ipcRenderer.send('create-settings');
};

const onUpdate = () => {
  if (props.type === 'login') {
    window.__E__?.ipcRenderer.send('on-update');
  } else {
    emitter.emit('on-update');
  }
};

window.__E__?.ipcRenderer.on('update-ready', () => {
  showUpdate.value = true;
});
</script>

<template>
  <div class="flex gap-1">
    <div class="hoverable-web relative" v-if="showUpdate">
      <svg-icon
        @click="onUpdate()"
        name="update"
        class="w-9 h-9 p-1.5 rounded-lg hover:bg-gray3 active:bg-gray2 cursor-pointer"
      />
      <div class="web-tooltip absolute left-1/2 -translate-x-1/2 rounded-md text-[12px] z-[999]">
        {{ t('click_to_update') }}
      </div>
    </div>
    <div class="hoverable-web relative">
      <svg-icon
        @click="onWeb()"
        name="my-recording"
        class="w-9 h-9 p-1.5 rounded-lg hover:bg-gray3 active:bg-gray2 cursor-pointer"
      />
      <div class="web-tooltip absolute left-1/2 -translate-x-1/2 rounded-md text-[12px] z-[999]">
        {{ t('my_recordings') }}
      </div>
    </div>
    <div class="hoverable-setting relative">
      <svg-icon
        @click="onSetting"
        name="setting"
        class="w-9 h-9 p-1.5 rounded-lg hover:bg-gray3 active:bg-gray2 cursor-pointer"
      />
      <div class="setting-tooltip absolute left-1/2 rounded-md text-[12px] z-[999]">
        {{ t('preference') }}
      </div>
    </div>
  </div>
</template>

<style lang="less">
.web-tooltip {
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
  white-space: nowrap;
}

.setting-tooltip {
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s,
    visibility 0.3s;
  white-space: nowrap;
  transform: translateX(-80%);
}

.hoverable-web:hover .web-tooltip {
  opacity: 1;
  visibility: visible;
  border-radius: 4px;
  background: #344054;
  padding: 2px 8px;
  color: white;
  white-space: nowrap;
}

.hoverable-setting:hover .setting-tooltip {
  opacity: 1;
  visibility: visible;
  border-radius: 4px;
  background: #344054;
  padding: 2px 8px;
  color: white;
  white-space: nowrap;
}
</style>
