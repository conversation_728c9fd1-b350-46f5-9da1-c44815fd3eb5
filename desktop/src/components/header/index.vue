<script setup lang="ts">
import {
  PopoverArrow,
  PopoverClose,
  PopoverContent,
  PopoverPortal,
  PopoverRoot,
  PopoverTrigger,
} from 'radix-vue';
import Setting from './setting.vue';
import { TOKEN } from '@/constants/user';
import { HOST } from '@/constants/config';
import { useRouter } from 'vue-router';
import { i18n } from '@/locales/i18n';
import { ref } from 'vue';
const { t } = i18n.global;
const router = useRouter();

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
  // onSetUserInfo: {
  //   type: Function,
  // },
});

let opened = ref(false);

const onOpened = (value) => {
  opened.value = value;
};

const onWeb = (url = HOST) => {
  window.__E__?.shell.openExternal(url);
};

// const onSignOut = () => {
//   localStorage.removeItem(TOKEN);
//   window.__E__?.ipcRenderer.send('change-token', false);

//   router.push('/login');
// };

window.__E__?.ipcRenderer.on('sign-out-exec', (event, data) => {
  localStorage.removeItem(TOKEN);
  router.push('/login');
});
</script>

<template>
  <!-- <div class="h-[60px] flex items-center px-4 py-1.5" v-if="userInfo.data_user"> -->
  <!-- <div class="flex-1 flex gap-2 items-center">
      <PopoverRoot :open="opened" @update:open="onOpened">
        <PopoverTrigger
          class="flex items-center text-sm rounded font-medium focus:outline-none"
          aria-label="Update dimensions"
        >
          <img
            v-if="userInfo.data_user?.avatar"
            class="h-10 w-10 img-border"
            :class="opened ? 'opacity-70' : ''"
            :src="userInfo.data_user?.avatar"
          />
          <span class="noimg" v-else>{{
            userInfo.data_user.nickname ? userInfo.data_user.nickname.substring(0, 1) : ''
          }}</span>
        </PopoverTrigger>
        <PopoverPortal>
          <PopoverContent
            align="start"
            side="bottom"
            :side-offset="0"
            class="z-[99999] flex max-h-96 text-[16px] flex-col overflow-hidden rounded-lg bg-white px-1.5 py-2 shadow-1"
          >
            <button
              type="button"
              class="group h-14 justify-between p-2 rounded text-black3 hover:bg-gray3 active:bg-gray2 hover:svg"
              @click="onWeb(`${HOST}?profile=open`)"
            >
              <div
                class="w-[244px] text-left cursor-pointer font-semibold text-[14px] overflow-hidden text-ellipsis"
              >
                {{ userInfo.data_user.nickname }}
              </div>
              <div
                class="w-[244px] text-left cursor-pointer text-gray7 text-[12px] whitespace-nowrap overflow-hidden text-ellipsis mt-0.5"
              >
                {{ userInfo.data_user.email }}
              </div>
            </button>

            <div class="flex border-t border-t-gray9 w-full my-2"></div>

            <button
              type="button"
              class="group h-9 flex items-center justify-between text-[14px] p-2 rounded text-black3 hover:bg-gray3 active:bg-gray2 hover:svg"
              @click="onSignOut"
            >
              <div class="flex items-center cursor-pointer">{{ t('sign_out') }}</div>
            </button>
          </PopoverContent>
        </PopoverPortal>
      </PopoverRoot>

      <div class="flex flex-col leading-6 text-[14px] flex-1">
        <div
          class="text-black3 h-5 items-center leading-none font-semibold max-w-28 overflow-hidden text-ellipsis"
          :title="userInfo.data_user?.nickname"
        >
          {{ userInfo.data_user?.nickname }}
        </div>
        <div
          class="text-gray7 h-4 items-center leading-none text-[12px]"
          v-if="userInfo.data_state?.membership_type"
        >
          {{ userInfo.data_state.membership_type?.replace(/^\w/, (c) => c.toUpperCase()) }}
        </div>
      </div>
    </div> -->
  <div class="h-[60px] flex items-center justify-between px-4 pt-1.5">
    <svg-icon name="plaud-logo" class="w-[140px] h-4" />
    <Setting />
  </div>
</template>

<style lang="less">
.img-border {
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.noimg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #d0d5dd;
  box-sizing: border-box;
  background: #a9b5c7;
  border-radius: 50%;
  font-weight: bold;
  font-size: 20px;
  color: #ffffff;
}
</style>
