<template>
  <span class="flex items-center justify-center">
    <svg aria-hidden="true" class="max-h-full">
      <use :xlink:href="iconClassName" fill="currentColor" />
    </svg>
  </span>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import './icons';
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
});
// 图标在 iconfont 中的名字
const iconClassName = computed(() => {
  return `#icon-${props.name}`;
});
</script>
<style lang="less">
svg {
  margin: 0 !important;
}
</style>
