<script setup lang="ts">
import { i18n } from '@/locales/i18n';

const { t } = i18n.global;

const props = defineProps({
  isRecordClick: {
    type: Boolean,
    default: false,
  },
});

const toPreference = () => {
  window.__E__?.ipcRenderer.send('create-settings', { type: 'About' });
};
</script>

<template>
  <div class="px-[30px] pb-2.5 text-[12px]">
    <div class="flex flex-col items-center" :class="{ 'animated-element': isRecordClick }">
      <div class="text-[#858C9B] text-center">
        {{ t('enable_ppc_tip') }}
      </div>
      <div class="text-[#007AFF] cursor-pointer" @click="toPreference">
        {{ t('go_to_preference') }}
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.animated-element {
  animation: customMove 0.3s;
}

@keyframes customMove {
  0% {
    transform: translateX(0px);
  }
  20% {
    transform: translateX(8px);
  }
  30% {
    transform: translateX(3.4px);
  }
  40% {
    transform: translateX(7.76px);
  }
  50% {
    transform: translateX(-7.76px);
  }
  60% {
    transform: translateX(7.76px);
  }
  100% {
    transform: translateX(0px);
  }
}
</style>
