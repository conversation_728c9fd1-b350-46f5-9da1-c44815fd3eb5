import { createWebHashHistory, createRouter } from 'vue-router';

import Setting from '@/pages/setting/index.vue';
import Record from '@/pages/record/index.vue';
import Login from '@/pages/login/index.vue';
import OnBoarding from '@/pages/onBoarding/index.vue';

const routes = [
  { path: '/setting', meta: { title: 'Preference' }, component: Setting },
  { path: '/login', meta: { title: 'Login' }, component: Login },
  { path: '/', meta: { title: 'Home' }, component: Record },
  { path: '/onBoarding', meta: { title: 'OnBoarding' }, component: OnBoarding },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
