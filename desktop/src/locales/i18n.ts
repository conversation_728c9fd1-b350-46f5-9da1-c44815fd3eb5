import { createI18n } from 'vue-i18n';
import en_US from './json/en_US.json';
import es_ES from './json/es_ES.json';
import fr_FR from './json/fr_FR.json';
import de_DE from './json/de_DE.json';
import it_IT from './json/it_IT.json';
import ko_KR from './json/ko_KR.json';
import pt_PT from './json/pt_PT.json';
import zh_CN from './json/zh_CN.json';
import zh_TW from './json/zh_TW.json';
import ja_<PERSON> from './json/ja_JP.json';
import { LANGUAGE } from '@/constants/user';
type MessageSchema = typeof en_US;

let messages = { en_US, es_ES, fr_FR, de_DE, it_IT, ko_KR, pt_PT, zh_CN, zh_TW, ja_JP };
export const i18n = createI18n<[MessageSchema], 'en_US'>({
  fallbackLocale: 'en_US',
  locale: window.__E__?.store.get(LANGUAGE) || 'en_US',
  messages,
  legacy: false,
});

async function init() {
  // alert(await window.__E__?.store.get(LANGUAGE));
  i18n.global.locale.value = (await window.__E__?.store.get(LANGUAGE)) || 'en_US';
}
init();

export default i18n;

// export function browserLanguage() {
//   const languageMap: any = {
//     en: 'en_US',
//     es: 'es_ES',
//     fr: 'fr_FR',
//     de: 'de_DE',
//     it: 'it_IT',
//     ja: 'ja_JP',
//     ko: 'ko_KR',
//     pt: 'pt_PT',
//     zh: 'zh_CN',
//     'zh-CN': 'zh_CN',
//     'zh-TW': 'zh_TW',
//     'zh-HK': 'zh_TW',
//     'en-US': 'en_US',
//   };
//   try {
//     const language = navigator.language || navigator.userLanguage;
//     const result = languageMap[language] || 'en_US';
//     localStorage.setItem(LANGUAGE, result);
//     return result;
//   } catch (error) {
//     localStorage.setItem(LANGUAGE, 'en_US');
//     return 'en_US';
//   }
// }
