@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --gray1: rgba(152, 162, 179, 1);
    --gray2: rgba(228, 231, 236, 1);
    --gray3: rgba(228, 231, 236, 0.65);
    --gray4: rgba(242, 244, 247, 1);
    --gray5: #1f1f1f;
    --gray6: rgba(208, 213, 221, 1);
    --gray7: rgba(133, 140, 155, 1);
    --gray8: rgba(252, 252, 253, 1);
    --gray9: rgba(228, 231, 236, 1);
    --gray10: rgba(249, 250, 251, 1);

    /* background: rgba(102, 112, 133, 1); */

    --black1: rgba(56, 56, 56, 1);
    --black2: rgba(29, 41, 57, 1);
    --black3: rgba(31, 31, 31, 1);
    --black4: rgba(0, 0, 0, 0.4);
    --black5: rgba(102, 112, 133, 1);
    --black6: rgba(0, 0, 0, 0.09);
    --black7: rgba(71, 84, 103, 1);

    /* --text-color1: rgba(102, 112, 133, 1); */

    --blue1: rgb(19, 59, 103);
    --blue2: rgba(0, 122, 255, 0.1);
    --blue3: rgba(133, 169, 247, 0.2);

    --shadow1: 0 0 20px 0px rgba(0, 0, 0, 0.1);
    --shadow2: 0 0 20px 0px rgba(0, 0, 0, 0.1);

    --shadow3: 0px 0.5px 1.5px 0px rgba(0, 0, 0, 0.12) inset 0px 0px 1px 0px rgba(0, 0, 0, 0.02)
      inset;

    --green1: rgba(52, 199, 89, 1);
  }

  /* .dark {
  } */
}

#app {
  border-radius: 10px;
  width: 100%;
  background: #fff;
}

.body-top {
  display: flex;
  align-items: flex-start;
}

.body-bottom {
  display: flex;
  align-items: flex-end;
}

body::-webkit-scrollbar {
  display: none;
}

body,
html {
  user-select: none;
  background: transparent;
}

body {
  height: 100vh;
  width: 280px;
}
