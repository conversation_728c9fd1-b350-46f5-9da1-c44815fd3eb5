<script setup>
import { onMounted } from 'vue';
import { TOKEN } from '@/constants/user';
import { useRouter, useRoute } from 'vue-router';
import { log } from '@/utils/firebase';
import { getUserInfo } from '@/apis/user';
import { getDevices, requestMediaPermissions } from '@/utils/devices';

const router = useRouter();
const route = useRoute();

window.__E__?.ipcRenderer.on('login-from-protocol', (event, data) => {
  window.__E__?.ipcRenderer.send('set-log', `app login-from-protocol: ${JSON.stringify(data)}`);
  log({ type: 'login_success' });
  if (data.token) {
    localStorage.setItem(TOKEN, data.token);
  }
  if (route.path === '/login') {
    window.__E__?.ipcRenderer.send('set-log', `app login-from-protocol: go record page`);
    router.push({ path: '/', query: { auto: data.auto } });
    return;
  }
  if (data.position === 'onboarding') {
    router.replace({ path: '/onBoarding', query: { from: 'web' } });
    window.__E__?.ipcRenderer.send('set-log', `go onboarding with web`);
  }
});

window.__E__?.ipcRenderer.on('onboarding-close', (event, data) => {
  const token = localStorage.getItem(TOKEN);
  if (route.path === '/login' && token) {
    router.push({ path: '/' });
  }
});

window.__E__?.ipcRenderer.on('daily-activity', async (event, data) => {
  try {
    const res = await getUserInfo();
    window.__E__?.ipcRenderer.send('user-info', res);
    window.__E__?.ipcRenderer.send('change-token', true);
    setUser(res?.data_user?.id);
  } catch (e) {
    window.__E__?.ipcRenderer.send('set-log', `daily-activity-get-token: ${JSON.stringify(e)}`);
  }
});

if (location.search.includes('name=onBoarding')) {
  router.push('/onBoarding');
}

// 获取token
const getUserToken = () => {
  let token = localStorage.getItem(TOKEN);
  window.__E__?.ipcRenderer.send('user-token', token);
};

onMounted(async () => {
  window.__E__?.ipcRenderer.send('set-log', `app.vue on mounted`);
  setTimeout(async () => {
    const version = await window.__E__?.ipcRenderer.invoke('get-version');
    log({ type: 'version', data: version });
    // log({ type: `version_${version}` });
  }, 10000);
  if (route.path != 'onBoarding') {
    const data = await getDevices();
    console.log(data, '11111111111111');
    if (data.isVirtualDefault) {
      await window.__E__?.ipcRenderer.invoke('switch-to-device', data.defaultOutputDeviceName);
    }
  }

  getUserToken();
});
</script>
<template>
  <RouterView />
</template>
