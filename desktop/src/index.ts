import { createApp } from 'vue';
import router from './router';
import App from './app.vue';

import SvgIcon from './components/svg-icon/index.vue';

import './assets/styles/index.css';

import { LANGUAGE, NOTIFY, AUTO_LAUNCH } from '@/constants/user';
import * as Sentry from '@sentry/electron/renderer';

Sentry.init({
  environment: window.__E__?.env,
  dsn: 'https://<EMAIL>/2',
  integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
  tracesSampleRate: 1.0,
  replaysSessionSampleRate: 1,
  replaysOnErrorSampleRate: 1.0,
  // beforeSend(event) {
  //   console.log(event, '111111111111111111');
  //   return event;
  // },
});

// const eventId = Sentry.captureException(new Error('触发反馈'));
// Sentry.showReportDialog({ eventId: eventId, title: '请告诉我们问题' });

// import { log } from './utils/firebase';

// log({ type: 'app_start', test: 'test', aa: 111 });

// if (localStorage.getItem(AUTO_LAUNCH) === null) {
//   localStorage.setItem(AUTO_LAUNCH, '1');
//   window.__E__?.ipcRenderer.invoke('toggle-auto-launch', true);
// }

if (localStorage.getItem(NOTIFY) === null) {
  localStorage.setItem(NOTIFY, '1');
}

// localStorage.setItem(
//   'USER_TOKEN',
//   'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0MzIyMDZiYjJhYTQzYjU3MDZjYWFmYjhkZjQwM2EwNSIsImF1ZCI6IiIsImV4cCI6MTc1NjE3OTEyNSwiaWF0IjoxNzMwMjU5MTI1LCJjbGllbnRfaWQiOiJ3ZWIifQ._tLOAsJ3aAUAqFYSRplC6DNe8GUAfBBJaKAaetQS7OI',
// );
const app = createApp(App);
router.beforeEach((to) => {
  // 如果 meta 中定义了标题，更新 document.title
  // if (window.__E__?.isMac) {
  // }
});
app.use(router);

app.component('SvgIcon', SvgIcon);

app.mount('#app');
