class AudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.config = {
      mode: 'input',
      threshold: -50,
      attack: 0.001,
      release: 0.1,
    };
    this.lastSample = 0;
    this.gain = 0;
    this.port.onmessage = (event) => {
      if (event.data.type === 'init') {
        this.config = event.data.config;
      }
    };
  }

  process(inputs, outputs) {
    const input = inputs[0];
    const output = outputs[0];
    
    if (!input || !input[0]) return true;

    const inputChannel = input[0];
    const outputChannel = output[0];

    for (let i = 0; i < inputChannel.length; i++) {
      const sample = inputChannel[i];
      
      // 应用噪声门限
      if (Math.abs(sample) < Math.pow(10, this.config.threshold / 20)) {
        outputChannel[i] = 0;
        continue;
      }

      // 应用增益控制
      if (this.config.mode === 'input') {
        // 输入模式：降低增益以减少反馈
        outputChannel[i] = sample * 0.5;
      } else {
        // 输出模式：保持原始音量
        outputChannel[i] = sample;
      }

      // 应用平滑处理
      const attackCoeff = Math.exp(-1 / (sampleRate * this.config.attack));
      const releaseCoeff = Math.exp(-1 / (sampleRate * this.config.release));
      
      if (Math.abs(sample) > Math.abs(this.lastSample)) {
        this.gain = attackCoeff * this.gain + (1 - attackCoeff) * Math.abs(sample);
      } else {
        this.gain = releaseCoeff * this.gain + (1 - releaseCoeff) * Math.abs(sample);
      }
      
      this.lastSample = sample;
    }

    return true;
  }
}

registerProcessor('audio-processor', AudioProcessor); 