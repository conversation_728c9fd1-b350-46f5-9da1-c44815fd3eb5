// 定义回调函数类型
type VolumeCallback = (volume: number) => void;

// 定义配置接口
interface AudioConfig {
  fftSize?: number;
  smoothingTimeConstant?: number;
  echoCancellation?: boolean;
  noiseSuppression?: boolean;
  autoGainControl?: boolean;
}

// 定义音频检测器类
export default class AudioDetector {
  private audioContext: AudioContext | null;
  private mediaStream: MediaStream | null;
  private analyser: AnalyserNode | null;
  private microphone: MediaStreamAudioSourceNode | null;
  private isInitialized: boolean;
  private animationFrame: number | null;

  constructor(private config: AudioConfig = {}) {
    this.audioContext = null;
    this.mediaStream = null;
    this.analyser = null;
    this.microphone = null;
    this.isInitialized = false;
    this.animationFrame = null;
  }

  public async init(deviceId): Promise<void> {
    console.log(deviceId, 'deviceId');
    try {
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

      // 请求麦克风权限并获取音频流
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          deviceId: { exact: deviceId },
          echoCancellation: this.config.echoCancellation ?? true,
          noiseSuppression: this.config.noiseSuppression ?? true,
          autoGainControl: this.config.autoGainControl ?? true,
        },
      });

      // 创建分析器
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = this.config.fftSize ?? 1024;
      this.analyser.smoothingTimeConstant = this.config.smoothingTimeConstant ?? 0.8;

      // 连接麦克风到分析器
      this.microphone = this.audioContext.createMediaStreamSource(this.mediaStream);
      this.microphone.connect(this.analyser);

      this.isInitialized = true;
      console.log('音频检测器初始化成功');
    } catch (error) {
      console.error('初始化失败:', error);
      throw error;
    }
  }

  // 切换媒体设备操作
  public async changeDevice(deviceId) {
    await this.dispose();
    await this.init(deviceId);
  }

  // public getVolumeLevel(): number {
  //   if (!this.isInitialized || !this.analyser) return 0;

  //   const dataArray = new Uint8Array(this.analyser.frequencyBinCount);
  //   this.analyser.getByteFrequencyData(dataArray);

  //   // 计算音量平均值
  //   const average = dataArray.reduce((acc, val) => acc + val, 0) / dataArray.length;

  //   // 将音量映射到0-100范围
  //   return Math.round((average / 255) * 100);
  // }

  public getVolumeLevel(): number {
    if (!this.isInitialized || !this.analyser) return 0;

    const dataArray = new Uint8Array(this.analyser.frequencyBinCount);
    this.analyser.getByteFrequencyData(dataArray);

    // 1. 计算均值，但只考虑前半部分频率（降低高频影响）
    const effectiveLength = Math.floor(dataArray.length * 0.5);
    let sum = 0;
    let count = 0;

    for (let i = 0; i < effectiveLength; i++) {
      if (dataArray[i] > 0) {
        sum += dataArray[i];
        count++;
      }
    }

    if (count === 0) return 0;

    const average = sum / count;

    // 2. 调整灵敏度系数（降低整体音量）
    const sensitivity = 0.4; // 降低灵敏度（原来是1.8）
    const minThreshold = 0.15; // 稍微提高噪音阈值

    // 3. 使用改进的映射函数
    const normalizedValue = average / 255; // 归一化到0-1
    let volume = Math.pow(normalizedValue, 1.5) * 100 * sensitivity; // 使用幂函数压缩范围

    // 4. 应用阈值
    if (normalizedValue < minThreshold) {
      volume = 0;
    }

    // 5. 确保范围在0-100之间
    volume = Math.max(0, Math.min(100, volume));

    return Math.round(volume);
  }

  public startVolumeMonitor(callback: VolumeCallback): void {
    if (!this.isInitialized) return;

    const monitorFrame = (): void => {
      const volume = this.getVolumeLevel();
      callback(volume);
      this.animationFrame = requestAnimationFrame(monitorFrame);
    };

    this.animationFrame = requestAnimationFrame(monitorFrame);
  }

  public stopVolumeMonitor(): void {
    if (this.animationFrame !== null) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  public async dispose() {
    this.stopVolumeMonitor();
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach((track) => track.stop());
    }
    if (this.audioContext) {
      await this.audioContext.close();
    }
    this.isInitialized = false;
  }

  public get initialized(): boolean {
    return this.isInitialized;
  }
}
