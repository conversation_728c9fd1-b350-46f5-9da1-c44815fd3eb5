import { DEVICE_NAME, DEVICE_NAME_OLD } from '@/constants/audio';

export async function requestMediaPermissions() {
  try {
    // 同时请求麦克风和扬声器权限
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: true,
    });

    // 权限获取成功
    stream.getTracks().forEach((track) => track.stop()); // 停止媒体流
    return true;
  } catch (err) {
    // 权限被拒绝或发生错误
    console.error('Media permissions error:', err);
    return false;
  }
}

export async function getDevices() {
  const inputDevices = [];
  const outputDevices = [];
  let virtualSpeaker;
  let builtInInputDeviceName = '';
  let builtInInputDeviceId = '';
  let builtInOutputDeviceName = '';
  let builtInOutputDeviceId = '';

  let defaultInputDeviceName = '';
  let defaultInputDeviceId = '';
  let defaultOutputDeviceName = '';
  let defaultOutputDeviceId = '';

  let isVirtualDefault = false;
  try {
    const deviceInfos = await navigator.mediaDevices.enumerateDevices();
    console.log(deviceInfos, 'deviceInfos');
    for (let i = 0; i < deviceInfos.length; i++) {
      const deviceInfo = deviceInfos[i];
      if (deviceInfo.label.includes(DEVICE_NAME_OLD)) {
        if (deviceInfo.deviceId === 'default' && deviceInfo.kind === 'audiooutput') {
          isVirtualDefault = true;
        }
        continue;
      }
      if (deviceInfo.label.includes(DEVICE_NAME)) {
        if (deviceInfo.kind === 'audiooutput') {
          virtualSpeaker = {
            deviceId: deviceInfo.deviceId,
            label: deviceInfo.label,
          };
        }
        if (deviceInfo.deviceId === 'default') {
          isVirtualDefault = true;
        }

        continue;
      }
      const label = !window.__E__.isMac
        ? deviceInfo.label
        : deviceInfo.label.replace(/\s*\([^)]+\)/g, '');
      if (
        deviceInfo.deviceId &&
        // deviceInfo.deviceId !== 'default' &&
        deviceInfo.deviceId !== 'communications'
      ) {
        if (deviceInfo.kind === 'audioinput') {
          if (deviceInfo.deviceId === 'default') {
            defaultInputDeviceName = label.replace('Default - ', '');
            continue;
          }
          // 默认的设备从localStorage中取，取用户上次使用的设备
          const defaultId = localStorage.getItem('DEFAULT_DEVICE');
          console.log(defaultId, 'defaultId');
          console.log(deviceInfo.deviceId, 'deviceInfo.deviceId');
          console.log(defaultId === deviceInfo.deviceId, 'defaultId === deviceInfo.deviceId');
          if (defaultId && defaultId === deviceInfo.deviceId) {
            builtInInputDeviceId = defaultId;
            builtInInputDeviceName = label;
          } else if (!defaultId && deviceInfo.label.includes('Built-in')) {
            builtInInputDeviceId = deviceInfo.deviceId;
            builtInInputDeviceName = label;
          }
          inputDevices.push({
            deviceId: deviceInfo.deviceId,
            label,
          });
        }

        // if (deviceInfo.kind === 'audiooutput' && deviceInfo.deviceId === 'default') {
        if (deviceInfo.kind === 'audiooutput') {
          if (deviceInfo.deviceId === 'default') {
            defaultOutputDeviceName = label.replace('Default - ', '');
            continue;
          }
          if (deviceInfo.label.includes('Built-in')) {
            builtInOutputDeviceId = deviceInfo.deviceId;
            builtInOutputDeviceName = label;
          }
          outputDevices.push({
            deviceId: deviceInfo.deviceId,
            label,
          });
        }
      }
    }
    if (!defaultInputDeviceName) {
      defaultInputDeviceName = builtInInputDeviceName;
      defaultInputDeviceId = builtInInputDeviceId;
    }

    if (defaultInputDeviceName === builtInInputDeviceName) {
      defaultInputDeviceId = builtInInputDeviceId;
    } else if (defaultInputDeviceName) {
      inputDevices.some((device: any) => {
        if (device.label === defaultInputDeviceName) {
          defaultInputDeviceId = device.deviceId;
          return true;
        }
        return false;
      });
    }

    if (!defaultInputDeviceId) {
      defaultInputDeviceId = builtInInputDeviceId;
      defaultInputDeviceName = builtInInputDeviceName;
    }

    if (!defaultInputDeviceId) {
      defaultInputDeviceId = inputDevices[0]?.deviceId;
      defaultInputDeviceName = inputDevices[0]?.label;
    }

    if (!defaultOutputDeviceName) {
      defaultOutputDeviceName = builtInOutputDeviceName;
      defaultOutputDeviceId = builtInOutputDeviceId;
    }

    // 优先判断是否是同一个
    if (defaultOutputDeviceName === builtInOutputDeviceName) {
      defaultOutputDeviceId = builtInOutputDeviceId;
    } else if (defaultOutputDeviceName) {
      if (defaultOutputDeviceName) {
        outputDevices.some((device: any) => {
          if (device.label === defaultOutputDeviceName) {
            defaultOutputDeviceId = device.deviceId;
            return true;
          }
          return false;
        });
      }
    }
    // 取不到就取 builtIn
    if (!defaultOutputDeviceId) {
      defaultOutputDeviceId = builtInOutputDeviceId;
      defaultOutputDeviceName = builtInOutputDeviceName;
    }

    if (!defaultOutputDeviceId) {
      defaultOutputDeviceId = outputDevices[0]?.deviceId;
      defaultOutputDeviceName = outputDevices[0]?.label;
    }
  } catch (e) {
    console.log(e, 222);
  }
  console.log({
    inputDevices,
    outputDevices,
    virtualSpeaker,
    defaultInputDeviceId,
    defaultInputDeviceName,
    defaultOutputDeviceName,
    defaultOutputDeviceId,
    isVirtualDefault,
  });
  // if(window.__E__?.isMac) {
  //   defaultOutputDeviceName = defaultOutputDeviceName.replace(/\s*\([^)]+\)/g, '')
  //   defaultInputDeviceName = defaultInputDeviceName.replace(/\s*\([^)]+\)/g, '')
  // }

  return {
    inputDevices,
    outputDevices,
    virtualSpeaker,
    defaultInputDeviceId,
    defaultInputDeviceName,
    defaultOutputDeviceName,
    defaultOutputDeviceId,
    isVirtualDefault,
  };
}
export async function detectMicrophoneVolume(deviceId, callback) {
  try {
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: { deviceId: { exact: deviceId } },
    });
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const analyser = audioContext.createAnalyser();
    const microphone = audioContext.createMediaStreamSource(stream);
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    analyser.fftSize = 1024;
    microphone.connect(analyser);

    // 计算音量大小
    function updateVolume() {
      analyser.getByteFrequencyData(dataArray);

      // 计算平均音量
      const volume = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      console.log(volume, 'volumevolumevolume');

      // 映射音量值到 0-100 的范围
      const normalizedVolume = Math.min(100, (volume / 255) * 100);
      callback && callback(normalizedVolume);
      requestAnimationFrame(updateVolume);
    }

    updateVolume();
  } catch (error) {
    console.error('无法获取麦克风权限:', error);
  }
}

export class AudioDetector {
  constructor() {
    this.audioContext = null;
    this.mediaStream = null;
    this.analyser = null;
    this.microphone = null;
    this.isInitialized = false;
    this.animationFrame = null;
  }

  async init() {
    try {
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

      // 请求麦克风权限并获取音频流
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      // 创建分析器
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 1024;
      this.analyser.smoothingTimeConstant = 0.8;

      // 连接麦克风到分析器
      this.microphone = this.audioContext.createMediaStreamSource(this.mediaStream);
      this.microphone.connect(this.analyser);

      this.isInitialized = true;
      console.log('音频检测器初始化成功');
    } catch (error) {
      console.error('初始化失败:', error);
      throw error;
    }
  }

  // 获取当前音量级别 (0-100)
  getVolumeLevel() {
    if (!this.isInitialized) return 0;

    const dataArray = new Uint8Array(this.analyser.frequencyBinCount);
    this.analyser.getByteFrequencyData(dataArray);

    // 计算音量平均值
    const average = dataArray.reduce((acc, val) => acc + val, 0) / dataArray.length;

    // 将音量映射到0-100范围
    return Math.round((average / 255) * 100);
  }

  // 开始持续监测音量
  startVolumeMonitor(callback) {
    if (!this.isInitialized) return;

    const monitorFrame = () => {
      const volume = this.getVolumeLevel();
      callback(volume);
      this.animationFrame = requestAnimationFrame(monitorFrame);
    };

    this.animationFrame = requestAnimationFrame(monitorFrame);
  }

  // 停止监测
  stopVolumeMonitor() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  // 清理资源
  dispose() {
    this.stopVolumeMonitor();
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach((track) => track.stop());
    }
    if (this.audioContext) {
      this.audioContext.close();
    }
    this.isInitialized = false;
  }
}
