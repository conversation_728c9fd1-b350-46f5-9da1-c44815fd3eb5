// // Import the functions you need from the SDKs you need
// import { initializeApp } from 'firebase/app';
// import { getAnalytics, logEvent } from 'firebase/analytics';
// // TODO: Add SDKs for Firebase products that you want to use
// // https://firebase.google.com/docs/web/setup#available-libraries

// // Your web app's Firebase configuration
// // For Firebase JS SDK v7.20.0 and later, measurementId is optional
// const firebaseConfig = {
//   apiKey: 'AIzaSyCCqxMZRDZzurfEz2xV-dMYwUuHjtZshOI',
//   authDomain: 'plaud-35e0f.firebaseapp.com',
//   projectId: 'plaud-35e0f',
//   storageBucket: 'plaud-35e0f.appspot.com',
//   messagingSenderId: '346186524912',
//   appId: '1:346186524912:web:e78e6527904cff6e5d3a35',
//   measurementId: 'G-LTV9GFHSPV',
//   automaticDataCollectionEnabled: true,
// };

// // Initialize Firebase
// const app = initializeApp(firebaseConfig);
// const analytics = getAnalytics(app);
// const PREFIX = 'desktop_';
// export function log(data) {
//   const { type, ...params } = data;
//   if (!type) {
//     return;
//   }
//   logEvent(analytics, PREFIX + type, params);
// }
import * as Sentry from '@sentry/electron/renderer';
let fingerprint = 0;
export function log(data) {
  const { type, ...params } = data;
  if (!type) {
    return;
  }
  // logEvent(analytics, PREFIX + type, params);
  fingerprint++;
  Sentry.captureEvent({
    message: type,
    level: 'info',
    tags: data,
    extra: params,
    fingerprint: [fingerprint + ''],
  });
}

export function setUser(id) {
  console.log(id, 'id');
  Sentry.setUser({
    id, // 用户唯一ID
  });
}
