/**
 * 日期格式化
 */

/**
 * 格式化日期
 * @param  {String} fmt  格式化串，支持的格式化原子：y-年，M-月，d-天，h-时，m-分，s-秒，S-毫秒，q-季度
 * @param  {Date|Number} date 日期或日期毫秒数
 * @return {String}      返回格式化后的内容
 */
export default (fmt, date) => {
  date = new Date(date);
  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  };
  if (/(y+)/.test(fmt)) {
    let y = (date.getFullYear() + '').substr(4 - RegExp.$1.length);
    fmt = fmt.replace(RegExp.$1, y);
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      let d = RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length);
      fmt = fmt.replace(RegExp.$1, d);
    }
  }
  return fmt;
};
