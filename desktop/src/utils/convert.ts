import { FFmpeg } from '@ffmpeg/ffmpeg';
import { toBlobURL } from '@ffmpeg/util';
import { t } from 'i18next';
import { v4 as uuidv4 } from 'uuid';

let ffmpeg = new FFmpeg();
let aborted = false;
const baseURL = __DEV__ ? '' : './';

export async function convertWithFFmpegWasm(uint8Array1, callback?) {
  const uint8Array = await fixWebMHeader(uint8Array1);
  let length = uint8Array.length * 32;

  function onProgress(event) {
    let data = 0;
    if (event.progress > 0) {
      data = event.progress * 50;
    } else {
      data = (Math.abs(event.progress) / length).toFixed(2) * 50;
    }
    data = Math.min(data, 50);
    callback && callback({ type: 'progress', data });
  }

  try {
    if (!ffmpeg.loaded) {
      await ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
        // 增加内存配置
        // initialMemory: 256 * 1024 * 1024, // 初始内存 256MB（以字节为单位）
        // maximumMemory: 512 * 1024 * 1024, // 最大内存 512MB
      });
    }

    const uuid = uuidv4();
    const inputFile = `${uuid}.webm`;
    const outputFile = `${uuid}-output.mp3`;

    ffmpeg.on('progress', onProgress);
    // ffmpeg.on('log', (log) => {
    //   console.log('[FFmpeg log]', log); // 关键：输出所有日志
    // });
    await ffmpeg.writeFile(inputFile, uint8Array);
    const args = [
      '-i',
      inputFile,
      '-c:a',
      'libmp3lame',
      '-ar',
      '16000',
      '-ac',
      '1',
      '-b:a',
      '32k',
      outputFile,
    ];
    // 执行转换
    await ffmpeg.exec(args);
    ffmpeg.off('progress', onProgress);
    const output = await ffmpeg.readFile(outputFile);
    // 清理
    await ffmpeg.deleteFile(inputFile);
    await ffmpeg.deleteFile(outputFile);
    return new Blob([output.buffer], { type: 'audio/mp3' });
  } catch (e: any) {
    console.error(e, 'e');
    ffmpeg.off('progress', onProgress);
    throw Error(e);
  } finally {
    await ffmpeg.terminate(); // 终止实例
    ffmpeg.loaded = false; // 确保下次调用时重新加载
  }
}

function isWebMHeaderValid(uint8Array) {
  // 检查前 4 个字节是否为 EBML 标识符
  const ebmlHeader = [26, 69, 223, 163];
  for (let i = 0; i < 4; i++) {
    if (uint8Array[i] !== ebmlHeader[i]) {
      return false;
    }
  }
  // 可选：进一步检查 DocType 是否为 "webm"
  // DocType 通常在头部几十字节内，查找 0x42 0x87 后跟 "webm"
  return true;
}
// 一个基本的 WebM 文件头部（EBML + Segment 初始化）
const correctHeader = new Uint8Array([
  0x1a,
  0x45,
  0xdf,
  0xa3, // EBML 标识符
  0x01,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0x1f, // EBML 元素大小（示例值）
  0x42,
  0x86,
  0x81,
  0x01, // EBMLVersion = 1
  0x42,
  0xf7,
  0x81,
  0x01, // EBMLReadVersion = 1
  0x42,
  0xf2,
  0x81,
  0x04, // EBMLMaxIDLength = 4
  0x42,
  0xf3,
  0x81,
  0x08, // EBMLMaxSizeLength = 8
  0x42,
  0x82,
  0x84,
  0x77,
  0x65,
  0x62,
  0x6d, // DocType = "webm"
  0x42,
  0x87,
  0x81,
  0x02, // DocTypeVersion = 2
  0x42,
  0x85,
  0x81,
  0x02, // DocTypeReadVersion = 2
]);
async function fixWebMHeaderForce(uint8Array: any) {
  if (!isWebMHeaderValid(uint8Array)) {
    // 假设原始文件有有效数据，只是头部损坏
    // 用 correctHeader 替换头部，保留后续数据
    const preservedData = uint8Array.slice(correctHeader.length); // 从头部长度后开始保留
    const newUint8Array = new Uint8Array(correctHeader.length + preservedData.length);

    // 填充新数组
    newUint8Array.set(correctHeader, 0); // 设置正确头部
    newUint8Array.set(preservedData, correctHeader.length); // 追加原始数据
    return newUint8Array;
  }
  return uint8Array; // 头部正确，无需修改
}

export async function fixWebMHeader(uint8Array: Uint8Array) {
  try {
    if (isWebMHeaderValid(uint8Array)) {
      console.log('Header is valid, no fix needed.');
      return uint8Array;
    }

    console.log('Fixing WebM header...');
    // 尝试找到第一个 Segment（0x18 0x53 0xAB 0x84）
    const segmentMarker = new Uint8Array([0x18, 0x53, 0xab, 0x84]);
    let segmentOffset = -1;
    for (let i = 0; i < uint8Array.length - segmentMarker.length; i++) {
      if (
        uint8Array[i] === segmentMarker[0] &&
        uint8Array[i + 1] === segmentMarker[1] &&
        uint8Array[i + 2] === segmentMarker[2] &&
        uint8Array[i + 3] === segmentMarker[3]
      ) {
        segmentOffset = i;
        break;
      }
    }

    if (segmentOffset === -1) {
      console.log('Could not find valid Segment marker in WebM file.');
      return fixWebMHeaderForce(uint8Array);
    }

    // 使用动态长度构建头部
    const dataLength = uint8Array.length - segmentOffset;
    const headerWithSize = new Uint8Array([
      0x1a,
      0x45,
      0xdf,
      0xa3, // EBML 标识符
      0x01,
      0x00,
      0x00,
      0x00,
      0x00,
      0x00,
      0x00,
      dataLength & 0xff, // 动态长度（简化示例）
      0x42,
      0x86,
      0x81,
      0x01, // EBMLVersion = 1
      0x42,
      0xf7,
      0x81,
      0x01, // EBMLReadVersion = 1
      0x42,
      0xf2,
      0x81,
      0x04, // EBMLMaxIDLength = 4
      0x42,
      0xf3,
      0x81,
      0x08, // EBMLMaxSizeLength = 8
      0x42,
      0x82,
      0x84,
      0x77,
      0x65,
      0x62,
      0x6d, // DocType = "webm"
      0x42,
      0x87,
      0x81,
      0x02, // DocTypeVersion = 2
      0x42,
      0x85,
      0x81,
      0x02, // DocTypeReadVersion = 2
    ]);

    const newUint8Array = new Uint8Array(headerWithSize.length + dataLength);
    newUint8Array.set(headerWithSize, 0);
    newUint8Array.set(uint8Array.slice(segmentOffset), headerWithSize.length);

    return newUint8Array;
  } catch (e) {
    console.log(e, 'e');
  }
}
