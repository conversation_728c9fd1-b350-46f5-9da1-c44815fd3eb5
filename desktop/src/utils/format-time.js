/**
 * 时间格式化
 * hh:mm:ss
 */

import formatDate from './format-date';

/**
 * 格式化时间
 * @param  {String} fmt  格式化串，支持的格式化原子：h-时，m-分，s-秒，S-毫秒
 * @param  {Number} time 时间数字，毫秒数
 * @return {String}      返回格式化后的内容
 */
export default (fmt, time) => {
  let date = new Date();
  date.setHours(0);
  date.setMinutes(0);
  date.setSeconds(0);
  date.setMilliseconds(0);
  return formatDate(fmt, date.getTime() + time);
};
