/**
 * axios请求封装
 */

import axios from 'axios';
// 支持的方法
const methods = ['get', 'head', 'post', 'put', 'delete', 'options', 'patch'];
const paramsMethods = ['get', 'delete'];

import { TOKEN } from '@/constants/user';

import router from '@/router';

// 添加请求前缀
let API_PREFIX =
  window.__E__?.env === 'production' ? 'https://api.plaud.ai' : 'https://api-test.plaud.ai';

// 允许携带cookie
axios.defaults.withCredentials = false;

class Api {
  constructor() {
    methods.forEach(
      (method) =>
        ((this as any)[method] = (path: string, data = {}, config = {}, options = {}) =>
          new Promise((resolve, reject) => {
            this.doFetch(method, path, data, config, resolve, reject, options);
          })),
    );
  }
  /*
   * config参数可以包含headers,cancelToken,onUploadProgress等配置
   * */
  async doFetch(
    method: string,
    path: string,
    data: any,
    config: any,
    resolve: (value: unknown) => void,
    reject: (value: unknown) => void,
    options: any,
  ) {
    if (!config.headers) {
      config.headers = {};
    }
    let token = localStorage.getItem(TOKEN);

    // if (!token && location.href.includes('http')) {
    //   token = localStorage.getItem(TOKEN);
    // }
    if (token && token != '' && token != 'null') {
      config.headers['Authorization'] = token;
    }

    config.headers['edit-from'] = 'dekstop';

    if (config.headers.accept === 'text/event-stream') {
      Object.assign(config, { responseType: 'stream', adapter: 'fetch' });
    }

    data = paramsMethods.indexOf(method) !== -1 ? { params: data, ...config } : data;
    const _path = path.indexOf('http') === 0 ? path : `${API_PREFIX}${path}`;
    (axios as any)
      [method](_path, data, config)
      .then(async (response: any) => {
        const { data } = response;
        if (config.headers.accept === 'text/event-stream') {
          const stream = response.data;

          const reader = stream.pipeThrough(new TextDecoderStream()).getReader();
          resolve(reader);
        }

        if (data.status === 0) {
          let resolveData: any = {};
          Object.keys(data).forEach((key) => {
            if (key !== 'msg' && key !== 'status') {
              resolveData[key] = data[key];
            }
          });
          resolve(resolveData);
        } else if (data.code === 0) {
          resolve(data.data);
        } else {
          //TODO: 逻辑错误，按实际业务需求处理
          reject(data);
        }
      })
      .catch(async (error: Record<string, any>) => {
        if (error.response) {
          if (error.response.status === 401) {
            localStorage.removeItem(TOKEN);
            window.__E__?.ipcRenderer.send('change-token', false);

            // TODO: 401逻辑
            if (!options.noLogin) {
              router.push('/login');
            } else {
              reject(error.response.data);
            }
          } else {
            reject(error.response.data);
          }
        } else {
          reject(error);
        }
      });
  }
}

const api = new Api();

export default api;
