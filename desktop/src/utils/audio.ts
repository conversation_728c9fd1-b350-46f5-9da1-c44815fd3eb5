export function parseDuration(audioChunks: any): any {
  return new Promise(async (resolve, reject) => {
    const audioBlob = new Blob(audioChunks, { type: 'audio/webm;codecs=opus' });
    let arrayBuffer = await audioBlob.arrayBuffer();

    // 将结果转化为 ArrayBuffer

    // 创建 AudioContext 实例
    const audioContext = new window.AudioContext();

    // 使用 decodeAudioData 方法解码 ArrayBuffer
    audioContext.decodeAudioData(
      arrayBuffer,
      function (audioBuffer) {
        // 获取音频的时长
        const duration = audioBuffer.duration;
        resolve(duration * 1000);
      },
      function (error) {
        console.error('Error decoding audio data:', error);
        reject(error);
      },
    );
  });
}
