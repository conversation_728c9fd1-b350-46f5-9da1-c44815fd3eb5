import { uploadMP3 } from '@/apis/audio';
import { convertWithFFmpegWasm } from '@/utils/convert';
import { v4 as uuidv4 } from 'uuid';
import fromatDate from '@/utils/format-date';

import { i18n } from '@/locales/i18n';
const { t } = i18n.global;
class UploadQueue {
  constructor(options = {}) {
    // 并发数量，默认为3
    this.concurrentLimit = options.concurrentLimit || 1;

    // 任务队列
    this.queue = [];

    // 正在执行的任务数量
    this.runningTasks = 0;

    // 回调函数
    this.onProgress = options.onProgress || (() => {});
    this.onComplete = options.onComplete || (() => {});
    this.onError = options.onError || (() => {});
  }

  // 添加上传任务
  addTask(item) {
    const task = {
      ...item,
      status: 'pending',
      progress: 0,
      attempts: 0,
      maxAttempts: 3,
    };

    this.queue.push(task);
    window.__E__?.ipcRenderer.send('set-log', `add upload task, task.name: ${task.name}`);

    this.processQueue();
    return task;
  }

  deleteTask(task) {
    window.__E__?.ipcRenderer.send('set-log', `delete task, task.name: ${task.name} `);
    const index = this.queue.findIndex((item) => task.id === item.id);
    if (index > -1) {
      this.queue.splice(index, 1);
    }
  }

  // 处理队列
  processQueue() {
    // 如果正在运行的任务已经达到并发限制，直接返回
    if (this.runningTasks >= this.concurrentLimit) {
      return;
    }

    // 找到下一个待处理的任务
    const nextTask = this.queue.find((task) => task.status === 'pending');
    window.__E__?.ipcRenderer.send('set-log', `process queue `);

    if (nextTask) {
      this.runTask(nextTask);
    }
  }

  // 执行单个任务
  runTask(task) {
    window.__E__?.ipcRenderer.send('set-log', `run task, task.name: ${task.name}} `);
    // 标记任务为运行中
    task.status = 'running';
    this.runningTasks++;

    // 执行上传
    this.uploadWithProgress(task)
      .then((result) => {
        task.status = 'completed';
        this.onComplete(task, result);
        this.deleteTask(task);
      })
      .catch((error) => {
        window.__E__?.ipcRenderer.send(
          'set-log',
          `uploadWithProgressError: ${JSON.stringify(error)}`,
        );
        // 处理上传错误
        this.handleTaskError(task, error);
      })
      .finally(() => {
        // 减少正在运行的任务数
        this.runningTasks--;

        // 继续处理队列
        this.processQueue();
      });
  }

  // 带进度的上传
  async uploadWithProgress(task) {
    window.__E__?.ipcRenderer.send(
      'set-log',
      `uploadWithProgress, task.name: ${JSON.stringify(task.name)}`,
    );
    try {
      if (!task.formData) {
        // const arrayBuffer = await task.file.arrayBuffer();
        // console.log(task, 888888888);

        if (task.retry && !task.buffer) {
          // task.buffer = await readAsArrayBuffer(task.filePath);
          const fileBuffer = await window.__E__?.ipcRenderer.invoke('get-single-audio', task);
          if (!fileBuffer) {
            throw new Error('get_file_buffer_failed');
          }
          task.buffer = fileBuffer;
        }

        const uint8Array = new Uint8Array(Buffer.from(task.buffer));
        // console.log(task, 111);
        const data = await convertWithFFmpegWasm(uint8Array, (data) => {
          window.__E__?.ipcRenderer.send('set-log', `convertWithFFmpegWasm}`);
          if (data.type === 'progress') {
            console.log(data.data, 'process');
            task.progress = data.data;
            // task.progress = data.data * 50;
            this.onProgress(task);
          }
        });

        // 创建下载链接
        // const url = URL.createObjectURL(data);
        // const a = document.createElement('a');
        // a.href = url;
        // a.download = `${task.name}.mp3`; // 文件名
        // document.body.appendChild(a); // 添加到 DOM
        // a.click(); // 触发下载
        // document.body.removeChild(a); // 清理
        // URL.revokeObjectURL(url); // 释放内存

        const formData = new FormData();
        const now = +new Date(task.name);
        formData.append('file', data, task.name + '.mp3');
        formData.append('filename', task.name + '');
        formData.append('scene', '102');
        formData.append('start_time', now + '');
        formData.append('session_id', Math.floor(now / 1000) + '');
        formData.append('serial_number', task.id);
        task.formData = formData;
      } else {
        task.progress = 50;
        this.onProgress(task);
      }

      await uploadMP3(
        task.formData,
        (progressEvent) => {
          // 计算进度
          const percentCompleted =
            Math.round((progressEvent.loaded * 50) / progressEvent.total) + 50;

          task.progress = percentCompleted;

          // 调用进度回调
          this.onProgress(task);
        },
        {
          noLogin: true,
        },
      );
    } catch (e) {
      // 捕获所有错误并抛出
      throw new Error(e);
    }
  }

  // 处理任务错误
  handleTaskError(task, error) {
    // 增加尝试次数
    task.attempts++;

    // 判断是否可以重试
    if (task.attempts < task.maxAttempts) {
      task.status = 'pending';

      // 重新加入队列
      this.processQueue();
    } else {
      // 超过最大重试次数
      task.status = 'failed';
      this.onError(task, error);
      this.deleteTask(task);
    }
  }

  // 清空队列
  clearQueue() {
    // 清空队列
    this.queue = [];
    this.runningTasks = 0;
  }
}

export default UploadQueue;
