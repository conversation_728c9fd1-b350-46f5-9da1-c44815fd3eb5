<script setup lang="ts">
import { ref, onUnmounted, onMounted, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { i18n } from '@/locales/i18n';
import { getDevices, requestMediaPermissions } from '@/utils/devices';
import { AUTO_LAUNCH } from '@/constants/user';
import { getUserInfo } from '@/apis/user';
import { setUser } from '@/utils/firebase';
const route = useRoute();
const { t } = i18n.global;
const router = useRouter();
const currentStep = ref(0);
const isGranted = ref(false);
const isCloudGranted = ref(false);
const isStartAtLoginGranted = ref(false);
const isLogin = ref(false);

watch(
  () => route.query.from,
  (newFrom) => {
    if (newFrom === 'web') {
      currentStep.value = 2;
      getLoginStatus();
    }
  },
  { immediate: true },
);

window.__E__?.ipcRenderer.send('set-log', `on boadring init`);

const steps = computed(() => [
  {
    title: 'Achieve. Applaud.', // 静态标题，无需翻译
    action: t('onboarding.welcome.continue'),
  },
  {
    title: t('onboarding.features.title'),
    features: [
      t('onboarding.features.capture'),
      t('onboarding.features.detect'),
      t('onboarding.features.record'),
      t('onboarding.features.compatibility'),
    ],
    apps: ['zoom', 'teams', 'meet', 'webex', 'slack'],
    action: t('onboarding.features.login'),
    actionNext: t('onboarding.features.next'),
  },
  {
    title: t('onboarding.permissions.title'),
    subtitle: t('onboarding.permissions.subtitle'),
    permissions: [
      {
        icon: 'microphone',
        title: t('record_mic'),
        desc: t('onboarding.permissions.microphone.desc'),
        action: t('onboarding.permissions.enable'),
      },
      {
        icon: 'ppccloud',
        title: t('onboarding.permissions.cloud.title'),
        desc: t('onboarding.permissions.cloud.desc'),
        action: t('onboarding.permissions.enable'),
      },
      {
        icon: 'login',
        title: t('onboarding.permissions.autostart.title'),
        desc: t('onboarding.permissions.autostart.desc'),
        enabled: true,
      },
    ],
    action: t('onboarding.permissions.get_started'),
  },
]);

const handleNext = () => {
  if (currentStep.value < steps.value.length - 1) {
    // 如果不是最后一步，进入下一步
    currentStep.value++;
  }
};

const handleNextOrLogin = () => {
  if (isLogin.value) {
    handleNext();
  } else {
    console.log('handleLogin');
    console.log(window.__E__?.env, 'window.__E__?.env');
    const HOST =
      window.__E__?.env === 'production'
        ? 'https://app.plaud.ai/'
        : 'https://plaud-web-dist-test.pages.dev/web9/';
    window.__E__?.shell.openExternal(`${HOST}?from=desktop&position=onboarding`);
  }
};

const microphoneTimer = ref(null);
const startLoginTimer = ref(null);
// let isPPCopened = ref(false);
isCloudGranted.value =
  localStorage.getItem('ON_PPC') === 'false' || localStorage.getItem('ON_PPC') === null
    ? false
    : true;
const onOpenSettings = async (type) => {
  if (!type) {
    return;
  }

  if (type === 'microphone') {
    window.__E__?.ipcRenderer.send('check-mic-permission');
    // 清除可能存在的旧定时器
    if (microphoneTimer.value) {
      clearInterval(microphoneTimer.value);
    }

    // 设置新的轮询定时器
    microphoneTimer.value = setInterval(async () => {
      const res = await requestMediaPermissions();
      isGranted.value = res;
      if (res) {
        clearInterval(microphoneTimer.value);
      }
    }, 3000);
  }

  if (type === 'cloud') {
    isCloudGranted.value = true;
    window.__E__?.ipcRenderer.send('set-ppc', true);
    localStorage.setItem('ON_PPC', 'true');
  }

  if (type === 'start-at-login') {
    isStartAtLoginGranted.value = true;
    const result = await window.__E__?.ipcRenderer.invoke('toggle-auto-launch', true);
    // if (startLoginTimer.value) {
    //   clearInterval(startLoginTimer.value);
    // }
    // // 设置新的轮询定时器
    // startLoginTimer.value = setInterval(async () => {
    //   const result = await window.__E__?.ipcRenderer.invoke('toggle-auto-launch', true);
    //   isStartAtLoginGranted.value = result;
    // }, 3000);
  }
};

const handleGetStart = () => {
  window.__E__?.ipcRenderer.send('onboarding-finished');
};

const getLoginStatus = async () => {
  try {
    const res = await getUserInfo({ noLogin: true });
    isLogin.value = true;
    window.__E__?.ipcRenderer.send('change-token', true);
    window.__E__?.ipcRenderer.send('user-info', res);
    setUser(res?.data_user?.id);
  } catch (error) {
    isLogin.value = false;
    window.__E__?.ipcRenderer.send('change-token', false);
  }
};

getLoginStatus();

onMounted(() => {
  setTimeout(() => {
    requestMediaPermissions().then((res) => {
      console.log(res, 'res');
      isGranted.value = res;
    });
  }, 0);
  window.__E__?.store.get(AUTO_LAUNCH).then((launch) => {
    isStartAtLoginGranted.value = launch;
  });
});
// 组件卸载时清除定时器
onUnmounted(() => {
  if (microphoneTimer.value) {
    clearInterval(microphoneTimer.value);
    microphoneTimer.value = null;
  }
});
</script>

<template>
  <div
    class="min-h-screen w-screen text-white flex justify-center bg-[#1F1F1F] bg-[url('@/assets/icons/onBoarding/bg-low-quality.png')] bg-no-repeat bg-cover"
  >
    <div class="w-full h-full">
      <!-- Step 1: Welcome -->
      <div v-show="currentStep === 0" class="text-center">
        <img
          src="@/assets/icons/onBoarding/logo.png"
          alt="PLAUD.AI"
          class="w-16 h-16 mx-auto mb-4 mt-24"
        />
        <div class="flex items-center justify-center">
          <h1
            class="font-['Raleway'] text-4xl font-semibold bg-gradient-to-b from-white to-gray-500 bg-clip-text text-transparent"
          >
            {{ steps[0].title }}
          </h1>
          <img src="@/assets/icons/onBoarding/applaud.png" alt="PLAUD.AI" class="w-9 h-9 ml-2" />
        </div>

        <div class="w-full fixed bottom-10 flex justify-center items-center">
          <button
            @click="handleNext"
            class="text-[14px] min-w-[160px] font-semibold rounded-[8px] border-[0.5px] border-[#A3A3A3] bg-[rgba(81, 81, 81, 0.30)] backdrop-blur-[40px] px-8 py-2 mt-[160px] cursor-pointer hover:bg-[rgba(162,162,162,0.30)] gradient-border-button"
          >
            <span>{{ steps[0].action }}</span>
          </button>
        </div>
      </div>

      <!-- Step 2: Features -->
      <div v-show="currentStep === 1" class="mt-20 flex flex-col items-start justify-start">
        <div class="flex items-start justify-start">
          <div class="w-[406px] pl-14">
            <h2
              class="font-['Raleway'] text-[22px] font-semibold bg-gradient-to-b from-white mb-2 to-gray-500 bg-clip-text text-transparent"
            >
              {{ steps[1].title }}
            </h2>

            <div class="space-y-2">
              <div
                v-for="(feature, index) in steps[1].features"
                :key="index"
                class="flex items-start text-[#999999]"
              >
                <svg-icon name="check" class="w-5 h-5 mr-3 text-white" />
                <span class="text-[14px]">{{ feature }}</span>
              </div>
            </div>

            <div class="flex gap-3 mt-2 ml-8">
              <!-- <svg-icon name="check" class="w-5 h-5 mr-3 text-white" /> -->
              <svg-icon
                v-for="app in steps[1].apps"
                :key="app"
                :name="app"
                :alt="app"
                class="w-6 h-6"
              />
            </div>
          </div>
          <img
            class="w-[430px] fixed top-[28px] right-0"
            src="@/assets/icons/onBoarding/record.png"
            alt="preview"
          />
        </div>
        <div class="w-full fixed bottom-10 flex justify-center items-center">
          <button
            @click="handleNextOrLogin"
            class="text-[14px] min-w-[160px] font-semibold rounded-[8px] border-[0.5px] border-[#A3A3A3] bg-[rgba(81, 81, 81, 0.30)] backdrop-blur-[40px] px-8 py-2 cursor-pointer hover:bg-[rgba(162,162,162,0.30)] gradient-border-button"
          >
            {{ isLogin ? steps[1].actionNext : steps[1].action }}
          </button>
        </div>
      </div>

      <!-- Step 3: Permissions -->
      <div v-if="currentStep === 2" class="mt-20">
        <div class="text-center mb-4">
          <h2
            class="font-['Raleway'] text-[22px] font-semibold mb-2 bg-gradient-to-b from-white to-gray-500 bg-clip-text text-transparent"
          >
            {{ steps[2].title }}
          </h2>
          <p class="text-[#999999] text-[14px]">{{ steps[2].subtitle }}</p>
        </div>

        <div class="max-w-[560px] mx-auto">
          <!-- Record Microphone -->
          <div class="flex items-center justify-between pb-1 border-b border-[#333333]">
            <div class="flex items-start gap-3">
              <svg-icon :name="steps[2].permissions[0].icon" class="w-8 h-8 mt-1 text-white" />
              <div>
                <div class="text-[14px] text-white mb-1">{{ steps[2].permissions[0].title }}</div>
                <div class="text-[12px] text-[#999999]">{{ steps[2].permissions[0].desc }}</div>
              </div>
            </div>
            <!-- <div class="flex items-center text-[13px] text-[#4CAF50]">
              <span class="mr-1.5">●</span>
              {{ steps[2].permissions[0].status }}
            </div> -->
            <template v-if="isGranted">
              <div class="h-8 flex items-center justify-between">
                <div class="flex items-center text-green1">
                  <svg-icon name="tip" class="w-4 h-4"></svg-icon
                  ><span class="flex-1 text-[12px]">{{ steps[2].permissions[1].action }}</span>
                </div>
              </div>
            </template>
            <div v-else class="flex items-center">
              <div
                @click="onOpenSettings('microphone')"
                class="px-4 py-1 bg-white text-black rounded-[4px] text-[13px] cursor-pointer hover:bg-[rgba(255,255,255,0.70)]"
              >
                {{ steps[2].permissions[0].action }}
              </div>
            </div>
          </div>

          <!-- Notification -->
          <!-- <div class="flex items-center justify-between pt-2 pb-1 border-b border-[#333333]">
            <div class="flex items-start gap-3">
              <svg-icon :name="steps[2].permissions[1].icon" class="w-8 h-8 mt-1 text-white" />
              <div>
                <div class="text-[14px] text-white mb-1">{{ steps[2].permissions[1].title }}</div>
                <div class="text-[12px] text-[#999999]">{{ steps[2].permissions[1].desc }}</div>
              </div>
            </div>
            <template v-if="isNotificationGranted">
              <div class="h-8 flex items-center justify-between">
                <div class="flex items-center text-green1">
                  <svg-icon name="tip" class="w-4 h-4"></svg-icon
                  ><span class="flex-1 text-[12px]">{{ t('permission_granted') }}</span>
                </div>
              </div>
            </template>
            <div v-else class="flex items-center">
              <div
                @click="onOpenSettings('notification')"
                class="px-4 py-1 bg-white text-black rounded-[4px] text-[13px]"
              >
                {{ steps[2].permissions[1].action }}
              </div>
            </div>
          </div> -->

          <!-- Sync to PLAUD PRIVATE CLOUD -->
          <div class="flex items-center justify-between pt-2 pb-1 border-b border-[#333333]">
            <div class="flex items-start gap-3">
              <svg-icon :name="steps[2].permissions[1].icon" class="w-8 h-8 mt-1 text-white" />
              <div>
                <div class="text-[14px] text-white mb-1">{{ steps[2].permissions[1].title }}</div>
                <div class="text-[12px] text-[#999999]">{{ steps[2].permissions[1].desc }}</div>
              </div>
            </div>
            <template v-if="isCloudGranted">
              <div class="h-8 flex items-center justify-between">
                <div class="flex items-center text-green1">
                  <svg-icon name="tip" class="w-4 h-4"></svg-icon
                  ><span class="flex-1 text-[12px]">{{ steps[2].permissions[1].action }}</span>
                </div>
              </div>
            </template>
            <div v-else class="flex items-center">
              <div
                @click="onOpenSettings('cloud')"
                class="px-4 py-1 bg-white text-black rounded-[4px] text-[13px] cursor-pointer hover:bg-[rgba(255,255,255,0.70)]"
              >
                {{ steps[2].permissions[1].action }}
              </div>
            </div>
          </div>

          <!-- Start at Login -->
          <div class="flex items-center justify-between pt-2">
            <div class="flex items-start gap-3">
              <svg-icon :name="steps[2].permissions[2].icon" class="w-8 h-8 mt-1 text-white" />
              <div>
                <div class="text-[14px] text-white mb-1">{{ steps[2].permissions[2].title }}</div>
                <div class="text-[12px] text-[#999999]">{{ steps[2].permissions[2].desc }}</div>
              </div>
            </div>
            <template v-if="isStartAtLoginGranted">
              <div class="h-8 flex items-center justify-between">
                <div class="flex items-center text-green1">
                  <svg-icon name="tip" class="w-4 h-4"></svg-icon
                  ><span class="flex-1 text-[12px]">{{ steps[2].permissions[1].action }}</span>
                </div>
              </div>
            </template>
            <div v-else class="flex items-center">
              <div
                @click="onOpenSettings('start-at-login')"
                class="px-4 py-1 bg-white text-black rounded-[4px] text-[13px] cursor-pointer hover:bg-[rgba(255,255,255,0.70)]"
              >
                {{ steps[2].permissions[1].action }}
              </div>
            </div>
          </div>
        </div>

        <div class="w-full fixed bottom-10 flex justify-center">
          <button
            @click="handleGetStart"
            class="text-[14px] min-w-[160px] font-semibold rounded-[8px] border-[0.5px] border-[#A3A3A3] bg-[rgba(81, 81, 81, 0.30)] backdrop-blur-[40px] px-8 py-2 cursor-pointer hover:bg-[rgba(162,162,162,0.30)] gradient-border-button"
          >
            {{ steps[2].action }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.gradient-border-button {
  border: none;
  border-radius: 8px; /* 圆角 */
  cursor: pointer;
  background-color: rgba(81, 81, 81, 0.3);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  backdrop-filter: blur(80px);
}

.gradient-border-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 8px;
  padding: 0.5px;
  background: linear-gradient(to bottom, rgba(101, 101, 101, 1) 0%, rgba(101, 101, 101, 0) 100%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}
</style>
