<script setup>
import { ref, watch } from 'vue';
import { i18n } from '@/locales/i18n';
const { t } = i18n.global;
import { SwitchRoot, SwitchThumb } from 'radix-vue';

// 同步状态
const PPCEnabled = ref(false);
PPCEnabled.value =
  localStorage.getItem('ON_PPC') === 'false' || localStorage.getItem('ON_PPC') === null
    ? false
    : true;
const onPPC = (value) => {
  window.__E__?.ipcRenderer.send('set-ppc', value);
};

window.__E__?.ipcRenderer.on('ppc-change', (event, data) => {
  PPCEnabled.value = data;
  localStorage.setItem('ON_PPC', data);
});

// 监听语言变化
</script>
<template>
  <div class="bg-white py-10 px-5" style="overflow-y: auto; height: 100%">
    <div class="flex items-start gap-6 mb-4">
      <!-- <img src="@/assets/cloud-icon.png" alt="Cloud Icon" class="w-6 h-6" /> -->
      <svg-icon name="cloud" class="w-[60px] h-[60px]"></svg-icon>
      <div>
        <div class="text-[18px] font-semibold h-[28px] mb-1">
          {{ t('ppc_title') }}
        </div>
        <div class="text-xs font-normal text-[#858C9B] leading-[18px]">
          <p>
            {{ t('ppc_explain') }}
          </p>
          <p>{{ t('ppc_desc1') }}</p>
          <p>{{ t('ppc_desc2') }}</p>
        </div>
      </div>
    </div>
    <div class="h-[1px] bg-gray9 my-6"></div>
    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 mb-4">
      <div class="flex items-center">
        <div class="flex-1 text-[14px]">{{ t('sync_ppc') }}</div>
        <SwitchRoot
          id="airplane-mode"
          :checked="PPCEnabled"
          @update:checked="onPPC"
          class="w-[26px] h-[15px] border bg-black6 flexshadow-3 rounded-full relative data-[state=checked]:bg-black3 data-[state=checked]:border-black3 cursor-pointer"
        >
          <SwitchThumb
            class="block w-[13px] h-[13px] my-auto bg-white shadow-sm rounded-full transition-transform duration-100 translate-x-0 will-change-transform data-[state=checked]:translate-x-[11px]"
          />
        </SwitchRoot>
      </div>
    </div>
    <div class="text-xs font-normal text-[#858C9B] leading-[18px] mx-2.5">
      {{ t('ppc_effect') }}
    </div>
  </div>
</template>

<style scoped></style>
