<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  PopoverContent,
  PopoverPortal,
  PopoverRoot,
  PopoverTrigger,
  SwitchRoot,
  SwitchThumb,
} from 'radix-vue';
import { LANGUAGE, NOTIFY } from '@/constants/user';
import { getDevices, detectMicrophoneVolume } from '@/utils/devices';
import { HOST } from '@/constants/config';
import General from './general.vue';
import Audio from './audio.vue';
import About from './about.vue';
import Ppcguide from './ppcguide.vue';
import { i18n } from '@/locales/i18n';
import { getUserInfo } from '@/apis/user';
import { setUser } from '@/utils/firebase';
import { TOKEN } from '@/constants/user';
const { t } = i18n.global;
const params = new URLSearchParams(location.search);

let userInfo = ref({});
let isLogin = ref(false);

init();

async function init() {
  let token = localStorage.getItem(TOKEN);
  if (token) {
    getUserInfo()
      .then((res) => {
        userInfo.value = res;
        // console.log('userInfo.value:', res);
        window.__E__?.ipcRenderer.send('user-info', res);
        window.__E__?.ipcRenderer.send('change-token', true);
        setUser(res?.data_user?.id);
      })
      .catch((error) => {
        console.log('error', error);
      });
    isLogin.value = true;
  } else {
    isLogin.value = false;
  }
}

defineOptions({
  components: {
    General,
    Audio,
    About,
    Ppcguide,
  },
});

let componentName = ref(params.get('type') || 'General');
const list = [
  { type: 'General', name: 'general', selectedName: 'general-select' },
  { type: 'Audio', name: 'audio', selectedName: 'audio-select' },
  { type: 'Ppcguide', name: 'PPC', selectedName: 'PPC' },
  { type: 'About', name: 'about', selectedName: 'about-select' },
];

const onWeb = (url = HOST) => {
  window.__E__?.shell.openExternal(url);
};

const onSignOut = () => {
  window.__E__?.ipcRenderer.send('sign-out');
};

const goLogin = () => {
  // 关闭当前偏好页面，同时拉起web
  window.__E__?.shell.openExternal(`${HOST}?from=desktop`);
  setTimeout(() => {
    window.__E__?.ipcRenderer.send('close-preference');
  }, 100);
};

window.__E__?.ipcRenderer.on('sign-out-exec', (event, data) => {
  localStorage.removeItem(TOKEN);
  isLogin.value = false;
  window.__E__?.ipcRenderer.send('change-token', false);
});

window.__E__?.ipcRenderer.on('change-token', (event, data) => {
  isLogin.value = data;
});

const onActive = (type: string) => {
  console.log('onActive', type);
  componentName.value = type;
};

window.__E__?.ipcRenderer.on('switch-tab', (event, type) => {
  if (type) {
    onActive(type);
  }
});
</script>

<template>
  <div class="flex w-[100vw] h-[100vh] text-black7 overflow-hidden">
    <div class="px-1.5 py-5 bg-gray4 border-r border-r-gray2 flex flex-col justify-between">
      <div>
        <div
          v-for="(item, index) in list"
          class="w-[188px] h-8 mb-2 flex items-center px-3 gap-2 rounded-lg hover:bg-gray3 active:bg-gray2 cursor-pointer text-[#475467]"
          :class="{ 'bg-gray2': componentName === item.type }"
          @click="onActive(item.type)"
        >
          <svg-icon
            :name="componentName === item.type ? item.selectedName : item.name"
            class="w-5 h-5"
          />{{ t(item.name) }}
        </div>
      </div>
      <div v-if="isLogin && userInfo && userInfo.data_user" class="relative group">
        <div
          class="flex items-center text-sm font-medium focus:outline-none px-3 py-1 hover:bg-gray3 rounded-lg cursor-pointer"
        >
          <img
            v-if="userInfo.data_user?.avatar"
            class="h-8 w-8 img-border mr-2 focus:outline-none"
            :class="opened ? 'opacity-70' : ''"
            :src="userInfo.data_user?.avatar"
          />
          <span class="noimg mr-2 h-8 w-8" v-else>{{
            userInfo.data_user.nickname ? userInfo.data_user.nickname.substring(0, 1) : ''
          }}</span>
          <div class="w-[108px] flex flex-col leading-6 text-[14px] flex-1 mr-2">
            <div
              class="text-black3 h-[18px] leading-[20px] font-semibold max-w-28 overflow-hidden text-ellipsis whitespace-nowrap"
              :title="userInfo.data_user?.nickname"
            >
              {{ userInfo.data_user?.nickname }}
            </div>
            <div
              class="levelInfo text-gray7 h-4 items-center text-[12px] max-w-28 overflow-hidden text-ellipsis"
              v-if="userInfo.data_state?.membership_type"
              :class="[userInfo.data_state.membership_type]"
            >
              {{ userInfo.data_state.membership_type?.replace(/^\w/, (c) => c.toUpperCase()) }}
            </div>
          </div>
          <svg-icon name="arrow" class="w-2 h-8" />
        </div>
        <div
          class="absolute left-full ml-1 bottom-0 w-64 bg-white shadow-lg rounded-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300"
        >
          <div class="text-sm h-10 px-1.5 font-normal cursor-pointer">
            <div
              class="p-2 flex justify-between items-center hover:bg-gray3 rounded-md"
              @click="onWeb(`${HOST}?profile=open`)"
            >
              <div>{{ t('account_profile') }}</div>
              <svg-icon name="jump" class="w-6 h-6" />
            </div>
          </div>
          <div class="flex border-t border-t-gray9 w-full my-1"></div>
          <div
            class="text-sm flex justify-between items-center h-10 px-1.5 font-normal cursor-pointer"
          >
            <div class="p-2 hover:bg-gray3 rounded-md w-full" @click="onSignOut">
              {{ t('sign_out') }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="!isLogin"
        class="flex items-center text-sm font-medium focus:outline-none px-3 py-1 hover:bg-gray3 rounded-lg cursor-pointer"
        @click="goLogin"
      >
        <svg-icon name="default-avatar" class="w-8 h-8 mr-2" />
        <div class="font-medium">{{ t('sign_in') }}</div>
      </div>
    </div>
    <div class="flex-1 text-black3">
      <component :is="componentName" />
    </div>
  </div>
</template>
<style lang="less">
.levelInfo {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 11px;
  color: #858c9b;
  text-transform: capitalize;
  &.pro {
    background: linear-gradient(90deg, #6675ff 0%, #fe3ba6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }
  &.unlimited {
    background: linear-gradient(103deg, #775514 0%, #d8a545 50%, #5b3f0c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }
}
</style>
