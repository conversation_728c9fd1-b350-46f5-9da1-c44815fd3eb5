<script setup lang="ts">
import { ref, onUnmounted, onMounted } from 'vue';
import { PopoverContent, PopoverPortal, PopoverRoot, PopoverTrigger } from 'radix-vue';
import { getDevices } from '@/utils/devices';
import AudioDetector from '@/utils/audio-detector';
import { i18n } from '../../locales/i18n';
const { t } = i18n.global;

let audio: any;
let volume = ref(0);
const isMac = ref(window.__E__?.isMac);
let devices = ref<any>({ output: [], input: [] });
let deviceId = ref({ input: '', output: '' });
let deviceName = ref({ input: '', output: '' });
let detector = new AudioDetector({
  fftSize: 512, // 减小 FFT 大小可以提高响应速度
  smoothingTimeConstant: 0.5, // 降低平滑系数，使响应更快
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: false, // 关闭自动增益，让音量显示更真实
});

let microphoneOpend = ref(false);
let speakerOpend = ref(false);

const onMicrophoneOpend = (value) => {
  microphoneOpend.value = value;
};

const onSpeakerOpend = (value) => {
  if (value) {
    if (!audio) {
      audio = new Audio('./test.mp3');
      audio.loop = true;
    }
    audio.play();
  } else if (audio) {
    audio.pause();
    audio.currentTime = 0;
  }
  speakerOpend.value = value;
};

init();
async function init() {
  const { inputDevices, outputDevices, defaultInputDeviceId, defaultInputDeviceName } =
    await getDevices();
  devices.value.input = inputDevices;
  devices.value.output = outputDevices;
  if (devices.value.input.length > 0) {
    deviceId.value.input = defaultInputDeviceId;
    deviceName.value.input = defaultInputDeviceName ? defaultInputDeviceName : t('not_record_mic');
    console.log('before detector init');
    if (!detector.initialized) {
      await detector.init(deviceId.value.input);
    }

    volume.value = 0;
    detector.startVolumeMonitor((data) => {
      volume.value = data;
    });
  }

  if (devices.value.output.length > 0) {
    deviceId.value.output = devices.value.output[0].deviceId;
    deviceName.value.output = devices.value.output[0].label;
  }
}

const onOpenSettings = (type) => {
  if (!type) {
    return;
  }
  window.__E__?.ipcRenderer.send('open-settings', type);
};

const onSelect = async (type: 'input' | 'output', audio: any = {}) => {
  if (audio.deviceId === deviceId.value[type]) {
    return;
  }
  microphoneOpend.value = false;
  deviceId.value[type] = audio.deviceId;
  if (!audio.label) {
    deviceName.value[type] = t('not_record_mic');
  } else {
    deviceName.value[type] = audio.label;
  }
  await detector.changeDevice(audio.deviceId);
  volume.value = 0;
  window.__E__?.ipcRenderer.send('change-audio-setting', { ...audio });
  localStorage.setItem('DEFAULT_DEVICE', audio.deviceId || '');
  detector.startVolumeMonitor((data) => {
    // console.log(data, volume.value, 'onSelect');
    volume.value = data;
  });
};

onMounted(() => {
  window.__E__?.ipcRenderer.on('set-audio-record', (event, data) => {
    onSelect('input', data);
  });
});

onUnmounted(() => {
  detector.dispose();
  if (audio) {
    audio.pause();
    audio = null;
  }
});
</script>

<template>
  <div class="px-5 py-10" style="overflow-y: auto; height: 100%">
    <div class="font-semibold text-[14px] text-gray7 mb-1">{{ t('system_permission') }}</div>
    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 pb-1.5 mb-6 text-[14px]">
      <template v-if="devices.input.length > 0">
        <div class="h-8 flex items-center justify-between">
          <div>{{ t('record_mic') }}</div>
          <div class="flex items-center text-green1">
            <svg-icon name="tip" class="w-4 h-4"></svg-icon
            ><span class="flex-1 text-[12px]">{{ t('permission_granted') }}</span>
          </div>
        </div>
      </template>
      <div v-else class="flex items-center">
        <div class="flex-1">{{ t('use_microphone') }}</div>
        <div
          @click="onOpenSettings('microphone')"
          class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer"
        >
          {{ t('grant_permission') }}
        </div>
      </div>
      <div class="h-[1px] bg-gray9 my-1.5"></div>
      <template v-if="devices.output.length > 0">
        <div class="flex items-center">
          <div class="flex-1">{{ t('record_audio') }}</div>

          <div class="h-8 flex items-center text-green1">
            <svg-icon name="tip" class="w-4 h-4"></svg-icon
            ><span class="flex-1 text-[12px]">{{ t('permission_granted') }}</span>
          </div>
        </div>
      </template>
      <div v-else class="flex items-center">
        <div class="flex-1">{{ t('use_speaker') }}</div>
        <div
          @click="onOpenSettings('sound')"
          class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer"
        >
          {{ t('grant_permission') }}
        </div>
      </div>
    </div>

    <div class="font-semibold text-[14px] text-gray7 mb-1">{{ t('record_mic') }}</div>
    <div
      class="bg-gray10 border border-gray9 rounded-md p-2.5 pb-1.5 mb-6 text-[14px] flex items-center justify-between"
    >
      <PopoverRoot :open="microphoneOpend" @update:open="onMicrophoneOpend">
        <PopoverTrigger
          class="max-w-[280px] h-8 gap-1.5 text-gray7 flex items-center justify-end pr-1.5 text-sm rounded-md hover:bg-gray3 active:bg-gray2 focus:outline-none"
          aria-label="Update dimensions"
        >
          <div
            class="overflow-hidden text-ellipsis whitespace-nowrap pl-1.5"
            :title="deviceName.input"
          >
            {{ deviceName.input }}
          </div>
          <svg-icon name="pop-up" class="w-4 h-4"></svg-icon>
        </PopoverTrigger>
        <PopoverPortal>
          <PopoverContent
            side="bottom"
            :side-offset="0"
            align="start"
            class="z-[99999] flex max-h-[200px] min-w-[240px] overflow-hidden overflow-y-auto text-[16px] flex-col rounded-lg bg-white px-1.5 py-2 shadow-1 device-container"
          >
            <!-- <div class="flex px-2 pt-2 text-[12px] text-gray7">{{ t('microphone') }}</div> -->

            <div class="flex items-center">
              <button type="button" class="group text-sm flex-1">
                <div
                  class="custom-radio flex p-2 cursor-pointer rounded hover:bg-gray3 active:bg-gray2"
                  :class="deviceId.input ? 'transparent-radio' : ''"
                >
                  <input
                    id="record-switch"
                    :checked="!deviceId.input"
                    name="record-switch"
                    type="radio"
                    class="cursor-pointer"
                  />
                  <label
                    @click="onSelect('input')"
                    for="record-switch"
                    class="cursor-pointer text-left"
                    >{{ t('not_record_mic') }}
                  </label>
                </div>
              </button>
            </div>
            <div class="flex border-t border-t-gray9 w-full my-2"></div>

            <button
              v-for="(audio, index) in devices.input"
              :key="index"
              type="button"
              class="group px-1 my-0.5 text-sm"
            >
              <div
                class="custom-radio flex p-2 cursor-pointer rounded hover:bg-gray3 active:bg-gray2"
                @click="onSelect('input', audio)"
              >
                <input
                  :id="audio.deviceId"
                  :checked="deviceId.input === audio.deviceId"
                  name="radio"
                  type="radio"
                  class="cursor-pointer"
                />
                <label :for="audio.value" class="cursor-pointer text-left">{{ audio.label }}</label>
              </div>
            </button>
          </PopoverContent>
        </PopoverPortal>
      </PopoverRoot>
      <div class="flex gap-1.5">
        <div
          v-for="index in 10"
          class="w-3 h-5 rounded-sm bg-gray6"
          :class="{ 'bg-green1': index < volume }"
        ></div>
      </div>
    </div>

    <template v-if="isMac">
      <div class="font-semibold text-[14px] text-gray7 mb-1">{{ t('recording_tips') }}</div>
      <div class="bg-gray10 border border-gray9 rounded-md p-3 text-[14px]">
        <!-- 1. Before Meeting -->
        <div class="mb-4">
          <div class="font-medium mb-1">1. {{ t('before_meeting') }}</div>
          <div class="text-gray7 pl-3 text-[14px]">• {{ t('open_plaud_tip') }}</div>
        </div>

        <!-- 2. When Joining a Meeting -->
        <div class="mb-4">
          <div class="font-medium mb-1">2. {{ t('when_joining_meeting') }}</div>
          <div class="text-gray7 pl-3">
            <div class="mb-1 text-[14px]">
              • {{ t('in_setting') + ' ' + t('select') + ' ' }}
              <span class="font-bold text-[#858C9B]">{{ t('as_speaker') }}</span>
            </div>
            <div class="text-[14px]">• {{ t('select_mic_device') }}</div>
          </div>
          <img
            src="@/assets/icons/setting/meeting.png"
            alt="Meeting Setup"
            class="mt-2 rounded-lg ml-5 w-[224px] h-[174px]"
          />
        </div>

        <!-- 3. Confirm Recording Status -->
        <div>
          <div class="font-medium mb-1">3. {{ t('confirm_recording_status') }}</div>
          <div class="text-gray7 pl-3 text-[14px]">• {{ t('check_recording_status') }}</div>
          <img
            src="@/assets/icons/setting/recording.png"
            alt="Recording Status"
            class="mt-2 rounded-lg ml-5 w-[224px]"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="less">
.device-container::-webkit-scrollbar {
  /* 隐藏默认的滚动条 */
  -webkit-appearance: none;
}

.device-container::-webkit-scrollbar:vertical {
  /* 设置垂直滚动条宽度 */
  width: 4px;
}

.device-container::-webkit-scrollbar-thumb {
  /* 滚动条的其他样式定制，注意，这个一定也要定制，否则就是一个透明的滚动条 */
  border-radius: 8px;
  border: 2px solid #d0d5dd;
  background-color: #d0d5dd;
}
</style>
