<script setup lang="ts">
import { ref } from 'vue';
import {
  <PERSON>over<PERSON>ontent,
  PopoverPortal,
  PopoverRoot,
  PopoverTrigger,
  SwitchRoot,
  SwitchThumb,
} from 'radix-vue';
import { LANGUAGE, NOTIFY } from '@/constants/user';
import { getDevices, detectMicrophoneVolume } from '@/utils/devices';
import { HOST, USER_AGREEMENT, PRIVACY_POLICY } from '@/constants/config';
import { i18n } from '@/locales/i18n';
const { t } = i18n.global;

let activeInedx = ref(0);
let version = ref('');
let updateMessage = ref('');

const socialList = [
  { name: 'x', link: 'https://x.com/PLAUDAI' },
  { name: 'facebook', link: 'https://www.facebook.com/plaudai/' },
  { name: 'instagram', link: 'https://www.instagram.com/plaud_official/' },
  { name: 'tiktok', link: 'https://www.tiktok.com/@plaud.ai' },
];

const onWeb = (url = HOST) => {
  window.__E__?.shell.openExternal(url);
};
async function init() {
  version.value = await window.__E__?.ipcRenderer.invoke('get-version');
  updateMessage.value = await window.__E__?.ipcRenderer.invoke('get-update-message');
}
init();

const checkUpdate = async () => {
  if (!updateMessage.value) {
    window.__E__?.ipcRenderer.send('check-update');
  }
};

// mainWindow.webContents

window.__E__?.ipcRenderer.on('update-message', (event, data) => {
  updateMessage.value = data;
});
</script>

<template>
  <div class="flex flex-col pt-10" style="overflow-y: auto; height: 100%">
    <div class="flex gap-6 mb-6 items-center px-5">
      <svg-icon name="logo" class="w-[60px] h-[60px]"></svg-icon>
      <div class="flex-1">
        <div class="font-semibold text-black3 text-[28px] flex items-center justify-start gap-1">
          <div>PLAUD</div>
          <svg-icon name="beta" class="w-7 h-3 text-black7" />
        </div>
        <div class="flex justify-between">
          <div v-if="version" class="text-gray7">{{ version }}</div>
          <div
            v-if="!updateMessage"
            @click="checkUpdate()"
            class="h-6 px-1.5 text-[14px] flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
          >
            {{ updateMessage || t('check_update') }}
          </div>
          <div
            v-else
            class="h-6 px-1.5 text-[14px] flex items-center select-none text-black3 rounded-md bg-white"
          >
            {{ updateMessage || t('check_update') }}
          </div>
        </div>
      </div>
    </div>
    <div class="h-[1px] bg-gray9 mb-6 text-[18px] mx-5"></div>
    <div class="flex gap-6 mb-6 mx-5">
      <div>
        <div class="font-semibold text-[14px] text-gray7">{{ t('empower') }}</div>
        <div class="text-gray7 text-[12px]">
          {{ t('empower_tip') }}
        </div>
      </div>
    </div>
    <div class="h-[1px] bg-gray9 mb-6 text-[18px] mx-5"></div>
    <div class="h-[60px] flex-1 flex flex-col px-5 mb-6">
      <div class="font-semibold text-[14px] text-gray7 mb-4">{{ t('follow_us') }}</div>
      <div class="flex gap-6 flex-1">
        <svg-icon
          v-for="item in socialList"
          :name="item.name"
          @click="onWeb(item.link)"
          class="w-6 h-6 cursor-pointer"
        ></svg-icon>
      </div>
    </div>
    <div class="flex items-center text-[14px] border border-gray9 h-12 bg-gray10 px-4 gap-1.5">
      <div
        @click="onWeb()"
        class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
      >
        PLAUD WEB
      </div>
      <div class="flex-1"></div>
      <div
        @click="onWeb(USER_AGREEMENT)"
        class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
      >
        {{ t('user_agreement') }}
      </div>
      <div
        @click="onWeb(PRIVACY_POLICY)"
        class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
      >
        {{ t('privacy_policy') }}
      </div>
    </div>
  </div>
</template>
