<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, toRaw } from 'vue';
import {
  PopoverContent,
  PopoverPortal,
  PopoverRoot,
  PopoverTrigger,
  SwitchRoot,
  SwitchThumb,
  CollapsibleRoot,
  CollapsibleContent,
  CollapsibleTrigger,
} from 'radix-vue';
import { LANGUAGE, NOTIFY, AUTO_LAUNCH, AUTO_RECORD, APPS } from '@/constants/user';
import { i18n } from '@/locales/i18n';
const { t } = i18n.global;
import { log } from '@/utils/firebase';

const isMac = ref(window.__E__?.isMac);

const languageList = [
  { value: 'en_US', name: 'English' },
  { value: 'es_ES', name: 'Espa<PERSON><PERSON>' },
  { value: 'fr_FR', name: 'Français' },
  { value: 'de_DE', name: '<PERSON><PERSON><PERSON>' },
  { value: 'it_IT', name: 'Italiano' },
  { value: 'ja_<PERSON>', name: '日本語' },
  { value: 'ko_KR', name: '한국어' },
  { value: 'pt_PT', name: 'Português' },
  { value: 'zh_CN', name: '中文（简体）' },
  { value: 'zh_TW', name: '中文（繁體）' },
];
const currentLanguage = localStorage.getItem(LANGUAGE) || 'en_US';
let language = ref('');

let launchEnabled = ref(false);
let showInDock = ref(true);
let notifyEnabled = ref(localStorage.getItem(NOTIFY) !== '0');
let inited = ref(false);
let autoRecordEnabled = ref(false);
let isAuto = ref(false);

let apps = ref([]);
init();
async function init() {
  try {
    const [lng = 'en_US', launch, autoRecord] = await Promise.all([
      window.__E__?.store.get(LANGUAGE),
      window.__E__?.store.get(AUTO_LAUNCH),
      window.__E__?.store.get(AUTO_RECORD),
    ]);
    autoRecordEnabled.value = autoRecord;
    console.log(autoRecordEnabled, 'apps');
    language.value = languageList.find((item) => item.value === lng).name;
    launchEnabled.value = launch;
    showInDock.value = !(localStorage.getItem('SHOWDOCK') === '0');
    window.__E__?.ipcRenderer.send('show_dock', showInDock.value);
  } catch (e) {
    console.log(e);
  } finally {
    inited.value = true;
  }

  apps.value = (await window.__E__?.store.get(APPS)) || [];
  if (window.__E__?.isMac) {
    apps.value = apps.value.filter((app) => {
      return app.mac;
    });
  } else {
    apps.value = apps.value.filter((app) => {
      return app.win && app.name;
    });
  }
  console.log(apps.value, 'apps');
}

const onLaunch = async () => {
  launchEnabled.value = !launchEnabled.value;
  if (launchEnabled.value) {
    log({ type: 'open_auto_start' });
  } else {
    log({ type: 'close_auto_start' });
  }
  const result = await window.__E__?.ipcRenderer.invoke('toggle-auto-launch', launchEnabled.value);
  // launchEnabled.value = result;
};

const onShowInDock = () => {
  showInDock.value = !showInDock.value;
  localStorage.setItem('SHOWDOCK', showInDock.value ? '1' : '0');
  window.__E__?.ipcRenderer.send('show_dock', showInDock.value);
};

let languageOpend = ref(false);

const onLanguageOpend = (value) => {
  languageOpend.value = value;
};

const onLanguage = (item) => {
  if (language.value === item.name) {
    return;
  }
  language.value = item.name;
  window.__E__?.store.set(LANGUAGE, item.value);
  i18n.global.locale.value = item.value;
  languageOpend.value = false;
  window.__E__?.ipcRenderer.send('update-settings', { type: LANGUAGE, data: item.value });
};

const onNotify = (item) => {
  notifyEnabled.value = !notifyEnabled.value;
  localStorage.setItem(NOTIFY, notifyEnabled.value ? 1 : 0);
};

const onOpenSettings = (type) => {
  if (!type) {
    return;
  }
  window.__E__?.ipcRenderer.send('open-settings', type);
};

const onExit = () => {
  window.__E__?.ipcRenderer.send('app-exit');
};

const onMail = () => {
  window.location.href = 'mailto:<EMAIL>';
};

const onConcat = () => {
  window.__E__?.ipcRenderer.send('open-help');
};

const keydownHandler = (event) => {
  const modifier = window.__E__?.isMac ? event.metaKey : event.ctrlKey;
  if (modifier && event.key === 'q') {
    event.preventDefault();
    console.log('快捷键 Command+Q 或 Ctrl+q 被按下');
    window.__E__?.ipcRenderer.send('app-exit');
  }
};

const setupKeyboardShortcuts = () => {
  document.addEventListener('keydown', keydownHandler);
};
const removeKeyboardShortcuts = () => {
  document.removeEventListener('keydown', keydownHandler);
};
onMounted(async () => {
  window.__E__?.isMac && setupKeyboardShortcuts();
});
onUnmounted(() => {
  window.__E__?.isMac && removeKeyboardShortcuts();
});

const onAutoRecordToggle = (value: boolean) => {
  if (!value) {
    // 关闭的话不需要请求权限
    autoRecordEnabled.value = value;
    window.__E__?.store.set(AUTO_RECORD, value);
    return;
  }
  // 处理自动录制开关逻辑
  window.__E__?.ipcRenderer.send('open-auto-record', 'all');
};
window.__E__?.ipcRenderer.on('open-auto-record-switch', (event, data) => {
  if (data === 'all') {
    autoRecordEnabled.value = true;
    window.__E__?.store.set(AUTO_RECORD, true);
    return;
  }
  if (data === 'teams') {
    apps.value.forEach((app) => {
      if (app.key === 'teams') {
        app.enabled = true;
        window.__E__?.store.set(APPS, JSON.parse(JSON.stringify(toRaw(apps.value))));
      }
    });
  }
});

const onAppToggle = (app, index) => {
  // if (app.key === 'zoom') {
  //   return;
  // }
  if (app.key !== 'teams' || app.enabled) {
    app.enabled = !app.enabled;
    window.__E__?.store.set(APPS, JSON.parse(JSON.stringify(toRaw(apps.value))));
    return;
  }

  window.__E__?.ipcRenderer.send('open-auto-record', 'teams');
};

let uploadPercent = ref(0);
let filePath = ref('');
/*
 * 日志下载流程状态
 * 0: 未开始，显示 上传按钮 Share
 * 1: 压缩中，显示 Loading...
 * 2: 压缩成功，显示 Success
 * 3: 压缩失败，显示 Retry 按钮
 * 日志上传流程状态（无后台资源暂不开发）
 * 4: 等待上传, 显示 Waiting to upload...
 * 5: 上传中，显示 Uploading ${percent}%
 * 6: 上传完成，显示 Success
 * 7: 上传失败，显示 Retry 按钮
 */
let uploadStatus = ref(0);
const onShareLogs = () => {
  window.__E__?.ipcRenderer.send('start-zip');
};
const onRetry = (status) => {
  if (status === 3) {
    // 压缩失败
    window.__E__?.ipcRenderer.send('start-zip');
  } else if (status === 7) {
    console.log('filePath.value:', filePath.value);
    // 上传失败
    window.__E__?.ipcRenderer.send('start-upload', filePath.value);
  }
};

// 监听压缩进度事件
window.__E__?.ipcRenderer.on('zip-progress', (event, data) => {
  // console.log('zip-progress', data);
  if (data.type === 'progress') {
    uploadStatus.value = 1;
  } else if (data.type === 'done') {
    uploadStatus.value = 2;
    window.__E__?.ipcRenderer.send('open-folder', data?.path);
  } else if (data.type === 'error') {
    uploadStatus.value = 3;
  }
});

// 监听上传进度事件
// window.__E__?.ipcRenderer.on('upload-progress', (event, data) => {
//   console.log('upload-progress', data);
//   if (data.type === 'progress') {
//     uploadPercent.value = data.message;
//     uploadStatus.value = 5;
//   } else if (data.type === 'done') {
//     uploadPercent.value = data.message;
//     uploadStatus.value = 6;
//   } else if (data.type === 'error') {
//     uploadStatus.value = 7;
//     filePath.value = data.data?.filePath;
//   }
// });
</script>
<template>
  <div class="px-5 py-10 text-sm" style="overflow-y: auto; height: 100%" v-if="inited">
    <div class="font-semibold text-[14px] text-[#858C9B] mb-1">
      {{ t('is_recording') }}
    </div>
    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 mb-6">
      <div class="flex items-center">
        <div class="flex-1 mr-[66px]">
          <div class="text-[14px] text-black3">{{ t('automatic_meeting_recording') }}</div>
          <div class="text-gray7 text-[12px] mt-1">
            {{ t('auto_recording_desc') }}
          </div>
        </div>
        <SwitchRoot
          :checked="autoRecordEnabled"
          @update:checked="onAutoRecordToggle"
          class="w-[26px] h-[15px] border bg-black6 flexshadow-3 rounded-full relative data-[state=checked]:bg-black3 data-[state=checked]:border-black3 cursor-pointer"
        >
          <SwitchThumb
            class="block w-[13px] h-[13px] my-auto bg-white shadow-sm rounded-full transition-transform duration-100 translate-x-0 will-change-transform data-[state=checked]:translate-x-[11px]"
          />
        </SwitchRoot>
      </div>

      <div class="h-[1px] bg-gray9 my-2.5" v-if="autoRecordEnabled"></div>

      <CollapsibleRoot v-model:open="isAuto" v-if="autoRecordEnabled">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between cursor-pointer py-1">
            <span class="text-[14px] text-black3">{{ t('supported_meeting_apps') }}</span>
            <svg-icon :name="isAuto ? 'arrow-up' : 'arrow-down'" class="w-4 h-4" />
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div class="flex flex-col mt-2 bg-black/[0.03] rounded-lg">
            <template v-for="(app, index) in apps">
              <div
                :key="app.key"
                v-if="app.icon"
                class="flex items-center justify-between p-3"
                :class="{
                  'border-b border-gray9': index !== apps.length - 1,
                }"
              >
                <div class="flex flex-col gap-1">
                  <div class="flex items-center gap-2">
                    <svg-icon :name="app.icon" class="w-5 h-5" />
                    <div>
                      <span>{{ app.name }}</span>
                      <span v-if="app.tip" class="text-[12px] text-gray7 ml-1.5">{{
                        t(app.tip)
                      }}</span>
                      <div v-if="app.desc" class="text-[12px] text-gray7">
                        {{ t(app.desc) }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <SwitchRoot
                    :checked="!!app.enabled"
                    @update:checked="onAppToggle(app, index)"
                    class="w-[26px] h-[15px] border bg-black6 flexshadow-3 rounded-full relative data-[state=checked]:bg-black3 data-[state=checked]:border-black3 cursor-pointer"
                  >
                    <SwitchThumb
                      class="block w-[13px] h-[13px] my-auto bg-white shadow-sm rounded-full transition-transform duration-100 translate-x-0 will-change-transform data-[state=checked]:translate-x-[11px]"
                    />
                  </SwitchRoot>
                </div>
              </div>
            </template>
          </div>
        </CollapsibleContent>
      </CollapsibleRoot>
    </div>

    <div class="font-semibold text-[14px] text-[#858C9B] mb-1">{{ t('notification') }}</div>
    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 mb-6">
      <div class="flex items-center">
        <div class="flex-1">{{ t('notify_me') }}</div>
        <SwitchRoot
          id="airplane-mode"
          :checked="notifyEnabled"
          @update:checked="onNotify"
          class="w-[26px] h-[15px] border bg-black6 flexshadow-3 rounded-full relative data-[state=checked]:bg-black3 data-[state=checked]:border-black3 cursor-pointer"
        >
          <SwitchThumb
            class="block w-[13px] h-[13px] my-auto bg-white shadow-sm rounded-full transition-transform duration-100 translate-x-0 will-change-transform data-[state=checked]:translate-x-[11px]"
          />
        </SwitchRoot>
      </div>

      <div v-if="isMac" class="h-[1px] bg-gray9 my-2.5"></div>

      <div v-if="isMac" class="flex items-center">
        <div class="flex-1">{{ t('manage_notification') }}</div>
        <div
          @click="onOpenSettings('notification')"
          class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
        >
          {{ t('go_to_preference') }}
        </div>
      </div>
    </div>

    <div class="font-semibold text-[14px] text-[#858C9B] mb-1">{{ t('system') }}</div>
    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 mb-1.5">
      <div class="flex items-center">
        <div class="flex-1">{{ t('start_at_login') }}</div>
        <SwitchRoot
          id="airplane-mode"
          :checked="launchEnabled"
          @update:checked="onLaunch"
          class="w-[26px] h-[15px] border bg-black6 flexshadow-3 rounded-full relative data-[state=checked]:bg-black3 data-[state=checked]:border-black3 cursor-pointer"
        >
          <SwitchThumb
            class="block w-[13px] h-[13px] my-auto bg-white shadow-sm rounded-full transition-transform duration-100 translate-x-0 will-change-transform data-[state=checked]:translate-x-[11px]"
          />
        </SwitchRoot>
      </div>
      <div v-if="isMac" class="h-[1px] bg-gray9 my-2.5"></div>
      <div v-if="isMac" class="flex items-center">
        <div class="flex-1">{{ t('show_in_dock') }}</div>
        <SwitchRoot
          id="airplane-mode"
          :checked="showInDock"
          @update:checked="onShowInDock"
          class="w-[26px] h-[15px] border bg-black6 flexshadow-3 rounded-full relative data-[state=checked]:bg-black3 data-[state=checked]:border-black3 cursor-pointer"
        >
          <SwitchThumb
            class="block w-[13px] h-[13px] my-auto bg-white shadow-sm rounded-full transition-transform duration-100 translate-x-0 will-change-transform data-[state=checked]:translate-x-[11px]"
          />
        </SwitchRoot>
      </div>
    </div>

    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 mb-6">
      <div class="flex items-center">
        <div class="flex-1">
          <div>{{ t('language') }}</div>
          <div class="text-gray7 text-[12px]">{{ t('select_language') }}</div>
        </div>
        <PopoverRoot :open="languageOpend" @update:open="onLanguageOpend">
          <PopoverTrigger
            class="h-8 gap-1.5 text-gray7 flex items-center px-1.5 text-sm rounded-md hover:bg-gray3 active:bg-gray2 focus:outline-none"
            aria-label="Update dimensions"
          >
            {{ language }}
            <svg-icon name="pop-up" class="w-4 h-4"></svg-icon>
          </PopoverTrigger>
          <PopoverPortal>
            <PopoverContent
              side="bottom"
              :side-offset="4"
              align="end"
              class="z-[99999] flex max-h-52 min-w-[100px] text-[16px] flex-col overflow-auto rounded-lg bg-white px-1.5 py-2 shadow-1"
            >
              <button
                v-for="item in languageList"
                @click.stop="onLanguage(item)"
                type="button"
                class="flex w-[180px] items-center group h-20 p-2 rounded hover:bg-gray3 active:bg-gray2 hover:svg"
                :class="{ 'bg-gray3': item.name === language }"
              >
                <div class="flex-1 text-left text-[14px] text-[#1F1F1F]">{{ item.name }}</div>
                <svg-icon v-if="item.name === language" name="checked" class="w-5 h-5"></svg-icon>
              </button>
            </PopoverContent>
          </PopoverPortal>
        </PopoverRoot>
      </div>
    </div>
    <!-- Help & Feedback -->
    <div class="font-semibold text-[14px] text-[#858C9B] mb-1">{{ t('help_feedback') }}</div>
    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 mb-1.5">
      <div class="flex items-center">
        <div class="flex-1">{{ t('Me_help_online') }}</div>
        <div
          @click="onConcat"
          class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
        >
          {{ t('get_support') }}
        </div>
      </div>
      <div class="h-[1px] bg-gray9 my-2.5"></div>
      <div class="flex items-center">
        <div class="flex-1">{{ t('feedback') }}</div>
        <div
          @click="onMail"
          class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
        >
          {{ t('send_mail') }}
        </div>
      </div>
    </div>
    <!-- support -->
    <div class="bg-gray10 border border-gray9 rounded-md p-2.5 mb-6">
      <div class="flex items-center">
        <div class="flex-1">{{ t('share_logs') }}</div>
        <div
          @click="onShareLogs"
          v-if="uploadStatus === 0"
          class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
        >
          {{ t('share_text') }}
        </div>
        <div class="text-gray7" v-else-if="[1].includes(uploadStatus)">
          {{ t('loading_text') }}
        </div>
        <div class="text-gray7 flex items-center" v-else-if="[3].includes(uploadStatus)">
          <div
            @click="onRetry(3)"
            class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
          >
            {{ t('retry') }}
          </div>
        </div>
        <div class="text-green1" v-else-if="[2].includes(uploadStatus)">
          {{ t('success_text') }}
        </div>
        <!-- 上传日志暂不做注释 -->
        <!-- <div class="text-gray7" v-else-if="[4].includes(uploadStatus)">
          {{ t('loading_waiting_text') }}
        </div>
        <div class="text-gray7" v-else-if="[5].includes(uploadStatus)">
          {{ t('uploading_text') }} {{ uploadPercent }}%
        </div>
        <div class="text-green1" v-else-if="[6].includes(uploadStatus)">
          {{ t('upload_success') }}
        </div>
        <div class="text-gray7 flex items-center" v-else-if="[7].includes(uploadStatus)">
          <div
            @click="onRetry(7)"
            class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
          >
            {{ t('retry') }}
          </div>
        </div> -->
      </div>
    </div>

    <div class="bg-gray10 border border-gray9 rounded-md p-2.5">
      <div class="flex items-center">
        <div class="flex-1">{{ t('quit') }}</div>
        <div
          @click="onExit"
          class="h-6 px-1.5 flex items-center select-none text-black3 rounded-md border border-gray2 bg-white cursor-pointer hover:bg-gray3 active:bg-gray2"
        >
          {{ t('quit_plaud') }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
/* 可以添加过渡动画 */
.disclosure-content-enter-active,
.disclosure-content-leave-active {
  transition: all 0.3s ease;
}

.disclosure-content-enter-from,
.disclosure-content-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 添加 Collapsible 动画 */
.radix-collapsible-content-enter-active,
.radix-collapsible-content-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  overflow: hidden;
}

.radix-collapsible-content-enter-from,
.radix-collapsible-content-leave-to {
  height: 0;
  opacity: 0;
}

.radix-collapsible-content-enter-to,
.radix-collapsible-content-leave-from {
  height: var(--radix-collapsible-content-height);
  opacity: 1;
}
</style>
