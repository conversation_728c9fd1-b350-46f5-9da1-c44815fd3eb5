<script setup lang="ts">
import Setting from '@/components/header/setting.vue';
import { HOST } from '@/constants/config';
import { i18n } from '@/locales/i18n';
import bgImg from './bg.png';
const { t } = i18n.global;
import { log } from '@/utils/firebase';
import { useRouter } from 'vue-router';
const router = useRouter();

const onLogin = () => {
  log({ type: 'login_click' });
  window.__E__?.shell.openExternal(`${HOST}?from=desktop`);
};

// window.__E__?.ipcRenderer.on('change-token', (event, data) => {
//   router.push('/');
// });
</script>

<template>
  <div class="rounded-[10px] overflow-hidden bg-white">
    <div class="h-14 flex items-center flex-row-reverse mr-4">
      <Setting type="login" />
    </div>
    <div class="px-[20px] py-10 relative">
      <img :src="bgImg" class="absolute top-0 left-0 w-full h-full" />
      <svg-icon name="logo-text" class="w-full h-6" />
      <div
        @click="onLogin"
        class="flex h-11 mt-10 items-center justify-center rounded-lg bg-black3 text-white cursor-pointer font-semibold select-none relative"
      >
        {{ t('sign_in') }}
      </div>
    </div>
  </div>
</template>

<style></style>
