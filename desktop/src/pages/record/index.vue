<script setup lang="ts">
import Header from '@/components/header/index.vue';
import Record from '@/components/record/index.vue';
import Guide from '@/components/guide/index.vue';
// import Login from '@/components/login/index.vue';
import { ref, onMounted, toRaw } from 'vue';
import { TOKEN } from '@/constants/user';
import { getUserInfo } from '@/apis/user';
import { i18n } from '@/locales/i18n';
import { LANGUAGE, NOTIFY } from '@/constants/user';
import { setUser } from '@/utils/firebase';

import { useRouter } from 'vue-router';
const router = useRouter();

let userInfo = ref({});
let isPPCopened = ref(false);
let isRecordClick = ref(false);
isPPCopened.value =
  localStorage.getItem('ON_PPC') === 'false' || localStorage.getItem('ON_PPC') === null
    ? false
    : true;

window.__E__?.ipcRenderer.send('set-log', 'record page create');

window.__E__?.ipcRenderer.send('get-ppc', isPPCopened.value);

window.__E__?.ipcRenderer.on('settings-updated', (event, data) => {
  console.log('Updated settings from second window:', data);
  if (data?.type === LANGUAGE) {
    i18n.global.locale.value = data.data;
  }
});

window.__E__?.ipcRenderer.on('ppc-change', (event, data) => {
  isPPCopened.value = data;
});

init();

async function init() {
  let token = localStorage.getItem(TOKEN);
  // if (!token && location.href.includes('http')) {
  //   token = localStorage.getItem(TOKEN);
  // }

  if (token) {
    try {
      window.__E__?.ipcRenderer.send('set-log', `record page: before getuserinfo`);
      const res = await getUserInfo();
      window.__E__?.ipcRenderer.send('set-log', `record page user info: ${JSON.stringify(res)}`);
      userInfo.value = res;
      window.__E__?.ipcRenderer.send('change-token', true);
      setUser(userInfo.value?.data_user?.id);
      window.__E__?.ipcRenderer.send('user-info', toRaw(userInfo.value));
    } catch (error) {
      router.push('/login');
      window.__E__?.ipcRenderer.send('change-token', false);
      window.__E__?.ipcRenderer.send('set-log', `record page catch: ${JSON.stringify(error)}`);
    }
  } else {
    router.push('/login');
    window.__E__?.ipcRenderer.send('change-token', false);
    window.__E__?.ipcRenderer.send('set-log', `record page: no token`);
  }
}

const onRecord = (data) => {
  isRecordClick.value = data;
};

// const onSetUserInfo = (value) => {
//   userInfo.value = value;
// };
</script>

<template>
  <template v-if="userInfo.data_user">
    <div class="rounded-[10px] overflow-hidden">
      <Header :userInfo="userInfo" />
      <Record @record="onRecord" />
      <Guide
        v-if="!isPPCopened"
        :class="{ 'bg-[#F2F4F7]': !isPPCopened }"
        :isRecordClick="isRecordClick"
      />
    </div>
  </template>
</template>
