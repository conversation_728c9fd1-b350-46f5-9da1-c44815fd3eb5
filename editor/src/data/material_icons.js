// app的iconName 和 编码的映射
export const MATERIAL_ICONS = {
  ten_k: '0xe000',
  ten_k_sharp: '0xe700',
  ten_k_rounded: '0xf4df',
  ten_k_outlined: '0xedf2',
  ten_mp: '0xe001',
  ten_mp_sharp: '0xe701',
  ten_mp_rounded: '0xf4e0',
  ten_mp_outlined: '0xedf3',
  eleven_mp: '0xe002',
  eleven_mp_sharp: '0xe702',
  eleven_mp_rounded: '0xf4e1',
  eleven_mp_outlined: '0xedf4',
  onetwothree: '0xf04b5',
  onetwothree_sharp: '0xf03c2',
  onetwothree_rounded: '0xe340',
  onetwothree_outlined: '0xf05b0',
  twelve_mp: '0xe003',
  twelve_mp_sharp: '0xe703',
  twelve_mp_rounded: '0xf4e2',
  twelve_mp_outlined: '0xedf5',
  thirteen_mp: '0xe004',
  thirteen_mp_sharp: '0xe704',
  thirteen_mp_rounded: '0xf4e3',
  thirteen_mp_outlined: '0xedf6',
  fourteen_mp: '0xe005',
  fourteen_mp_sharp: '0xe705',
  fourteen_mp_rounded: '0xf4e4',
  fourteen_mp_outlined: '0xedf7',
  fifteen_mp: '0xe006',
  fifteen_mp_sharp: '0xe706',
  fifteen_mp_rounded: '0xf4e5',
  fifteen_mp_outlined: '0xedf8',
  sixteen_mp: '0xe007',
  sixteen_mp_sharp: '0xe707',
  sixteen_mp_rounded: '0xf4e6',
  sixteen_mp_outlined: '0xedf9',
  seventeen_mp: '0xe008',
  seventeen_mp_sharp: '0xe708',
  seventeen_mp_rounded: '0xf4e7',
  seventeen_mp_outlined: '0xedfa',
  eighteen_up_rating: '0xf0784',
  eighteen_up_rating_sharp: '0xf072c',
  eighteen_up_rating_rounded: '0xf07dc',
  eighteen_up_rating_outlined: '0xf06d4',
  eighteen_mp: '0xe009',
  eighteen_mp_sharp: '0xe709',
  eighteen_mp_rounded: '0xf4e8',
  eighteen_mp_outlined: '0xedfb',
  nineteen_mp: '0xe00a',
  nineteen_mp_sharp: '0xe70a',
  nineteen_mp_rounded: '0xf4e9',
  nineteen_mp_outlined: '0xedfc',
  one_k: '0xe00b',
  one_k_sharp: '0xe70c',
  one_k_rounded: '0xf4eb',
  one_k_outlined: '0xedfd',
  one_k_plus: '0xe00c',
  one_k_plus_sharp: '0xe70b',
  one_k_plus_rounded: '0xf4ea',
  one_k_plus_outlined: '0xedfe',
  one_x_mobiledata: '0xe00d',
  one_x_mobiledata_sharp: '0xe70d',
  one_x_mobiledata_rounded: '0xf4ec',
  one_x_mobiledata_outlined: '0xedff',
  twenty_mp: '0xe00e',
  twenty_mp_sharp: '0xe70e',
  twenty_mp_rounded: '0xf4ed',
  twenty_mp_outlined: '0xee00',
  twenty_one_mp: '0xe00f',
  twenty_one_mp_sharp: '0xe70f',
  twenty_one_mp_rounded: '0xf4ee',
  twenty_one_mp_outlined: '0xee01',
  twenty_two_mp: '0xe010',
  twenty_two_mp_sharp: '0xe710',
  twenty_two_mp_rounded: '0xf4ef',
  twenty_two_mp_outlined: '0xee02',
  twenty_three_mp: '0xe011',
  twenty_three_mp_sharp: '0xe711',
  twenty_three_mp_rounded: '0xf4f0',
  twenty_three_mp_outlined: '0xee03',
  twenty_four_mp: '0xe012',
  twenty_four_mp_sharp: '0xe712',
  twenty_four_mp_rounded: '0xf4f1',
  twenty_four_mp_outlined: '0xee04',
  two_k: '0xe013',
  two_k_sharp: '0xe714',
  two_k_rounded: '0xf4f3',
  two_k_outlined: '0xee05',
  two_k_plus: '0xe014',
  two_k_plus_sharp: '0xe713',
  two_k_plus_rounded: '0xf4f2',
  two_k_plus_outlined: '0xee06',
  two_mp: '0xe015',
  two_mp_sharp: '0xe715',
  two_mp_rounded: '0xf4f4',
  two_mp_outlined: '0xee07',
  thirty_fps: '0xe016',
  thirty_fps_sharp: '0xe717',
  thirty_fps_rounded: '0xf4f5',
  thirty_fps_outlined: '0xee08',
  thirty_fps_select: '0xe017',
  thirty_fps_select_sharp: '0xe716',
  thirty_fps_select_rounded: '0xf4f6',
  thirty_fps_select_outlined: '0xee09',
  threesixty: '0xe018',
  threesixty_sharp: '0xe718',
  threesixty_rounded: '0xf4f7',
  threesixty_outlined: '0xee0a',
  threed_rotation: '0xe019',
  threed_rotation_sharp: '0xe719',
  threed_rotation_rounded: '0xf4f8',
  threed_rotation_outlined: '0xee0b',
  three_g_mobiledata: '0xe01a',
  three_g_mobiledata_sharp: '0xe71a',
  three_g_mobiledata_rounded: '0xf4f9',
  three_g_mobiledata_outlined: '0xee0c',
  three_k: '0xe01b',
  three_k_sharp: '0xe71c',
  three_k_rounded: '0xf4fb',
  three_k_outlined: '0xee0d',
  three_k_plus: '0xe01c',
  three_k_plus_sharp: '0xe71b',
  three_k_plus_rounded: '0xf4fa',
  three_k_plus_outlined: '0xee0e',
  three_mp: '0xe01d',
  three_mp_sharp: '0xe71d',
  three_mp_rounded: '0xf4fc',
  three_mp_outlined: '0xee0f',
  three_p: '0xe01e',
  three_p_sharp: '0xe71e',
  three_p_rounded: '0xf4fd',
  three_p_outlined: '0xee10',
  four_g_mobiledata: '0xe01f',
  four_g_mobiledata_sharp: '0xe71f',
  four_g_mobiledata_rounded: '0xf4fe',
  four_g_mobiledata_outlined: '0xee11',
  four_g_plus_mobiledata: '0xe020',
  four_g_plus_mobiledata_sharp: '0xe720',
  four_g_plus_mobiledata_rounded: '0xf4ff',
  four_g_plus_mobiledata_outlined: '0xee12',
  four_k: '0xe021',
  four_k_sharp: '0xe722',
  four_k_rounded: '0xf501',
  four_k_outlined: '0xee13',
  four_k_plus: '0xe022',
  four_k_plus_sharp: '0xe721',
  four_k_plus_rounded: '0xf500',
  four_k_plus_outlined: '0xee14',
  four_mp: '0xe023',
  four_mp_sharp: '0xe723',
  four_mp_rounded: '0xf502',
  four_mp_outlined: '0xee15',
  five_g: '0xe024',
  five_g_sharp: '0xe724',
  five_g_rounded: '0xf503',
  five_g_outlined: '0xee16',
  five_k: '0xe025',
  five_k_sharp: '0xe726',
  five_k_rounded: '0xf505',
  five_k_outlined: '0xee17',
  five_k_plus: '0xe026',
  five_k_plus_sharp: '0xe725',
  five_k_plus_rounded: '0xf504',
  five_k_plus_outlined: '0xee18',
  five_mp: '0xe027',
  five_mp_sharp: '0xe727',
  five_mp_rounded: '0xf506',
  five_mp_outlined: '0xee19',
  sixty_fps: '0xe028',
  sixty_fps_sharp: '0xe729',
  sixty_fps_rounded: '0xf507',
  sixty_fps_outlined: '0xee1a',
  sixty_fps_select: '0xe029',
  sixty_fps_select_sharp: '0xe728',
  sixty_fps_select_rounded: '0xf508',
  sixty_fps_select_outlined: '0xee1b',
  six_ft_apart: '0xe02a',
  six_ft_apart_sharp: '0xe72a',
  six_ft_apart_rounded: '0xf509',
  six_ft_apart_outlined: '0xee1c',
  six_k: '0xe02b',
  six_k_sharp: '0xe72c',
  six_k_rounded: '0xf50b',
  six_k_outlined: '0xee1d',
  six_k_plus: '0xe02c',
  six_k_plus_sharp: '0xe72b',
  six_k_plus_rounded: '0xf50a',
  six_k_plus_outlined: '0xee1e',
  six_mp: '0xe02d',
  six_mp_sharp: '0xe72d',
  six_mp_rounded: '0xf50c',
  six_mp_outlined: '0xee1f',
  seven_k: '0xe02e',
  seven_k_sharp: '0xe72f',
  seven_k_rounded: '0xf50e',
  seven_k_outlined: '0xee20',
  seven_k_plus: '0xe02f',
  seven_k_plus_sharp: '0xe72e',
  seven_k_plus_rounded: '0xf50d',
  seven_k_plus_outlined: '0xee21',
  seven_mp: '0xe030',
  seven_mp_sharp: '0xe730',
  seven_mp_rounded: '0xf50f',
  seven_mp_outlined: '0xee22',
  eight_k: '0xe031',
  eight_k_sharp: '0xe732',
  eight_k_rounded: '0xf511',
  eight_k_outlined: '0xee23',
  eight_k_plus: '0xe032',
  eight_k_plus_sharp: '0xe731',
  eight_k_plus_rounded: '0xf510',
  eight_k_plus_outlined: '0xee24',
  eight_mp: '0xe033',
  eight_mp_sharp: '0xe733',
  eight_mp_rounded: '0xf512',
  eight_mp_outlined: '0xee25',
  nine_k: '0xe034',
  nine_k_sharp: '0xe735',
  nine_k_rounded: '0xf514',
  nine_k_outlined: '0xee26',
  nine_k_plus: '0xe035',
  nine_k_plus_sharp: '0xe734',
  nine_k_plus_rounded: '0xf513',
  nine_k_plus_outlined: '0xee27',
  nine_mp: '0xe036',
  nine_mp_sharp: '0xe736',
  nine_mp_rounded: '0xf515',
  nine_mp_outlined: '0xee28',
  abc: '0xf04b6',
  abc_sharp: '0xf03c3',
  abc_rounded: '0xe4c4',
  abc_outlined: '0xf05b1',
  ac_unit: '0xe037',
  ac_unit_sharp: '0xe737',
  ac_unit_rounded: '0xf516',
  ac_unit_outlined: '0xee29',
  access_alarm: '0xe038',
  access_alarm_sharp: '0xe738',
  access_alarm_rounded: '0xf517',
  access_alarm_outlined: '0xee2a',
  access_alarms: '0xe039',
  access_alarms_sharp: '0xe739',
  access_alarms_rounded: '0xf518',
  access_alarms_outlined: '0xee2b',
  access_time: '0xe03a',
  access_time_sharp: '0xe73b',
  access_time_rounded: '0xf51a',
  access_time_outlined: '0xee2d',
  access_time_filled: '0xe03b',
  access_time_filled_sharp: '0xe73a',
  access_time_filled_rounded: '0xf519',
  access_time_filled_outlined: '0xee2c',
  accessibility: '0xe03c',
  accessibility_sharp: '0xe73d',
  accessibility_rounded: '0xf51c',
  accessibility_outlined: '0xee2f',
  accessibility_new: '0xe03d',
  accessibility_new_sharp: '0xe73c',
  accessibility_new_rounded: '0xf51b',
  accessibility_new_outlined: '0xee2e',
  accessible: '0xe03e',
  accessible_sharp: '0xe73f',
  accessible_rounded: '0xf51e',
  accessible_outlined: '0xee31',
  accessible_forward: '0xe03f',
  accessible_forward_sharp: '0xe73e',
  accessible_forward_rounded: '0xf51d',
  accessible_forward_outlined: '0xee30',
  account_balance: '0xe040',
  account_balance_sharp: '0xe740',
  account_balance_rounded: '0xf51f',
  account_balance_outlined: '0xee32',
  account_balance_wallet: '0xe041',
  account_balance_wallet_sharp: '0xe741',
  account_balance_wallet_rounded: '0xf520',
  account_balance_wallet_outlined: '0xee33',
  account_box: '0xe042',
  account_box_sharp: '0xe742',
  account_box_rounded: '0xf521',
  account_box_outlined: '0xee34',
  account_circle: '0xe043',
  account_circle_sharp: '0xe743',
  account_circle_rounded: '0xf522',
  account_circle_outlined: '0xee35',
  account_tree: '0xe044',
  account_tree_sharp: '0xe744',
  account_tree_rounded: '0xf523',
  account_tree_outlined: '0xee36',
  ad_units: '0xe045',
  ad_units_sharp: '0xe745',
  ad_units_rounded: '0xf524',
  ad_units_outlined: '0xee37',
  adb: '0xe046',
  adb_sharp: '0xe746',
  adb_rounded: '0xf525',
  adb_outlined: '0xee38',
  add: '0xe047',
  add_sharp: '0xe758',
  add_rounded: '0xf537',
  add_outlined: '0xee47',
  add_a_photo: '0xe048',
  add_a_photo_sharp: '0xe747',
  add_a_photo_rounded: '0xf526',
  add_a_photo_outlined: '0xee39',
  add_alarm: '0xe049',
  add_alarm_sharp: '0xe748',
  add_alarm_rounded: '0xf527',
  add_alarm_outlined: '0xee3a',
  add_alert: '0xe04a',
  add_alert_sharp: '0xe749',
  add_alert_rounded: '0xf528',
  add_alert_outlined: '0xee3b',
  add_box: '0xe04b',
  add_box_sharp: '0xe74a',
  add_box_rounded: '0xf529',
  add_box_outlined: '0xee3c',
  add_business: '0xe04c',
  add_business_sharp: '0xe74b',
  add_business_rounded: '0xf52a',
  add_business_outlined: '0xee3d',
  add_call: '0xe04d',
  add_card: '0xf04b7',
  add_card_sharp: '0xf03c4',
  add_card_rounded: '0xf02d1',
  add_card_outlined: '0xf05b2',
  add_chart: '0xe04e',
  add_chart_sharp: '0xe74c',
  add_chart_rounded: '0xf52b',
  add_chart_outlined: '0xee3e',
  add_circle: '0xe04f',
  add_circle_sharp: '0xe74e',
  add_circle_rounded: '0xf52d',
  add_circle_outlined: '0xee40',
  add_circle_outline: '0xe050',
  add_circle_outline_sharp: '0xe74d',
  add_circle_outline_rounded: '0xf52c',
  add_circle_outline_outlined: '0xee3f',
  add_comment: '0xe051',
  add_comment_sharp: '0xe74f',
  add_comment_rounded: '0xf52e',
  add_comment_outlined: '0xee41',
  add_home: '0xf0785',
  add_home_sharp: '0xf072d',
  add_home_rounded: '0xf07dd',
  add_home_outlined: '0xf06d5',
  add_home_work: '0xf0786',
  add_home_work_sharp: '0xf072e',
  add_home_work_rounded: '0xf07de',
  add_home_work_outlined: '0xf06d6',
  add_ic_call: '0xe052',
  add_ic_call_sharp: '0xe750',
  add_ic_call_rounded: '0xf52f',
  add_ic_call_outlined: '0xee42',
  add_link: '0xe053',
  add_link_sharp: '0xe751',
  add_link_rounded: '0xf530',
  add_link_outlined: '0xee43',
  add_location: '0xe054',
  add_location_sharp: '0xe753',
  add_location_rounded: '0xf532',
  add_location_outlined: '0xee45',
  add_location_alt: '0xe055',
  add_location_alt_sharp: '0xe752',
  add_location_alt_rounded: '0xf531',
  add_location_alt_outlined: '0xee44',
  add_moderator: '0xe056',
  add_moderator_sharp: '0xe754',
  add_moderator_rounded: '0xf533',
  add_moderator_outlined: '0xee46',
  add_photo_alternate: '0xe057',
  add_photo_alternate_sharp: '0xe755',
  add_photo_alternate_rounded: '0xf534',
  add_photo_alternate_outlined: '0xee48',
  add_reaction: '0xe058',
  add_reaction_sharp: '0xe756',
  add_reaction_rounded: '0xf535',
  add_reaction_outlined: '0xee49',
  add_road: '0xe059',
  add_road_sharp: '0xe757',
  add_road_rounded: '0xf536',
  add_road_outlined: '0xee4a',
  add_shopping_cart: '0xe05a',
  add_shopping_cart_sharp: '0xe759',
  add_shopping_cart_rounded: '0xf538',
  add_shopping_cart_outlined: '0xee4b',
  add_task: '0xe05b',
  add_task_sharp: '0xe75a',
  add_task_rounded: '0xf539',
  add_task_outlined: '0xee4c',
  add_to_drive: '0xe05c',
  add_to_drive_sharp: '0xe75b',
  add_to_drive_rounded: '0xf53a',
  add_to_drive_outlined: '0xee4d',
  add_to_home_screen: '0xe05d',
  add_to_home_screen_sharp: '0xe75c',
  add_to_home_screen_rounded: '0xf53b',
  add_to_home_screen_outlined: '0xee4e',
  add_to_photos: '0xe05e',
  add_to_photos_sharp: '0xe75d',
  add_to_photos_rounded: '0xf53c',
  add_to_photos_outlined: '0xee4f',
  add_to_queue: '0xe05f',
  add_to_queue_sharp: '0xe75e',
  add_to_queue_rounded: '0xf53d',
  add_to_queue_outlined: '0xee50',
  addchart: '0xe060',
  addchart_sharp: '0xe75f',
  addchart_rounded: '0xf53e',
  addchart_outlined: '0xee51',
  adf_scanner: '0xf04b8',
  adf_scanner_sharp: '0xf03c5',
  adf_scanner_rounded: '0xf02d2',
  adf_scanner_outlined: '0xf05b3',
  adjust: '0xe061',
  adjust_sharp: '0xe760',
  adjust_rounded: '0xf53f',
  adjust_outlined: '0xee52',
  admin_panel_settings: '0xe062',
  admin_panel_settings_sharp: '0xe761',
  admin_panel_settings_rounded: '0xf540',
  admin_panel_settings_outlined: '0xee53',
  adobe: '0xf04b9',
  adobe_sharp: '0xf03c6',
  adobe_rounded: '0xf02d3',
  adobe_outlined: '0xf05b4',
  ads_click: '0xf04ba',
  ads_click_sharp: '0xf03c7',
  ads_click_rounded: '0xf02d4',
  ads_click_outlined: '0xf05b5',
  agriculture: '0xe063',
  agriculture_sharp: '0xe762',
  agriculture_rounded: '0xf541',
  agriculture_outlined: '0xee54',
  air: '0xe064',
  air_sharp: '0xe763',
  air_rounded: '0xf542',
  air_outlined: '0xee55',
  airline_seat_flat: '0xe065',
  airline_seat_flat_sharp: '0xe765',
  airline_seat_flat_rounded: '0xf544',
  airline_seat_flat_outlined: '0xee57',
  airline_seat_flat_angled: '0xe066',
  airline_seat_flat_angled_sharp: '0xe764',
  airline_seat_flat_angled_rounded: '0xf543',
  airline_seat_flat_angled_outlined: '0xee56',
  airline_seat_individual_suite: '0xe067',
  airline_seat_individual_suite_sharp: '0xe766',
  airline_seat_individual_suite_rounded: '0xf545',
  airline_seat_individual_suite_outlined: '0xee58',
  airline_seat_legroom_extra: '0xe068',
  airline_seat_legroom_extra_sharp: '0xe767',
  airline_seat_legroom_extra_rounded: '0xf546',
  airline_seat_legroom_extra_outlined: '0xee59',
  airline_seat_legroom_normal: '0xe069',
  airline_seat_legroom_normal_sharp: '0xe768',
  airline_seat_legroom_normal_rounded: '0xf547',
  airline_seat_legroom_normal_outlined: '0xee5a',
  airline_seat_legroom_reduced: '0xe06a',
  airline_seat_legroom_reduced_sharp: '0xe769',
  airline_seat_legroom_reduced_rounded: '0xf548',
  airline_seat_legroom_reduced_outlined: '0xee5b',
  airline_seat_recline_extra: '0xe06b',
  airline_seat_recline_extra_sharp: '0xe76a',
  airline_seat_recline_extra_rounded: '0xf549',
  airline_seat_recline_extra_outlined: '0xee5c',
  airline_seat_recline_normal: '0xe06c',
  airline_seat_recline_normal_sharp: '0xe76b',
  airline_seat_recline_normal_rounded: '0xf54a',
  airline_seat_recline_normal_outlined: '0xee5d',
  airline_stops: '0xf04bb',
  airline_stops_sharp: '0xf03c8',
  airline_stops_rounded: '0xf02d5',
  airline_stops_outlined: '0xf05b6',
  airlines: '0xf04bc',
  airlines_sharp: '0xf03c9',
  airlines_rounded: '0xf02d6',
  airlines_outlined: '0xf05b7',
  airplane_ticket: '0xe06d',
  airplane_ticket_sharp: '0xe76c',
  airplane_ticket_rounded: '0xf54b',
  airplane_ticket_outlined: '0xee5e',
  airplanemode_active: '0xe06e',
  airplanemode_active_sharp: '0xe76d',
  airplanemode_active_rounded: '0xf54c',
  airplanemode_active_outlined: '0xee5f',
  airplanemode_inactive: '0xe06f',
  airplanemode_inactive_sharp: '0xe76e',
  airplanemode_inactive_rounded: '0xf54d',
  airplanemode_inactive_outlined: '0xee60',
  airplanemode_off: '0xe06f',
  airplanemode_off_sharp: '0xe76e',
  airplanemode_off_rounded: '0xf54d',
  airplanemode_off_outlined: '0xee60',
  airplanemode_on: '0xe06e',
  airplanemode_on_sharp: '0xe76d',
  airplanemode_on_rounded: '0xf54c',
  airplanemode_on_outlined: '0xee5f',
  airplay: '0xe070',
  airplay_sharp: '0xe76f',
  airplay_rounded: '0xf54e',
  airplay_outlined: '0xee61',
  airport_shuttle: '0xe071',
  airport_shuttle_sharp: '0xe770',
  airport_shuttle_rounded: '0xf54f',
  airport_shuttle_outlined: '0xee62',
  alarm: '0xe072',
  alarm_sharp: '0xe774',
  alarm_rounded: '0xf553',
  alarm_outlined: '0xee66',
  alarm_add: '0xe073',
  alarm_add_sharp: '0xe771',
  alarm_add_rounded: '0xf550',
  alarm_add_outlined: '0xee63',
  alarm_off: '0xe074',
  alarm_off_sharp: '0xe772',
  alarm_off_rounded: '0xf551',
  alarm_off_outlined: '0xee64',
  alarm_on: '0xe075',
  alarm_on_sharp: '0xe773',
  alarm_on_rounded: '0xf552',
  alarm_on_outlined: '0xee65',
  album: '0xe076',
  album_sharp: '0xe775',
  album_rounded: '0xf554',
  album_outlined: '0xee67',
  align_horizontal_center: '0xe077',
  align_horizontal_center_sharp: '0xe776',
  align_horizontal_center_rounded: '0xf555',
  align_horizontal_center_outlined: '0xee68',
  align_horizontal_left: '0xe078',
  align_horizontal_left_sharp: '0xe777',
  align_horizontal_left_rounded: '0xf556',
  align_horizontal_left_outlined: '0xee69',
  align_horizontal_right: '0xe079',
  align_horizontal_right_sharp: '0xe778',
  align_horizontal_right_rounded: '0xf557',
  align_horizontal_right_outlined: '0xee6a',
  align_vertical_bottom: '0xe07a',
  align_vertical_bottom_sharp: '0xe779',
  align_vertical_bottom_rounded: '0xf558',
  align_vertical_bottom_outlined: '0xee6b',
  align_vertical_center: '0xe07b',
  align_vertical_center_sharp: '0xe77a',
  align_vertical_center_rounded: '0xf559',
  align_vertical_center_outlined: '0xee6c',
  align_vertical_top: '0xe07c',
  align_vertical_top_sharp: '0xe77b',
  align_vertical_top_rounded: '0xf55a',
  align_vertical_top_outlined: '0xee6d',
  all_inbox: '0xe07d',
  all_inbox_sharp: '0xe77c',
  all_inbox_rounded: '0xf55b',
  all_inbox_outlined: '0xee6e',
  all_inclusive: '0xe07e',
  all_inclusive_sharp: '0xe77d',
  all_inclusive_rounded: '0xf55c',
  all_inclusive_outlined: '0xee6f',
  all_out: '0xe07f',
  all_out_sharp: '0xe77e',
  all_out_rounded: '0xf55d',
  all_out_outlined: '0xee70',
  alt_route: '0xe080',
  alt_route_sharp: '0xe77f',
  alt_route_rounded: '0xf55e',
  alt_route_outlined: '0xee71',
  alternate_email: '0xe081',
  alternate_email_sharp: '0xe780',
  alternate_email_rounded: '0xf55f',
  alternate_email_outlined: '0xee72',
  amp_stories: '0xe082',
  amp_stories_sharp: '0xe781',
  amp_stories_rounded: '0xf560',
  amp_stories_outlined: '0xee73',
  analytics: '0xe083',
  analytics_sharp: '0xe782',
  analytics_rounded: '0xf561',
  analytics_outlined: '0xee74',
  anchor: '0xe084',
  anchor_sharp: '0xe783',
  anchor_rounded: '0xf562',
  anchor_outlined: '0xee75',
  android: '0xe085',
  android_sharp: '0xe784',
  android_rounded: '0xf563',
  android_outlined: '0xee76',
  animation: '0xe086',
  animation_sharp: '0xe785',
  animation_rounded: '0xf564',
  animation_outlined: '0xee77',
  announcement: '0xe087',
  announcement_sharp: '0xe786',
  announcement_rounded: '0xf565',
  announcement_outlined: '0xee78',
  aod: '0xe088',
  aod_sharp: '0xe787',
  aod_rounded: '0xf566',
  aod_outlined: '0xee79',
  apartment: '0xe089',
  apartment_sharp: '0xe788',
  apartment_rounded: '0xf567',
  apartment_outlined: '0xee7a',
  api: '0xe08a',
  api_sharp: '0xe789',
  api_rounded: '0xf568',
  api_outlined: '0xee7b',
  app_blocking: '0xe08b',
  app_blocking_sharp: '0xe78a',
  app_blocking_rounded: '0xf569',
  app_blocking_outlined: '0xee7c',
  app_registration: '0xe08c',
  app_registration_sharp: '0xe78b',
  app_registration_rounded: '0xf56a',
  app_registration_outlined: '0xee7d',
  app_settings_alt: '0xe08d',
  app_settings_alt_sharp: '0xe78c',
  app_settings_alt_rounded: '0xf56b',
  app_settings_alt_outlined: '0xee7e',
  app_shortcut: '0xf04bd',
  app_shortcut_sharp: '0xf03ca',
  app_shortcut_rounded: '0xf02d7',
  app_shortcut_outlined: '0xf05b8',
  apple: '0xf04be',
  apple_sharp: '0xf03cb',
  apple_rounded: '0xf02d8',
  apple_outlined: '0xf05b9',
  approval: '0xe08e',
  approval_sharp: '0xe78d',
  approval_rounded: '0xf56c',
  approval_outlined: '0xee7f',
  apps: '0xe08f',
  apps_sharp: '0xe78e',
  apps_rounded: '0xf56d',
  apps_outlined: '0xee80',
  apps_outage: '0xf04bf',
  apps_outage_sharp: '0xf03cc',
  apps_outage_rounded: '0xf02d9',
  apps_outage_outlined: '0xf05ba',
  architecture: '0xe090',
  architecture_sharp: '0xe78f',
  architecture_rounded: '0xf56e',
  architecture_outlined: '0xee81',
  archive: '0xe091',
  archive_sharp: '0xe790',
  archive_rounded: '0xf56f',
  archive_outlined: '0xee82',
  area_chart: '0xf04c0',
  area_chart_sharp: '0xf03cd',
  area_chart_rounded: '0xf02da',
  area_chart_outlined: '0xf05bb',
  arrow_back: '0xe092',
  arrow_back_sharp: '0xe793',
  arrow_back_rounded: '0xf572',
  arrow_back_outlined: '0xee85',
  arrow_back_ios: '0xe093',
  arrow_back_ios_sharp: '0xe792',
  arrow_back_ios_rounded: '0xf571',
  arrow_back_ios_outlined: '0xee84',
  arrow_back_ios_new: '0xe094',
  arrow_back_ios_new_sharp: '0xe791',
  arrow_back_ios_new_rounded: '0xf570',
  arrow_back_ios_new_outlined: '0xee83',
  arrow_circle_down: '0xe095',
  arrow_circle_down_sharp: '0xe794',
  arrow_circle_down_rounded: '0xf573',
  arrow_circle_down_outlined: '0xee86',
  arrow_circle_left: '0xf04c1',
  arrow_circle_left_sharp: '0xf03ce',
  arrow_circle_left_rounded: '0xf02db',
  arrow_circle_left_outlined: '0xf05bc',
  arrow_circle_right: '0xf04c2',
  arrow_circle_right_sharp: '0xf03cf',
  arrow_circle_right_rounded: '0xf02dc',
  arrow_circle_right_outlined: '0xf05bd',
  arrow_circle_up: '0xe096',
  arrow_circle_up_sharp: '0xe795',
  arrow_circle_up_rounded: '0xf574',
  arrow_circle_up_outlined: '0xee87',
  arrow_downward: '0xe097',
  arrow_downward_sharp: '0xe796',
  arrow_downward_rounded: '0xf575',
  arrow_downward_outlined: '0xee88',
  arrow_drop_down: '0xe098',
  arrow_drop_down_sharp: '0xe798',
  arrow_drop_down_rounded: '0xf577',
  arrow_drop_down_outlined: '0xee8a',
  arrow_drop_down_circle: '0xe099',
  arrow_drop_down_circle_sharp: '0xe797',
  arrow_drop_down_circle_rounded: '0xf576',
  arrow_drop_down_circle_outlined: '0xee89',
  arrow_drop_up: '0xe09a',
  arrow_drop_up_sharp: '0xe799',
  arrow_drop_up_rounded: '0xf578',
  arrow_drop_up_outlined: '0xee8b',
  arrow_forward: '0xe09b',
  arrow_forward_sharp: '0xe79b',
  arrow_forward_rounded: '0xf57a',
  arrow_forward_outlined: '0xee8d',
  arrow_forward_ios: '0xe09c',
  arrow_forward_ios_sharp: '0xe79a',
  arrow_forward_ios_rounded: '0xf579',
  arrow_forward_ios_outlined: '0xee8c',
  arrow_left: '0xe09d',
  arrow_left_sharp: '0xe79c',
  arrow_left_rounded: '0xf57b',
  arrow_left_outlined: '0xee8e',
  arrow_outward: '0xf0852',
  arrow_outward_sharp: '0xf0834',
  arrow_outward_rounded: '0xf087d',
  arrow_outward_outlined: '0xf089b',
  arrow_right: '0xe09e',
  arrow_right_sharp: '0xe79e',
  arrow_right_rounded: '0xf57d',
  arrow_right_outlined: '0xee90',
  arrow_right_alt: '0xe09f',
  arrow_right_alt_sharp: '0xe79d',
  arrow_right_alt_rounded: '0xf57c',
  arrow_right_alt_outlined: '0xee8f',
  arrow_upward: '0xe0a0',
  arrow_upward_sharp: '0xe79f',
  arrow_upward_rounded: '0xf57e',
  arrow_upward_outlined: '0xee91',
  art_track: '0xe0a1',
  art_track_sharp: '0xe7a0',
  art_track_rounded: '0xf57f',
  art_track_outlined: '0xee92',
  article: '0xe0a2',
  article_sharp: '0xe7a1',
  article_rounded: '0xf580',
  article_outlined: '0xee93',
  aspect_ratio: '0xe0a3',
  aspect_ratio_sharp: '0xe7a2',
  aspect_ratio_rounded: '0xf581',
  aspect_ratio_outlined: '0xee94',
  assessment: '0xe0a4',
  assessment_sharp: '0xe7a3',
  assessment_rounded: '0xf582',
  assessment_outlined: '0xee95',
  assignment: '0xe0a5',
  assignment_sharp: '0xe7a8',
  assignment_rounded: '0xf587',
  assignment_outlined: '0xee98',
  assignment_add: '0xf0853',
  assignment_ind: '0xe0a6',
  assignment_ind_sharp: '0xe7a4',
  assignment_ind_rounded: '0xf583',
  assignment_ind_outlined: '0xee96',
  assignment_late: '0xe0a7',
  assignment_late_sharp: '0xe7a5',
  assignment_late_rounded: '0xf584',
  assignment_late_outlined: '0xee97',
  assignment_return: '0xe0a8',
  assignment_return_sharp: '0xe7a6',
  assignment_return_rounded: '0xf585',
  assignment_return_outlined: '0xee99',
  assignment_returned: '0xe0a9',
  assignment_returned_sharp: '0xe7a7',
  assignment_returned_rounded: '0xf586',
  assignment_returned_outlined: '0xee9a',
  assignment_turned_in: '0xe0aa',
  assignment_turned_in_sharp: '0xe7a9',
  assignment_turned_in_rounded: '0xf588',
  assignment_turned_in_outlined: '0xee9b',
  assist_walker: '0xf0854',
  assist_walker_sharp: '0xf0835',
  assist_walker_rounded: '0xf087e',
  assist_walker_outlined: '0xf089c',
  assistant: '0xe0ab',
  assistant_sharp: '0xe7ac',
  assistant_rounded: '0xf58b',
  assistant_outlined: '0xee9d',
  assistant_direction: '0xe0ac',
  assistant_direction_sharp: '0xe7aa',
  assistant_direction_rounded: '0xf589',
  assistant_direction_outlined: '0xee9c',
  assistant_navigation: '0xe0ad',
  assistant_photo: '0xe0ae',
  assistant_photo_sharp: '0xe7ab',
  assistant_photo_rounded: '0xf58a',
  assistant_photo_outlined: '0xee9e',
  assured_workload: '0xf04c3',
  assured_workload_sharp: '0xf03d0',
  assured_workload_rounded: '0xf02dd',
  assured_workload_outlined: '0xf05be',
  atm: '0xe0af',
  atm_sharp: '0xe7ad',
  atm_rounded: '0xf58c',
  atm_outlined: '0xee9f',
  attach_email: '0xe0b0',
  attach_email_sharp: '0xe7ae',
  attach_email_rounded: '0xf58d',
  attach_email_outlined: '0xeea0',
  attach_file: '0xe0b1',
  attach_file_sharp: '0xe7af',
  attach_file_rounded: '0xf58e',
  attach_file_outlined: '0xeea1',
  attach_money: '0xe0b2',
  attach_money_sharp: '0xe7b0',
  attach_money_rounded: '0xf58f',
  attach_money_outlined: '0xeea2',
  attachment: '0xe0b3',
  attachment_sharp: '0xe7b1',
  attachment_rounded: '0xf590',
  attachment_outlined: '0xeea3',
  attractions: '0xe0b4',
  attractions_sharp: '0xe7b2',
  attractions_rounded: '0xf591',
  attractions_outlined: '0xeea4',
  attribution: '0xe0b5',
  attribution_sharp: '0xe7b3',
  attribution_rounded: '0xf592',
  attribution_outlined: '0xeea5',
  audio_file: '0xf04c4',
  audio_file_sharp: '0xf03d1',
  audio_file_rounded: '0xf02de',
  audio_file_outlined: '0xf05bf',
  audiotrack: '0xe0b6',
  audiotrack_sharp: '0xe7b4',
  audiotrack_rounded: '0xf593',
  audiotrack_outlined: '0xeea6',
  auto_awesome: '0xe0b7',
  auto_awesome_sharp: '0xe7b7',
  auto_awesome_rounded: '0xf596',
  auto_awesome_outlined: '0xeea9',
  auto_awesome_mosaic: '0xe0b8',
  auto_awesome_mosaic_sharp: '0xe7b5',
  auto_awesome_mosaic_rounded: '0xf594',
  auto_awesome_mosaic_outlined: '0xeea7',
  auto_awesome_motion: '0xe0b9',
  auto_awesome_motion_sharp: '0xe7b6',
  auto_awesome_motion_rounded: '0xf595',
  auto_awesome_motion_outlined: '0xeea8',
  auto_delete: '0xe0ba',
  auto_delete_sharp: '0xe7b8',
  auto_delete_rounded: '0xf597',
  auto_delete_outlined: '0xeeaa',
  auto_fix_high: '0xe0bb',
  auto_fix_high_sharp: '0xe7b9',
  auto_fix_high_rounded: '0xf598',
  auto_fix_high_outlined: '0xeeab',
  auto_fix_normal: '0xe0bc',
  auto_fix_normal_sharp: '0xe7ba',
  auto_fix_normal_rounded: '0xf599',
  auto_fix_normal_outlined: '0xeeac',
  auto_fix_off: '0xe0bd',
  auto_fix_off_sharp: '0xe7bb',
  auto_fix_off_rounded: '0xf59a',
  auto_fix_off_outlined: '0xeead',
  auto_graph: '0xe0be',
  auto_graph_sharp: '0xe7bc',
  auto_graph_rounded: '0xf59b',
  auto_graph_outlined: '0xeeae',
  auto_mode: '0xf0787',
  auto_mode_sharp: '0xf072f',
  auto_mode_rounded: '0xf07df',
  auto_mode_outlined: '0xf06d7',
  auto_stories: '0xe0bf',
  auto_stories_sharp: '0xe7bd',
  auto_stories_rounded: '0xf59c',
  auto_stories_outlined: '0xeeaf',
  autofps_select: '0xe0c0',
  autofps_select_sharp: '0xe7be',
  autofps_select_rounded: '0xf59d',
  autofps_select_outlined: '0xeeb0',
  autorenew: '0xe0c1',
  autorenew_sharp: '0xe7bf',
  autorenew_rounded: '0xf59e',
  autorenew_outlined: '0xeeb1',
  av_timer: '0xe0c2',
  av_timer_sharp: '0xe7c0',
  av_timer_rounded: '0xf59f',
  av_timer_outlined: '0xeeb2',
  baby_changing_station: '0xe0c3',
  baby_changing_station_sharp: '0xe7c1',
  baby_changing_station_rounded: '0xf5a0',
  baby_changing_station_outlined: '0xeeb3',
  back_hand: '0xf04c5',
  back_hand_sharp: '0xf03d2',
  back_hand_rounded: '0xf02df',
  back_hand_outlined: '0xf05c0',
  backpack: '0xe0c4',
  backpack_sharp: '0xe7c2',
  backpack_rounded: '0xf5a1',
  backpack_outlined: '0xeeb4',
  backspace: '0xe0c5',
  backspace_sharp: '0xe7c3',
  backspace_rounded: '0xf5a2',
  backspace_outlined: '0xeeb5',
  backup: '0xe0c6',
  backup_sharp: '0xe7c4',
  backup_rounded: '0xf5a3',
  backup_outlined: '0xeeb6',
  backup_table: '0xe0c7',
  backup_table_sharp: '0xe7c5',
  backup_table_rounded: '0xf5a4',
  backup_table_outlined: '0xeeb7',
  badge: '0xe0c8',
  badge_sharp: '0xe7c6',
  badge_rounded: '0xf5a5',
  badge_outlined: '0xeeb8',
  bakery_dining: '0xe0c9',
  bakery_dining_sharp: '0xe7c7',
  bakery_dining_rounded: '0xf5a6',
  bakery_dining_outlined: '0xeeb9',
  balance: '0xf04c6',
  balance_sharp: '0xf03d3',
  balance_rounded: '0xf02e0',
  balance_outlined: '0xf05c1',
  balcony: '0xe0ca',
  balcony_sharp: '0xe7c8',
  balcony_rounded: '0xf5a7',
  balcony_outlined: '0xeeba',
  ballot: '0xe0cb',
  ballot_sharp: '0xe7c9',
  ballot_rounded: '0xf5a8',
  ballot_outlined: '0xeebb',
  bar_chart: '0xe0cc',
  bar_chart_sharp: '0xe7ca',
  bar_chart_rounded: '0xf5a9',
  bar_chart_outlined: '0xeebc',
  barcode_reader: '0xf0855',
  batch_prediction: '0xe0cd',
  batch_prediction_sharp: '0xe7cb',
  batch_prediction_rounded: '0xf5aa',
  batch_prediction_outlined: '0xeebd',
  bathroom: '0xe0ce',
  bathroom_sharp: '0xe7cc',
  bathroom_rounded: '0xf5ab',
  bathroom_outlined: '0xeebe',
  bathtub: '0xe0cf',
  bathtub_sharp: '0xe7cd',
  bathtub_rounded: '0xf5ac',
  bathtub_outlined: '0xeebf',
  battery_0_bar: '0xf0788',
  battery_0_bar_sharp: '0xf0730',
  battery_0_bar_rounded: '0xf07e0',
  battery_0_bar_outlined: '0xf06d8',
  battery_1_bar: '0xf0789',
  battery_1_bar_sharp: '0xf0731',
  battery_1_bar_rounded: '0xf07e1',
  battery_1_bar_outlined: '0xf06d9',
  battery_2_bar: '0xf078a',
  battery_2_bar_sharp: '0xf0732',
  battery_2_bar_rounded: '0xf07e2',
  battery_2_bar_outlined: '0xf06da',
  battery_3_bar: '0xf078b',
  battery_3_bar_sharp: '0xf0733',
  battery_3_bar_rounded: '0xf07e3',
  battery_3_bar_outlined: '0xf06db',
  battery_4_bar: '0xf078c',
  battery_4_bar_sharp: '0xf0734',
  battery_4_bar_rounded: '0xf07e4',
  battery_4_bar_outlined: '0xf06dc',
  battery_5_bar: '0xf078d',
  battery_5_bar_sharp: '0xf0735',
  battery_5_bar_rounded: '0xf07e5',
  battery_5_bar_outlined: '0xf06dd',
  battery_6_bar: '0xf078e',
  battery_6_bar_sharp: '0xf0736',
  battery_6_bar_rounded: '0xf07e6',
  battery_6_bar_outlined: '0xf06de',
  battery_alert: '0xe0d0',
  battery_alert_sharp: '0xe7ce',
  battery_alert_rounded: '0xf5ad',
  battery_alert_outlined: '0xeec0',
  battery_charging_full: '0xe0d1',
  battery_charging_full_sharp: '0xe7cf',
  battery_charging_full_rounded: '0xf5ae',
  battery_charging_full_outlined: '0xeec1',
  battery_full: '0xe0d2',
  battery_full_sharp: '0xe7d0',
  battery_full_rounded: '0xf5af',
  battery_full_outlined: '0xeec2',
  battery_saver: '0xe0d3',
  battery_saver_sharp: '0xe7d1',
  battery_saver_rounded: '0xf5b0',
  battery_saver_outlined: '0xeec3',
  battery_std: '0xe0d4',
  battery_std_sharp: '0xe7d2',
  battery_std_rounded: '0xf5b1',
  battery_std_outlined: '0xeec4',
  battery_unknown: '0xe0d5',
  battery_unknown_sharp: '0xe7d3',
  battery_unknown_rounded: '0xf5b2',
  battery_unknown_outlined: '0xeec5',
  beach_access: '0xe0d6',
  beach_access_sharp: '0xe7d4',
  beach_access_rounded: '0xf5b3',
  beach_access_outlined: '0xeec6',
  bed: '0xe0d7',
  bed_sharp: '0xe7d5',
  bed_rounded: '0xf5b4',
  bed_outlined: '0xeec7',
  bedroom_baby: '0xe0d8',
  bedroom_baby_sharp: '0xe7d6',
  bedroom_baby_rounded: '0xf5b5',
  bedroom_baby_outlined: '0xeec8',
  bedroom_child: '0xe0d9',
  bedroom_child_sharp: '0xe7d7',
  bedroom_child_rounded: '0xf5b6',
  bedroom_child_outlined: '0xeec9',
  bedroom_parent: '0xe0da',
  bedroom_parent_sharp: '0xe7d8',
  bedroom_parent_rounded: '0xf5b7',
  bedroom_parent_outlined: '0xeeca',
  bedtime: '0xe0db',
  bedtime_sharp: '0xe7d9',
  bedtime_rounded: '0xf5b8',
  bedtime_outlined: '0xeecb',
  bedtime_off: '0xf04c7',
  bedtime_off_sharp: '0xf03d4',
  bedtime_off_rounded: '0xf02e1',
  bedtime_off_outlined: '0xf05c2',
  beenhere: '0xe0dc',
  beenhere_sharp: '0xe7da',
  beenhere_rounded: '0xf5b9',
  beenhere_outlined: '0xeecc',
  bento: '0xe0dd',
  bento_sharp: '0xe7db',
  bento_rounded: '0xf5ba',
  bento_outlined: '0xeecd',
  bike_scooter: '0xe0de',
  bike_scooter_sharp: '0xe7dc',
  bike_scooter_rounded: '0xf5bb',
  bike_scooter_outlined: '0xeece',
  biotech: '0xe0df',
  biotech_sharp: '0xe7dd',
  biotech_rounded: '0xf5bc',
  biotech_outlined: '0xeecf',
  blender: '0xe0e0',
  blender_sharp: '0xe7de',
  blender_rounded: '0xf5bd',
  blender_outlined: '0xeed0',
  blind: '0xf0856',
  blind_sharp: '0xf0836',
  blind_rounded: '0xf087f',
  blind_outlined: '0xf089d',
  blinds: '0xf078f',
  blinds_sharp: '0xf0738',
  blinds_rounded: '0xf07e8',
  blinds_outlined: '0xf06e0',
  blinds_closed: '0xf0790',
  blinds_closed_sharp: '0xf0737',
  blinds_closed_rounded: '0xf07e7',
  blinds_closed_outlined: '0xf06df',
  block: '0xe0e1',
  block_sharp: '0xe7df',
  block_rounded: '0xf5be',
  block_outlined: '0xeed1',
  block_flipped: '0xe0e2',
  bloodtype: '0xe0e3',
  bloodtype_sharp: '0xe7e0',
  bloodtype_rounded: '0xf5bf',
  bloodtype_outlined: '0xeed2',
  bluetooth: '0xe0e4',
  bluetooth_sharp: '0xe7e6',
  bluetooth_rounded: '0xf5c4',
  bluetooth_outlined: '0xeed7',
  bluetooth_audio: '0xe0e5',
  bluetooth_audio_sharp: '0xe7e1',
  bluetooth_audio_rounded: '0xf5c0',
  bluetooth_audio_outlined: '0xeed3',
  bluetooth_connected: '0xe0e6',
  bluetooth_connected_sharp: '0xe7e2',
  bluetooth_connected_rounded: '0xf5c1',
  bluetooth_connected_outlined: '0xeed4',
  bluetooth_disabled: '0xe0e7',
  bluetooth_disabled_sharp: '0xe7e3',
  bluetooth_disabled_rounded: '0xf5c2',
  bluetooth_disabled_outlined: '0xeed5',
  bluetooth_drive: '0xe0e8',
  bluetooth_drive_sharp: '0xe7e4',
  bluetooth_drive_rounded: '0xf5c3',
  bluetooth_drive_outlined: '0xeed6',
  bluetooth_searching: '0xe0e9',
  bluetooth_searching_sharp: '0xe7e5',
  bluetooth_searching_rounded: '0xf5c5',
  bluetooth_searching_outlined: '0xeed8',
  blur_circular: '0xe0ea',
  blur_circular_sharp: '0xe7e7',
  blur_circular_rounded: '0xf5c6',
  blur_circular_outlined: '0xeed9',
  blur_linear: '0xe0eb',
  blur_linear_sharp: '0xe7e8',
  blur_linear_rounded: '0xf5c7',
  blur_linear_outlined: '0xeeda',
  blur_off: '0xe0ec',
  blur_off_sharp: '0xe7e9',
  blur_off_rounded: '0xf5c8',
  blur_off_outlined: '0xeedb',
  blur_on: '0xe0ed',
  blur_on_sharp: '0xe7ea',
  blur_on_rounded: '0xf5c9',
  blur_on_outlined: '0xeedc',
  bolt: '0xe0ee',
  bolt_sharp: '0xe7eb',
  bolt_rounded: '0xf5ca',
  bolt_outlined: '0xeedd',
  book: '0xe0ef',
  book_sharp: '0xe7ed',
  book_rounded: '0xf5cc',
  book_outlined: '0xeedf',
  book_online: '0xe0f0',
  book_online_sharp: '0xe7ec',
  book_online_rounded: '0xf5cb',
  book_online_outlined: '0xeede',
  bookmark: '0xe0f1',
  bookmark_sharp: '0xe7f2',
  bookmark_rounded: '0xf5d1',
  bookmark_outlined: '0xeee3',
  bookmark_add: '0xe0f2',
  bookmark_add_sharp: '0xe7ee',
  bookmark_add_rounded: '0xf5cd',
  bookmark_add_outlined: '0xeee0',
  bookmark_added: '0xe0f3',
  bookmark_added_sharp: '0xe7ef',
  bookmark_added_rounded: '0xf5ce',
  bookmark_added_outlined: '0xeee1',
  bookmark_border: '0xe0f4',
  bookmark_border_sharp: '0xe7f0',
  bookmark_border_rounded: '0xf5cf',
  bookmark_border_outlined: '0xeee2',
  bookmark_outline: '0xe0f4',
  bookmark_outline_sharp: '0xe7f0',
  bookmark_outline_rounded: '0xf5cf',
  bookmark_outline_outlined: '0xeee2',
  bookmark_remove: '0xe0f5',
  bookmark_remove_sharp: '0xe7f1',
  bookmark_remove_rounded: '0xf5d0',
  bookmark_remove_outlined: '0xeee4',
  bookmarks: '0xe0f6',
  bookmarks_sharp: '0xe7f3',
  bookmarks_rounded: '0xf5d2',
  bookmarks_outlined: '0xeee5',
  border_all: '0xe0f7',
  border_all_sharp: '0xe7f4',
  border_all_rounded: '0xf5d3',
  border_all_outlined: '0xeee6',
  border_bottom: '0xe0f8',
  border_bottom_sharp: '0xe7f5',
  border_bottom_rounded: '0xf5d4',
  border_bottom_outlined: '0xeee7',
  border_clear: '0xe0f9',
  border_clear_sharp: '0xe7f6',
  border_clear_rounded: '0xf5d5',
  border_clear_outlined: '0xeee8',
  border_color: '0xe0fa',
  border_color_sharp: '0xe7f7',
  border_color_rounded: '0xf5d6',
  border_color_outlined: '0xeee9',
  border_horizontal: '0xe0fb',
  border_horizontal_sharp: '0xe7f8',
  border_horizontal_rounded: '0xf5d7',
  border_horizontal_outlined: '0xeeea',
  border_inner: '0xe0fc',
  border_inner_sharp: '0xe7f9',
  border_inner_rounded: '0xf5d8',
  border_inner_outlined: '0xeeeb',
  border_left: '0xe0fd',
  border_left_sharp: '0xe7fa',
  border_left_rounded: '0xf5d9',
  border_left_outlined: '0xeeec',
  border_outer: '0xe0fe',
  border_outer_sharp: '0xe7fb',
  border_outer_rounded: '0xf5da',
  border_outer_outlined: '0xeeed',
  border_right: '0xe0ff',
  border_right_sharp: '0xe7fc',
  border_right_rounded: '0xf5db',
  border_right_outlined: '0xeeee',
  border_style: '0xe100',
  border_style_sharp: '0xe7fd',
  border_style_rounded: '0xf5dc',
  border_style_outlined: '0xeeef',
  border_top: '0xe101',
  border_top_sharp: '0xe7fe',
  border_top_rounded: '0xf5dd',
  border_top_outlined: '0xeef0',
  border_vertical: '0xe102',
  border_vertical_sharp: '0xe7ff',
  border_vertical_rounded: '0xf5de',
  border_vertical_outlined: '0xeef1',
  boy: '0xf04c8',
  boy_sharp: '0xf03d5',
  boy_rounded: '0xf02e2',
  boy_outlined: '0xf05c3',
  branding_watermark: '0xe103',
  branding_watermark_sharp: '0xe800',
  branding_watermark_rounded: '0xf5df',
  branding_watermark_outlined: '0xeef2',
  breakfast_dining: '0xe104',
  breakfast_dining_sharp: '0xe801',
  breakfast_dining_rounded: '0xf5e0',
  breakfast_dining_outlined: '0xeef3',
  brightness_1: '0xe105',
  brightness_1_sharp: '0xe802',
  brightness_1_rounded: '0xf5e1',
  brightness_1_outlined: '0xeef4',
  brightness_2: '0xe106',
  brightness_2_sharp: '0xe803',
  brightness_2_rounded: '0xf5e2',
  brightness_2_outlined: '0xeef5',
  brightness_3: '0xe107',
  brightness_3_sharp: '0xe804',
  brightness_3_rounded: '0xf5e3',
  brightness_3_outlined: '0xeef6',
  brightness_4: '0xe108',
  brightness_4_sharp: '0xe805',
  brightness_4_rounded: '0xf5e4',
  brightness_4_outlined: '0xeef7',
  brightness_5: '0xe109',
  brightness_5_sharp: '0xe806',
  brightness_5_rounded: '0xf5e5',
  brightness_5_outlined: '0xeef8',
  brightness_6: '0xe10a',
  brightness_6_sharp: '0xe807',
  brightness_6_rounded: '0xf5e6',
  brightness_6_outlined: '0xeef9',
  brightness_7: '0xe10b',
  brightness_7_sharp: '0xe808',
  brightness_7_rounded: '0xf5e7',
  brightness_7_outlined: '0xeefa',
  brightness_auto: '0xe10c',
  brightness_auto_sharp: '0xe809',
  brightness_auto_rounded: '0xf5e8',
  brightness_auto_outlined: '0xeefb',
  brightness_high: '0xe10d',
  brightness_high_sharp: '0xe80a',
  brightness_high_rounded: '0xf5e9',
  brightness_high_outlined: '0xeefc',
  brightness_low: '0xe10e',
  brightness_low_sharp: '0xe80b',
  brightness_low_rounded: '0xf5ea',
  brightness_low_outlined: '0xeefd',
  brightness_medium: '0xe10f',
  brightness_medium_sharp: '0xe80c',
  brightness_medium_rounded: '0xf5eb',
  brightness_medium_outlined: '0xeefe',
  broadcast_on_home: '0xf0791',
  broadcast_on_home_sharp: '0xf0739',
  broadcast_on_home_rounded: '0xf07e9',
  broadcast_on_home_outlined: '0xf06e1',
  broadcast_on_personal: '0xf0792',
  broadcast_on_personal_sharp: '0xf073a',
  broadcast_on_personal_rounded: '0xf07ea',
  broadcast_on_personal_outlined: '0xf06e2',
  broken_image: '0xe110',
  broken_image_sharp: '0xe80d',
  broken_image_rounded: '0xf5ec',
  broken_image_outlined: '0xeeff',
  browse_gallery: '0xf06ba',
  browse_gallery_sharp: '0xf06ad',
  browse_gallery_rounded: '0xf06c7',
  browse_gallery_outlined: '0xf03bc',
  browser_not_supported: '0xe111',
  browser_not_supported_sharp: '0xe80e',
  browser_not_supported_rounded: '0xf5ed',
  browser_not_supported_outlined: '0xef00',
  browser_updated: '0xf04c9',
  browser_updated_sharp: '0xf03d6',
  browser_updated_rounded: '0xf02e3',
  browser_updated_outlined: '0xf05c4',
  brunch_dining: '0xe112',
  brunch_dining_sharp: '0xe80f',
  brunch_dining_rounded: '0xf5ee',
  brunch_dining_outlined: '0xef01',
  brush: '0xe113',
  brush_sharp: '0xe810',
  brush_rounded: '0xf5ef',
  brush_outlined: '0xef02',
  bubble_chart: '0xe114',
  bubble_chart_sharp: '0xe811',
  bubble_chart_rounded: '0xf5f0',
  bubble_chart_outlined: '0xef03',
  bug_report: '0xe115',
  bug_report_sharp: '0xe812',
  bug_report_rounded: '0xf5f1',
  bug_report_outlined: '0xef04',
  build: '0xe116',
  build_sharp: '0xe814',
  build_rounded: '0xf5f3',
  build_outlined: '0xef06',
  build_circle: '0xe117',
  build_circle_sharp: '0xe813',
  build_circle_rounded: '0xf5f2',
  build_circle_outlined: '0xef05',
  bungalow: '0xe118',
  bungalow_sharp: '0xe815',
  bungalow_rounded: '0xf5f4',
  bungalow_outlined: '0xef07',
  burst_mode: '0xe119',
  burst_mode_sharp: '0xe816',
  burst_mode_rounded: '0xf5f5',
  burst_mode_outlined: '0xef08',
  bus_alert: '0xe11a',
  bus_alert_sharp: '0xe817',
  bus_alert_rounded: '0xf5f6',
  bus_alert_outlined: '0xef09',
  business: '0xe11b',
  business_sharp: '0xe819',
  business_rounded: '0xf5f8',
  business_outlined: '0xef0b',
  business_center: '0xe11c',
  business_center_sharp: '0xe818',
  business_center_rounded: '0xf5f7',
  business_center_outlined: '0xef0a',
  cabin: '0xe11d',
  cabin_sharp: '0xe81a',
  cabin_rounded: '0xf5f9',
  cabin_outlined: '0xef0c',
  cable: '0xe11e',
  cable_sharp: '0xe81b',
  cable_rounded: '0xf5fa',
  cable_outlined: '0xef0d',
  cached: '0xe11f',
  cached_sharp: '0xe81c',
  cached_rounded: '0xf5fb',
  cached_outlined: '0xef0e',
  cake: '0xe120',
  cake_sharp: '0xe81d',
  cake_rounded: '0xf5fc',
  cake_outlined: '0xef0f',
  calculate: '0xe121',
  calculate_sharp: '0xe81e',
  calculate_rounded: '0xf5fd',
  calculate_outlined: '0xef10',
  calendar_month: '0xf06bb',
  calendar_month_sharp: '0xf06ae',
  calendar_month_rounded: '0xf06c8',
  calendar_month_outlined: '0xf051f',
  calendar_today: '0xe122',
  calendar_today_sharp: '0xe81f',
  calendar_today_rounded: '0xf5fe',
  calendar_today_outlined: '0xef11',
  calendar_view_day: '0xe123',
  calendar_view_day_sharp: '0xe820',
  calendar_view_day_rounded: '0xf5ff',
  calendar_view_day_outlined: '0xef12',
  calendar_view_month: '0xe124',
  calendar_view_month_sharp: '0xe821',
  calendar_view_month_rounded: '0xf600',
  calendar_view_month_outlined: '0xef13',
  calendar_view_week: '0xe125',
  calendar_view_week_sharp: '0xe822',
  calendar_view_week_rounded: '0xf601',
  calendar_view_week_outlined: '0xef14',
  call: '0xe126',
  call_sharp: '0xe829',
  call_rounded: '0xf608',
  call_outlined: '0xef1a',
  call_end: '0xe127',
  call_end_sharp: '0xe823',
  call_end_rounded: '0xf602',
  call_end_outlined: '0xef15',
  call_made: '0xe128',
  call_made_sharp: '0xe824',
  call_made_rounded: '0xf603',
  call_made_outlined: '0xef16',
  call_merge: '0xe129',
  call_merge_sharp: '0xe825',
  call_merge_rounded: '0xf604',
  call_merge_outlined: '0xef17',
  call_missed: '0xe12a',
  call_missed_sharp: '0xe827',
  call_missed_rounded: '0xf606',
  call_missed_outlined: '0xef19',
  call_missed_outgoing: '0xe12b',
  call_missed_outgoing_sharp: '0xe826',
  call_missed_outgoing_rounded: '0xf605',
  call_missed_outgoing_outlined: '0xef18',
  call_received: '0xe12c',
  call_received_sharp: '0xe828',
  call_received_rounded: '0xf607',
  call_received_outlined: '0xef1b',
  call_split: '0xe12d',
  call_split_sharp: '0xe82a',
  call_split_rounded: '0xf609',
  call_split_outlined: '0xef1c',
  call_to_action: '0xe12e',
  call_to_action_sharp: '0xe82b',
  call_to_action_rounded: '0xf60a',
  call_to_action_outlined: '0xef1d',
  camera: '0xe12f',
  camera_sharp: '0xe833',
  camera_rounded: '0xf612',
  camera_outlined: '0xef23',
  camera_alt: '0xe130',
  camera_alt_sharp: '0xe82c',
  camera_alt_rounded: '0xf60b',
  camera_alt_outlined: '0xef1e',
  camera_enhance: '0xe131',
  camera_enhance_sharp: '0xe82d',
  camera_enhance_rounded: '0xf60c',
  camera_enhance_outlined: '0xef1f',
  camera_front: '0xe132',
  camera_front_sharp: '0xe82e',
  camera_front_rounded: '0xf60d',
  camera_front_outlined: '0xef20',
  camera_indoor: '0xe133',
  camera_indoor_sharp: '0xe82f',
  camera_indoor_rounded: '0xf60e',
  camera_indoor_outlined: '0xef21',
  camera_outdoor: '0xe134',
  camera_outdoor_sharp: '0xe830',
  camera_outdoor_rounded: '0xf60f',
  camera_outdoor_outlined: '0xef22',
  camera_rear: '0xe135',
  camera_rear_sharp: '0xe831',
  camera_rear_rounded: '0xf610',
  camera_rear_outlined: '0xef24',
  camera_roll: '0xe136',
  camera_roll_sharp: '0xe832',
  camera_roll_rounded: '0xf611',
  camera_roll_outlined: '0xef25',
  cameraswitch: '0xe137',
  cameraswitch_sharp: '0xe834',
  cameraswitch_rounded: '0xf613',
  cameraswitch_outlined: '0xef26',
  campaign: '0xe138',
  campaign_sharp: '0xe835',
  campaign_rounded: '0xf614',
  campaign_outlined: '0xef27',
  cancel: '0xe139',
  cancel_sharp: '0xe838',
  cancel_rounded: '0xf616',
  cancel_outlined: '0xef28',
  cancel_presentation: '0xe13a',
  cancel_presentation_sharp: '0xe836',
  cancel_presentation_rounded: '0xf615',
  cancel_presentation_outlined: '0xef29',
  cancel_schedule_send: '0xe13b',
  cancel_schedule_send_sharp: '0xe837',
  cancel_schedule_send_rounded: '0xf617',
  cancel_schedule_send_outlined: '0xef2a',
  candlestick_chart: '0xf04ca',
  candlestick_chart_sharp: '0xf03d7',
  candlestick_chart_rounded: '0xf02e4',
  candlestick_chart_outlined: '0xf05c5',
  car_crash: '0xf0793',
  car_crash_sharp: '0xf073b',
  car_crash_rounded: '0xf07eb',
  car_crash_outlined: '0xf06e3',
  car_rental: '0xe13c',
  car_rental_sharp: '0xe839',
  car_rental_rounded: '0xf618',
  car_rental_outlined: '0xef2b',
  car_repair: '0xe13d',
  car_repair_sharp: '0xe83a',
  car_repair_rounded: '0xf619',
  car_repair_outlined: '0xef2c',
  card_giftcard: '0xe13e',
  card_giftcard_sharp: '0xe83b',
  card_giftcard_rounded: '0xf61a',
  card_giftcard_outlined: '0xef2d',
  card_membership: '0xe13f',
  card_membership_sharp: '0xe83c',
  card_membership_rounded: '0xf61b',
  card_membership_outlined: '0xef2e',
  card_travel: '0xe140',
  card_travel_sharp: '0xe83d',
  card_travel_rounded: '0xf61c',
  card_travel_outlined: '0xef2f',
  carpenter: '0xe141',
  carpenter_sharp: '0xe83e',
  carpenter_rounded: '0xf61d',
  carpenter_outlined: '0xef30',
  cases: '0xe142',
  cases_sharp: '0xe83f',
  cases_rounded: '0xf61e',
  cases_outlined: '0xef31',
  casino: '0xe143',
  casino_sharp: '0xe840',
  casino_rounded: '0xf61f',
  casino_outlined: '0xef32',
  cast: '0xe144',
  cast_sharp: '0xe843',
  cast_rounded: '0xf622',
  cast_outlined: '0xef35',
  cast_connected: '0xe145',
  cast_connected_sharp: '0xe841',
  cast_connected_rounded: '0xf620',
  cast_connected_outlined: '0xef33',
  cast_for_education: '0xe146',
  cast_for_education_sharp: '0xe842',
  cast_for_education_rounded: '0xf621',
  cast_for_education_outlined: '0xef34',
  castle: '0xf04cb',
  castle_sharp: '0xf03d8',
  castle_rounded: '0xf02e5',
  castle_outlined: '0xf05c6',
  catching_pokemon: '0xe147',
  catching_pokemon_sharp: '0xe844',
  catching_pokemon_rounded: '0xf623',
  catching_pokemon_outlined: '0xef36',
  category: '0xe148',
  category_sharp: '0xe845',
  category_rounded: '0xf624',
  category_outlined: '0xef37',
  celebration: '0xe149',
  celebration_sharp: '0xe846',
  celebration_rounded: '0xf625',
  celebration_outlined: '0xef38',
  cell_tower: '0xf04cc',
  cell_tower_sharp: '0xf03d9',
  cell_tower_rounded: '0xf02e6',
  cell_tower_outlined: '0xf05c7',
  cell_wifi: '0xe14a',
  cell_wifi_sharp: '0xe847',
  cell_wifi_rounded: '0xf626',
  cell_wifi_outlined: '0xef39',
  center_focus_strong: '0xe14b',
  center_focus_strong_sharp: '0xe848',
  center_focus_strong_rounded: '0xf627',
  center_focus_strong_outlined: '0xef3a',
  center_focus_weak: '0xe14c',
  center_focus_weak_sharp: '0xe849',
  center_focus_weak_rounded: '0xf628',
  center_focus_weak_outlined: '0xef3b',
  chair: '0xe14d',
  chair_sharp: '0xe84b',
  chair_rounded: '0xf62a',
  chair_outlined: '0xef3d',
  chair_alt: '0xe14e',
  chair_alt_sharp: '0xe84a',
  chair_alt_rounded: '0xf629',
  chair_alt_outlined: '0xef3c',
  chalet: '0xe14f',
  chalet_sharp: '0xe84c',
  chalet_rounded: '0xf62b',
  chalet_outlined: '0xef3e',
  change_circle: '0xe150',
  change_circle_sharp: '0xe84d',
  change_circle_rounded: '0xf62c',
  change_circle_outlined: '0xef3f',
  change_history: '0xe151',
  change_history_sharp: '0xe84e',
  change_history_rounded: '0xf62d',
  change_history_outlined: '0xef40',
  charging_station: '0xe152',
  charging_station_sharp: '0xe84f',
  charging_station_rounded: '0xf62e',
  charging_station_outlined: '0xef41',
  chat: '0xe153',
  chat_sharp: '0xe852',
  chat_rounded: '0xf631',
  chat_outlined: '0xef44',
  chat_bubble: '0xe154',
  chat_bubble_sharp: '0xe851',
  chat_bubble_rounded: '0xf630',
  chat_bubble_outlined: '0xef43',
  chat_bubble_outline: '0xe155',
  chat_bubble_outline_sharp: '0xe850',
  chat_bubble_outline_rounded: '0xf62f',
  chat_bubble_outline_outlined: '0xef42',
  check: '0xe156',
  check_sharp: '0xe857',
  check_rounded: '0xf636',
  check_outlined: '0xef49',
  check_box: '0xe157',
  check_box_sharp: '0xe854',
  check_box_rounded: '0xf633',
  check_box_outlined: '0xef46',
  check_box_outline_blank: '0xe158',
  check_box_outline_blank_sharp: '0xe853',
  check_box_outline_blank_rounded: '0xf632',
  check_box_outline_blank_outlined: '0xef45',
  check_circle: '0xe159',
  check_circle_sharp: '0xe856',
  check_circle_rounded: '0xf635',
  check_circle_outlined: '0xef48',
  check_circle_outline: '0xe15a',
  check_circle_outline_sharp: '0xe855',
  check_circle_outline_rounded: '0xf634',
  check_circle_outline_outlined: '0xef47',
  checklist: '0xe15b',
  checklist_sharp: '0xe859',
  checklist_rounded: '0xf637',
  checklist_outlined: '0xef4a',
  checklist_rtl: '0xe15c',
  checklist_rtl_sharp: '0xe858',
  checklist_rtl_rounded: '0xf638',
  checklist_rtl_outlined: '0xef4b',
  checkroom: '0xe15d',
  checkroom_sharp: '0xe85a',
  checkroom_rounded: '0xf639',
  checkroom_outlined: '0xef4c',
  chevron_left: '0xe15e',
  chevron_left_sharp: '0xe85b',
  chevron_left_rounded: '0xf63a',
  chevron_left_outlined: '0xef4d',
  chevron_right: '0xe15f',
  chevron_right_sharp: '0xe85c',
  chevron_right_rounded: '0xf63b',
  chevron_right_outlined: '0xef4e',
  child_care: '0xe160',
  child_care_sharp: '0xe85d',
  child_care_rounded: '0xf63c',
  child_care_outlined: '0xef4f',
  child_friendly: '0xe161',
  child_friendly_sharp: '0xe85e',
  child_friendly_rounded: '0xf63d',
  child_friendly_outlined: '0xef50',
  chrome_reader_mode: '0xe162',
  chrome_reader_mode_sharp: '0xe85f',
  chrome_reader_mode_rounded: '0xf63e',
  chrome_reader_mode_outlined: '0xef51',
  church: '0xf04cd',
  church_sharp: '0xf03da',
  church_rounded: '0xf02e7',
  church_outlined: '0xf05c8',
  circle: '0xe163',
  circle_sharp: '0xe861',
  circle_rounded: '0xf640',
  circle_outlined: '0xef53',
  circle_notifications: '0xe164',
  circle_notifications_sharp: '0xe860',
  circle_notifications_rounded: '0xf63f',
  circle_notifications_outlined: '0xef52',
  class_: '0xe165',
  class_sharp: '0xe862',
  class_rounded: '0xf641',
  class_outlined: '0xef54',
  clean_hands: '0xe166',
  clean_hands_sharp: '0xe863',
  clean_hands_rounded: '0xf642',
  clean_hands_outlined: '0xef55',
  cleaning_services: '0xe167',
  cleaning_services_sharp: '0xe864',
  cleaning_services_rounded: '0xf643',
  cleaning_services_outlined: '0xef56',
  clear: '0xe168',
  clear_sharp: '0xe866',
  clear_rounded: '0xf645',
  clear_outlined: '0xef58',
  clear_all: '0xe169',
  clear_all_sharp: '0xe865',
  clear_all_rounded: '0xf644',
  clear_all_outlined: '0xef57',
  close: '0xe16a',
  close_sharp: '0xe868',
  close_rounded: '0xf647',
  close_outlined: '0xef5a',
  close_fullscreen: '0xe16b',
  close_fullscreen_sharp: '0xe867',
  close_fullscreen_rounded: '0xf646',
  close_fullscreen_outlined: '0xef59',
  closed_caption: '0xe16c',
  closed_caption_sharp: '0xe86b',
  closed_caption_rounded: '0xf64a',
  closed_caption_outlined: '0xef5d',
  closed_caption_disabled: '0xe16d',
  closed_caption_disabled_sharp: '0xe869',
  closed_caption_disabled_rounded: '0xf648',
  closed_caption_disabled_outlined: '0xef5b',
  closed_caption_off: '0xe16e',
  closed_caption_off_sharp: '0xe86a',
  closed_caption_off_rounded: '0xf649',
  closed_caption_off_outlined: '0xef5c',
  cloud: '0xe16f',
  cloud_sharp: '0xe871',
  cloud_rounded: '0xf650',
  cloud_outlined: '0xef62',
  cloud_circle: '0xe170',
  cloud_circle_sharp: '0xe86c',
  cloud_circle_rounded: '0xf64b',
  cloud_circle_outlined: '0xef5e',
  cloud_done: '0xe171',
  cloud_done_sharp: '0xe86d',
  cloud_done_rounded: '0xf64c',
  cloud_done_outlined: '0xef5f',
  cloud_download: '0xe172',
  cloud_download_sharp: '0xe86e',
  cloud_download_rounded: '0xf64d',
  cloud_download_outlined: '0xef60',
  cloud_off: '0xe173',
  cloud_off_sharp: '0xe86f',
  cloud_off_rounded: '0xf64e',
  cloud_off_outlined: '0xef61',
  cloud_queue: '0xe174',
  cloud_queue_sharp: '0xe870',
  cloud_queue_rounded: '0xf64f',
  cloud_queue_outlined: '0xef63',
  cloud_sync: '0xf04ce',
  cloud_sync_sharp: '0xf03db',
  cloud_sync_rounded: '0xf02e8',
  cloud_sync_outlined: '0xf05c9',
  cloud_upload: '0xe175',
  cloud_upload_sharp: '0xe872',
  cloud_upload_rounded: '0xf651',
  cloud_upload_outlined: '0xef64',
  cloudy_snowing: '0xf04cf',
  co2: '0xf04d0',
  co2_sharp: '0xf03dc',
  co2_rounded: '0xf02e9',
  co2_outlined: '0xf05ca',
  co_present: '0xf04d1',
  co_present_sharp: '0xf03dd',
  co_present_rounded: '0xf02ea',
  co_present_outlined: '0xf05cb',
  code: '0xe176',
  code_sharp: '0xe874',
  code_rounded: '0xf653',
  code_outlined: '0xef66',
  code_off: '0xe177',
  code_off_sharp: '0xe873',
  code_off_rounded: '0xf652',
  code_off_outlined: '0xef65',
  coffee: '0xe178',
  coffee_sharp: '0xe876',
  coffee_rounded: '0xf655',
  coffee_outlined: '0xef68',
  coffee_maker: '0xe179',
  coffee_maker_sharp: '0xe875',
  coffee_maker_rounded: '0xf654',
  coffee_maker_outlined: '0xef67',
  collections: '0xe17a',
  collections_sharp: '0xe878',
  collections_rounded: '0xf657',
  collections_outlined: '0xef6a',
  collections_bookmark: '0xe17b',
  collections_bookmark_sharp: '0xe877',
  collections_bookmark_rounded: '0xf656',
  collections_bookmark_outlined: '0xef69',
  color_lens: '0xe17c',
  color_lens_sharp: '0xe879',
  color_lens_rounded: '0xf658',
  color_lens_outlined: '0xef6b',
  colorize: '0xe17d',
  colorize_sharp: '0xe87a',
  colorize_rounded: '0xf659',
  colorize_outlined: '0xef6c',
  comment: '0xe17e',
  comment_sharp: '0xe87c',
  comment_rounded: '0xf65b',
  comment_outlined: '0xef6e',
  comment_bank: '0xe17f',
  comment_bank_sharp: '0xe87b',
  comment_bank_rounded: '0xf65a',
  comment_bank_outlined: '0xef6d',
  comments_disabled: '0xf04d2',
  comments_disabled_sharp: '0xf03de',
  comments_disabled_rounded: '0xf02eb',
  comments_disabled_outlined: '0xf05cc',
  commit: '0xf04d3',
  commit_sharp: '0xf03df',
  commit_rounded: '0xf02ec',
  commit_outlined: '0xf05cd',
  commute: '0xe180',
  commute_sharp: '0xe87d',
  commute_rounded: '0xf65c',
  commute_outlined: '0xef6f',
  compare: '0xe181',
  compare_sharp: '0xe87f',
  compare_rounded: '0xf65e',
  compare_outlined: '0xef71',
  compare_arrows: '0xe182',
  compare_arrows_sharp: '0xe87e',
  compare_arrows_rounded: '0xf65d',
  compare_arrows_outlined: '0xef70',
  compass_calibration: '0xe183',
  compass_calibration_sharp: '0xe880',
  compass_calibration_rounded: '0xf65f',
  compass_calibration_outlined: '0xef72',
  compost: '0xf04d4',
  compost_sharp: '0xf03e0',
  compost_rounded: '0xf02ed',
  compost_outlined: '0xf05ce',
  compress: '0xe184',
  compress_sharp: '0xe881',
  compress_rounded: '0xf660',
  compress_outlined: '0xef73',
  computer: '0xe185',
  computer_sharp: '0xe882',
  computer_rounded: '0xf661',
  computer_outlined: '0xef74',
  confirmation_num: '0xe186',
  confirmation_num_sharp: '0xe883',
  confirmation_num_rounded: '0xf662',
  confirmation_num_outlined: '0xef75',
  confirmation_number: '0xe186',
  confirmation_number_sharp: '0xe883',
  confirmation_number_rounded: '0xf662',
  confirmation_number_outlined: '0xef75',
  connect_without_contact: '0xe187',
  connect_without_contact_sharp: '0xe884',
  connect_without_contact_rounded: '0xf663',
  connect_without_contact_outlined: '0xef76',
  connected_tv: '0xe188',
  connected_tv_sharp: '0xe885',
  connected_tv_rounded: '0xf664',
  connected_tv_outlined: '0xef77',
  connecting_airports: '0xf04d5',
  connecting_airports_sharp: '0xf03e1',
  connecting_airports_rounded: '0xf02ee',
  connecting_airports_outlined: '0xf05cf',
  construction: '0xe189',
  construction_sharp: '0xe886',
  construction_rounded: '0xf665',
  construction_outlined: '0xef78',
  contact_emergency: '0xf0857',
  contact_emergency_sharp: '0xf0837',
  contact_emergency_rounded: '0xf0880',
  contact_emergency_outlined: '0xf089e',
  contact_mail: '0xe18a',
  contact_mail_sharp: '0xe887',
  contact_mail_rounded: '0xf666',
  contact_mail_outlined: '0xef79',
  contact_page: '0xe18b',
  contact_page_sharp: '0xe888',
  contact_page_rounded: '0xf667',
  contact_page_outlined: '0xef7a',
  contact_phone: '0xe18c',
  contact_phone_sharp: '0xe889',
  contact_phone_rounded: '0xf668',
  contact_phone_outlined: '0xef7b',
  contact_support: '0xe18d',
  contact_support_sharp: '0xe88a',
  contact_support_rounded: '0xf669',
  contact_support_outlined: '0xef7c',
  contactless: '0xe18e',
  contactless_sharp: '0xe88b',
  contactless_rounded: '0xf66a',
  contactless_outlined: '0xef7d',
  contacts: '0xe18f',
  contacts_sharp: '0xe88c',
  contacts_rounded: '0xf66b',
  contacts_outlined: '0xef7e',
  content_copy: '0xe190',
  content_copy_sharp: '0xe88d',
  content_copy_rounded: '0xf66c',
  content_copy_outlined: '0xef7f',
  content_cut: '0xe191',
  content_cut_sharp: '0xe88e',
  content_cut_rounded: '0xf66d',
  content_cut_outlined: '0xef80',
  content_paste: '0xe192',
  content_paste_sharp: '0xe890',
  content_paste_rounded: '0xf66f',
  content_paste_outlined: '0xef82',
  content_paste_go: '0xf04d6',
  content_paste_go_sharp: '0xf03e2',
  content_paste_go_rounded: '0xf02ef',
  content_paste_go_outlined: '0xf05d0',
  content_paste_off: '0xe193',
  content_paste_off_sharp: '0xe88f',
  content_paste_off_rounded: '0xf66e',
  content_paste_off_outlined: '0xef81',
  content_paste_search: '0xf04d7',
  content_paste_search_sharp: '0xf03e3',
  content_paste_search_rounded: '0xf02f0',
  content_paste_search_outlined: '0xf05d1',
  contrast: '0xf04d8',
  contrast_sharp: '0xf03e4',
  contrast_rounded: '0xf02f1',
  contrast_outlined: '0xf05d2',
  control_camera: '0xe194',
  control_camera_sharp: '0xe891',
  control_camera_rounded: '0xf670',
  control_camera_outlined: '0xef83',
  control_point: '0xe195',
  control_point_sharp: '0xe893',
  control_point_rounded: '0xf672',
  control_point_outlined: '0xef85',
  control_point_duplicate: '0xe196',
  control_point_duplicate_sharp: '0xe892',
  control_point_duplicate_rounded: '0xf671',
  control_point_duplicate_outlined: '0xef84',
  conveyor_belt: '0xf0858',
  cookie: '0xf04d9',
  cookie_sharp: '0xf03e5',
  cookie_rounded: '0xf02f2',
  cookie_outlined: '0xf05d3',
  copy: '0xe190',
  copy_sharp: '0xe88d',
  copy_rounded: '0xf66c',
  copy_outlined: '0xef7f',
  copy_all: '0xe197',
  copy_all_sharp: '0xe894',
  copy_all_rounded: '0xf673',
  copy_all_outlined: '0xef86',
  copyright: '0xe198',
  copyright_sharp: '0xe895',
  copyright_rounded: '0xf674',
  copyright_outlined: '0xef87',
  corporate_fare: '0xe19a',
  corporate_fare_sharp: '0xe897',
  corporate_fare_rounded: '0xf676',
  corporate_fare_outlined: '0xef89',
  cottage: '0xe19b',
  cottage_sharp: '0xe898',
  cottage_rounded: '0xf677',
  cottage_outlined: '0xef8a',
  countertops: '0xe19c',
  countertops_sharp: '0xe899',
  countertops_rounded: '0xf678',
  countertops_outlined: '0xef8b',
  create: '0xe19d',
  create_sharp: '0xe89b',
  create_rounded: '0xf67a',
  create_outlined: '0xef8d',
  create_new_folder: '0xe19e',
  create_new_folder_sharp: '0xe89a',
  create_new_folder_rounded: '0xf679',
  create_new_folder_outlined: '0xef8c',
  credit_card: '0xe19f',
  credit_card_sharp: '0xe89d',
  credit_card_rounded: '0xf67c',
  credit_card_outlined: '0xef8f',
  credit_card_off: '0xe1a0',
  credit_card_off_sharp: '0xe89c',
  credit_card_off_rounded: '0xf67b',
  credit_card_off_outlined: '0xef8e',
  credit_score: '0xe1a1',
  credit_score_sharp: '0xe89e',
  credit_score_rounded: '0xf67d',
  credit_score_outlined: '0xef90',
  crib: '0xe1a2',
  crib_sharp: '0xe89f',
  crib_rounded: '0xf67e',
  crib_outlined: '0xef91',
  crisis_alert: '0xf0794',
  crisis_alert_sharp: '0xf073c',
  crisis_alert_rounded: '0xf07ec',
  crisis_alert_outlined: '0xf06e4',
  crop: '0xe1a3',
  crop_sharp: '0xe8aa',
  crop_rounded: '0xf689',
  crop_outlined: '0xef9a',
  crop_16_9: '0xe1a4',
  crop_16_9_sharp: '0xe8a0',
  crop_16_9_rounded: '0xf67f',
  crop_16_9_outlined: '0xef92',
  crop_3_2: '0xe1a5',
  crop_3_2_sharp: '0xe8a1',
  crop_3_2_rounded: '0xf680',
  crop_3_2_outlined: '0xef93',
  crop_5_4: '0xe1a6',
  crop_5_4_sharp: '0xe8a2',
  crop_5_4_rounded: '0xf681',
  crop_5_4_outlined: '0xef94',
  crop_7_5: '0xe1a7',
  crop_7_5_sharp: '0xe8a3',
  crop_7_5_rounded: '0xf682',
  crop_7_5_outlined: '0xef95',
  crop_din: '0xe1a8',
  crop_din_sharp: '0xe8a4',
  crop_din_rounded: '0xf683',
  crop_din_outlined: '0xef96',
  crop_free: '0xe1a9',
  crop_free_sharp: '0xe8a5',
  crop_free_rounded: '0xf684',
  crop_free_outlined: '0xef97',
  crop_landscape: '0xe1aa',
  crop_landscape_sharp: '0xe8a6',
  crop_landscape_rounded: '0xf685',
  crop_landscape_outlined: '0xef98',
  crop_original: '0xe1ab',
  crop_original_sharp: '0xe8a7',
  crop_original_rounded: '0xf686',
  crop_original_outlined: '0xef99',
  crop_portrait: '0xe1ac',
  crop_portrait_sharp: '0xe8a8',
  crop_portrait_rounded: '0xf687',
  crop_portrait_outlined: '0xef9b',
  crop_rotate: '0xe1ad',
  crop_rotate_sharp: '0xe8a9',
  crop_rotate_rounded: '0xf688',
  crop_rotate_outlined: '0xef9c',
  crop_square: '0xe1ae',
  crop_square_sharp: '0xe8ab',
  crop_square_rounded: '0xf68a',
  crop_square_outlined: '0xef9d',
  cruelty_free: '0xf04da',
  cruelty_free_sharp: '0xf03e6',
  cruelty_free_rounded: '0xf02f3',
  cruelty_free_outlined: '0xf05d4',
  css: '0xf04db',
  css_sharp: '0xf03e7',
  css_rounded: '0xf02f4',
  css_outlined: '0xf05d5',
  currency_bitcoin: '0xf06bc',
  currency_bitcoin_sharp: '0xf06af',
  currency_bitcoin_rounded: '0xf06c9',
  currency_bitcoin_outlined: '0xf054a',
  currency_exchange: '0xf04dc',
  currency_exchange_sharp: '0xf03e8',
  currency_exchange_rounded: '0xf02f5',
  currency_exchange_outlined: '0xf05d6',
  currency_franc: '0xf04dd',
  currency_franc_sharp: '0xf03e9',
  currency_franc_rounded: '0xf02f6',
  currency_franc_outlined: '0xf05d7',
  currency_lira: '0xf04de',
  currency_lira_sharp: '0xf03ea',
  currency_lira_rounded: '0xf02f7',
  currency_lira_outlined: '0xf05d8',
  currency_pound: '0xf04df',
  currency_pound_sharp: '0xf03eb',
  currency_pound_rounded: '0xf02f8',
  currency_pound_outlined: '0xf05d9',
  currency_ruble: '0xf04e0',
  currency_ruble_sharp: '0xf03ec',
  currency_ruble_rounded: '0xf02f9',
  currency_ruble_outlined: '0xf05da',
  currency_rupee: '0xf04e1',
  currency_rupee_sharp: '0xf03ed',
  currency_rupee_rounded: '0xf02fa',
  currency_rupee_outlined: '0xf05db',
  currency_yen: '0xf04e2',
  currency_yen_sharp: '0xf03ee',
  currency_yen_rounded: '0xf02fb',
  currency_yen_outlined: '0xf05dc',
  currency_yuan: '0xf04e3',
  currency_yuan_sharp: '0xf03ef',
  currency_yuan_rounded: '0xf02fc',
  currency_yuan_outlined: '0xf05dd',
  curtains: '0xf0795',
  curtains_sharp: '0xf073e',
  curtains_rounded: '0xf07ee',
  curtains_outlined: '0xf06e6',
  curtains_closed: '0xf0796',
  curtains_closed_sharp: '0xf073d',
  curtains_closed_rounded: '0xf07ed',
  curtains_closed_outlined: '0xf06e5',
  cut: '0xe191',
  cut_sharp: '0xe88e',
  cut_rounded: '0xf66d',
  cut_outlined: '0xef80',
  cyclone: '0xf0797',
  cyclone_sharp: '0xf073f',
  cyclone_rounded: '0xf07ef',
  cyclone_outlined: '0xf06e7',
  dangerous: '0xe1af',
  dangerous_sharp: '0xe8ac',
  dangerous_rounded: '0xf68b',
  dangerous_outlined: '0xef9e',
  dark_mode: '0xe1b0',
  dark_mode_sharp: '0xe8ad',
  dark_mode_rounded: '0xf68c',
  dark_mode_outlined: '0xef9f',
  dashboard: '0xe1b1',
  dashboard_sharp: '0xe8af',
  dashboard_rounded: '0xf68e',
  dashboard_outlined: '0xefa1',
  dashboard_customize: '0xe1b2',
  dashboard_customize_sharp: '0xe8ae',
  dashboard_customize_rounded: '0xf68d',
  dashboard_customize_outlined: '0xefa0',
  data_array: '0xf04e4',
  data_array_sharp: '0xf03f0',
  data_array_rounded: '0xf02fd',
  data_array_outlined: '0xf05de',
  data_exploration: '0xf04e5',
  data_exploration_sharp: '0xf03f1',
  data_exploration_rounded: '0xf02fe',
  data_exploration_outlined: '0xf05df',
  data_object: '0xf04e6',
  data_object_sharp: '0xf03f2',
  data_object_rounded: '0xf02ff',
  data_object_outlined: '0xf05e0',
  data_saver_off: '0xe1b3',
  data_saver_off_sharp: '0xe8b0',
  data_saver_off_rounded: '0xf68f',
  data_saver_off_outlined: '0xefa2',
  data_saver_on: '0xe1b4',
  data_saver_on_sharp: '0xe8b1',
  data_saver_on_rounded: '0xf690',
  data_saver_on_outlined: '0xefa3',
  data_thresholding: '0xf04e7',
  data_thresholding_sharp: '0xf03f3',
  data_thresholding_rounded: '0xf0300',
  data_thresholding_outlined: '0xf05e1',
  data_usage: '0xe1b5',
  data_usage_sharp: '0xe8b2',
  data_usage_rounded: '0xf691',
  data_usage_outlined: '0xefa4',
  dataset: '0xf0798',
  dataset_sharp: '0xf0741',
  dataset_rounded: '0xf07f1',
  dataset_outlined: '0xf06e9',
  dataset_linked: '0xf0799',
  dataset_linked_sharp: '0xf0740',
  dataset_linked_rounded: '0xf07f0',
  dataset_linked_outlined: '0xf06e8',
  date_range: '0xe1b6',
  date_range_sharp: '0xe8b3',
  date_range_rounded: '0xf692',
  date_range_outlined: '0xefa5',
  deblur: '0xf04e8',
  deblur_sharp: '0xf03f4',
  deblur_rounded: '0xf0301',
  deblur_outlined: '0xf05e2',
  deck: '0xe1b7',
  deck_sharp: '0xe8b4',
  deck_rounded: '0xf693',
  deck_outlined: '0xefa6',
  dehaze: '0xe1b8',
  dehaze_sharp: '0xe8b5',
  dehaze_rounded: '0xf694',
  dehaze_outlined: '0xefa7',
  delete: '0xe1b9',
  delete_sharp: '0xe8b8',
  delete_rounded: '0xf697',
  delete_outlined: '0xefaa',
  delete_forever: '0xe1ba',
  delete_forever_sharp: '0xe8b6',
  delete_forever_rounded: '0xf695',
  delete_forever_outlined: '0xefa8',
  delete_outline: '0xe1bb',
  delete_outline_sharp: '0xe8b7',
  delete_outline_rounded: '0xf696',
  delete_outline_outlined: '0xefa9',
  delete_sweep: '0xe1bc',
  delete_sweep_sharp: '0xe8b9',
  delete_sweep_rounded: '0xf698',
  delete_sweep_outlined: '0xefab',
  delivery_dining: '0xe1bd',
  delivery_dining_sharp: '0xe8ba',
  delivery_dining_rounded: '0xf699',
  delivery_dining_outlined: '0xefac',
  density_large: '0xf04e9',
  density_large_sharp: '0xf03f5',
  density_large_rounded: '0xf0302',
  density_large_outlined: '0xf05e3',
  density_medium: '0xf04ea',
  density_medium_sharp: '0xf03f6',
  density_medium_rounded: '0xf0303',
  density_medium_outlined: '0xf05e4',
  density_small: '0xf04eb',
  density_small_sharp: '0xf03f7',
  density_small_rounded: '0xf0304',
  density_small_outlined: '0xf05e5',
  departure_board: '0xe1be',
  departure_board_sharp: '0xe8bb',
  departure_board_rounded: '0xf69a',
  departure_board_outlined: '0xefad',
  description: '0xe1bf',
  description_sharp: '0xe8bc',
  description_rounded: '0xf69b',
  description_outlined: '0xefae',
  deselect: '0xf04ec',
  deselect_sharp: '0xf03f8',
  deselect_rounded: '0xf0305',
  deselect_outlined: '0xf05e6',
  design_services: '0xe1c0',
  design_services_sharp: '0xe8bd',
  design_services_rounded: '0xf69c',
  design_services_outlined: '0xefaf',
  desk: '0xf079a',
  desk_sharp: '0xf0742',
  desk_rounded: '0xf07f2',
  desk_outlined: '0xf06ea',
  desktop_access_disabled: '0xe1c1',
  desktop_access_disabled_sharp: '0xe8be',
  desktop_access_disabled_rounded: '0xf69d',
  desktop_access_disabled_outlined: '0xefb0',
  desktop_mac: '0xe1c2',
  desktop_mac_sharp: '0xe8bf',
  desktop_mac_rounded: '0xf69e',
  desktop_mac_outlined: '0xefb1',
  desktop_windows: '0xe1c3',
  desktop_windows_sharp: '0xe8c0',
  desktop_windows_rounded: '0xf69f',
  desktop_windows_outlined: '0xefb2',
  details: '0xe1c4',
  details_sharp: '0xe8c1',
  details_rounded: '0xf6a0',
  details_outlined: '0xefb3',
  developer_board: '0xe1c5',
  developer_board_sharp: '0xe8c3',
  developer_board_rounded: '0xf6a2',
  developer_board_outlined: '0xefb5',
  developer_board_off: '0xe1c6',
  developer_board_off_sharp: '0xe8c2',
  developer_board_off_rounded: '0xf6a1',
  developer_board_off_outlined: '0xefb4',
  developer_mode: '0xe1c7',
  developer_mode_sharp: '0xe8c4',
  developer_mode_rounded: '0xf6a3',
  developer_mode_outlined: '0xefb6',
  device_hub: '0xe1c8',
  device_hub_sharp: '0xe8c5',
  device_hub_rounded: '0xf6a4',
  device_hub_outlined: '0xefb7',
  device_thermostat: '0xe1c9',
  device_thermostat_sharp: '0xe8c6',
  device_thermostat_rounded: '0xf6a5',
  device_thermostat_outlined: '0xefb8',
  device_unknown: '0xe1ca',
  device_unknown_sharp: '0xe8c7',
  device_unknown_rounded: '0xf6a6',
  device_unknown_outlined: '0xefb9',
  devices: '0xe1cb',
  devices_sharp: '0xe8c9',
  devices_rounded: '0xf6a8',
  devices_outlined: '0xefbb',
  devices_fold: '0xf079b',
  devices_fold_sharp: '0xf0743',
  devices_fold_rounded: '0xf07f3',
  devices_fold_outlined: '0xf06eb',
  devices_other: '0xe1cc',
  devices_other_sharp: '0xe8c8',
  devices_other_rounded: '0xf6a7',
  devices_other_outlined: '0xefba',
  dew_point: '0xf0859',
  dialer_sip: '0xe1cd',
  dialer_sip_sharp: '0xe8ca',
  dialer_sip_rounded: '0xf6a9',
  dialer_sip_outlined: '0xefbc',
  dialpad: '0xe1ce',
  dialpad_sharp: '0xe8cb',
  dialpad_rounded: '0xf6aa',
  dialpad_outlined: '0xefbd',
  diamond: '0xf04ed',
  diamond_sharp: '0xf03f9',
  diamond_rounded: '0xf0306',
  diamond_outlined: '0xf05e7',
  difference: '0xf04ee',
  difference_sharp: '0xf03fa',
  difference_rounded: '0xf0307',
  difference_outlined: '0xf05e8',
  dining: '0xe1cf',
  dining_sharp: '0xe8cc',
  dining_rounded: '0xf6ab',
  dining_outlined: '0xefbe',
  dinner_dining: '0xe1d0',
  dinner_dining_sharp: '0xe8cd',
  dinner_dining_rounded: '0xf6ac',
  dinner_dining_outlined: '0xefbf',
  directions: '0xe1d1',
  directions_sharp: '0xe8d9',
  directions_rounded: '0xf6b7',
  directions_outlined: '0xefc8',
  directions_bike: '0xe1d2',
  directions_bike_sharp: '0xe8ce',
  directions_bike_rounded: '0xf6ad',
  directions_bike_outlined: '0xefc0',
  directions_boat: '0xe1d3',
  directions_boat_sharp: '0xe8d0',
  directions_boat_rounded: '0xf6af',
  directions_boat_outlined: '0xefc2',
  directions_boat_filled: '0xe1d4',
  directions_boat_filled_sharp: '0xe8cf',
  directions_boat_filled_rounded: '0xf6ae',
  directions_boat_filled_outlined: '0xefc1',
  directions_bus: '0xe1d5',
  directions_bus_sharp: '0xe8d2',
  directions_bus_rounded: '0xf6b1',
  directions_bus_outlined: '0xefc4',
  directions_bus_filled: '0xe1d6',
  directions_bus_filled_sharp: '0xe8d1',
  directions_bus_filled_rounded: '0xf6b0',
  directions_bus_filled_outlined: '0xefc3',
  directions_car: '0xe1d7',
  directions_car_sharp: '0xe8d4',
  directions_car_rounded: '0xf6b3',
  directions_car_outlined: '0xefc6',
  directions_car_filled: '0xe1d8',
  directions_car_filled_sharp: '0xe8d3',
  directions_car_filled_rounded: '0xf6b2',
  directions_car_filled_outlined: '0xefc5',
  directions_ferry: '0xe1d3',
  directions_ferry_sharp: '0xe8d0',
  directions_ferry_rounded: '0xf6af',
  directions_ferry_outlined: '0xefc2',
  directions_off: '0xe1d9',
  directions_off_sharp: '0xe8d5',
  directions_off_rounded: '0xf6b4',
  directions_off_outlined: '0xefc7',
  directions_railway: '0xe1da',
  directions_railway_sharp: '0xe8d7',
  directions_railway_rounded: '0xf6b6',
  directions_railway_outlined: '0xefca',
  directions_railway_filled: '0xe1db',
  directions_railway_filled_sharp: '0xe8d6',
  directions_railway_filled_rounded: '0xf6b5',
  directions_railway_filled_outlined: '0xefc9',
  directions_run: '0xe1dc',
  directions_run_sharp: '0xe8d8',
  directions_run_rounded: '0xf6b8',
  directions_run_outlined: '0xefcb',
  directions_subway: '0xe1dd',
  directions_subway_sharp: '0xe8db',
  directions_subway_rounded: '0xf6ba',
  directions_subway_outlined: '0xefcd',
  directions_subway_filled: '0xe1de',
  directions_subway_filled_sharp: '0xe8da',
  directions_subway_filled_rounded: '0xf6b9',
  directions_subway_filled_outlined: '0xefcc',
  directions_train: '0xe1da',
  directions_train_sharp: '0xe8d7',
  directions_train_rounded: '0xf6b6',
  directions_train_outlined: '0xefca',
  directions_transit: '0xe1df',
  directions_transit_sharp: '0xe8dd',
  directions_transit_rounded: '0xf6bc',
  directions_transit_outlined: '0xefcf',
  directions_transit_filled: '0xe1e0',
  directions_transit_filled_sharp: '0xe8dc',
  directions_transit_filled_rounded: '0xf6bb',
  directions_transit_filled_outlined: '0xefce',
  directions_walk: '0xe1e1',
  directions_walk_sharp: '0xe8de',
  directions_walk_rounded: '0xf6bd',
  directions_walk_outlined: '0xefd0',
  dirty_lens: '0xe1e2',
  dirty_lens_sharp: '0xe8df',
  dirty_lens_rounded: '0xf6be',
  dirty_lens_outlined: '0xefd1',
  disabled_by_default: '0xe1e3',
  disabled_by_default_sharp: '0xe8e0',
  disabled_by_default_rounded: '0xf6bf',
  disabled_by_default_outlined: '0xefd2',
  disabled_visible: '0xf04ef',
  disabled_visible_sharp: '0xf03fb',
  disabled_visible_rounded: '0xf0308',
  disabled_visible_outlined: '0xf05e9',
  disc_full: '0xe1e4',
  disc_full_sharp: '0xe8e1',
  disc_full_rounded: '0xf6c0',
  disc_full_outlined: '0xefd3',
  discord: '0xf04f0',
  discord_sharp: '0xf03fc',
  discord_rounded: '0xf0309',
  discord_outlined: '0xf05ea',
  discount: '0xf06bd',
  discount_sharp: '0xf06b0',
  discount_rounded: '0xf06ca',
  discount_outlined: '0xf06a3',
  display_settings: '0xf04f1',
  display_settings_sharp: '0xf03fd',
  display_settings_rounded: '0xf030a',
  display_settings_outlined: '0xf05eb',
  diversity_1: '0xf085a',
  diversity_1_sharp: '0xf0838',
  diversity_1_rounded: '0xf0881',
  diversity_1_outlined: '0xf089f',
  diversity_2: '0xf085b',
  diversity_2_sharp: '0xf0839',
  diversity_2_rounded: '0xf0882',
  diversity_2_outlined: '0xf08a0',
  diversity_3: '0xf085c',
  diversity_3_sharp: '0xf083a',
  diversity_3_rounded: '0xf0883',
  diversity_3_outlined: '0xf08a1',
  dnd_forwardslash: '0xe1eb',
  dnd_forwardslash_sharp: '0xe8e7',
  dnd_forwardslash_rounded: '0xf6c6',
  dnd_forwardslash_outlined: '0xefd9',
  dns: '0xe1e5',
  dns_sharp: '0xe8e2',
  dns_rounded: '0xf6c1',
  dns_outlined: '0xefd4',
  do_disturb: '0xe1e6',
  do_disturb_sharp: '0xe8e6',
  do_disturb_rounded: '0xf6c5',
  do_disturb_outlined: '0xefd8',
  do_disturb_alt: '0xe1e7',
  do_disturb_alt_sharp: '0xe8e3',
  do_disturb_alt_rounded: '0xf6c2',
  do_disturb_alt_outlined: '0xefd5',
  do_disturb_off: '0xe1e8',
  do_disturb_off_sharp: '0xe8e4',
  do_disturb_off_rounded: '0xf6c3',
  do_disturb_off_outlined: '0xefd6',
  do_disturb_on: '0xe1e9',
  do_disturb_on_sharp: '0xe8e5',
  do_disturb_on_rounded: '0xf6c4',
  do_disturb_on_outlined: '0xefd7',
  do_not_disturb: '0xe1ea',
  do_not_disturb_sharp: '0xe8eb',
  do_not_disturb_rounded: '0xf6ca',
  do_not_disturb_outlined: '0xefdd',
  do_not_disturb_alt: '0xe1eb',
  do_not_disturb_alt_sharp: '0xe8e7',
  do_not_disturb_alt_rounded: '0xf6c6',
  do_not_disturb_alt_outlined: '0xefd9',
  do_not_disturb_off: '0xe1ec',
  do_not_disturb_off_sharp: '0xe8e8',
  do_not_disturb_off_rounded: '0xf6c7',
  do_not_disturb_off_outlined: '0xefda',
  do_not_disturb_on: '0xe1ed',
  do_not_disturb_on_sharp: '0xe8e9',
  do_not_disturb_on_rounded: '0xf6c8',
  do_not_disturb_on_outlined: '0xefdb',
  do_not_disturb_on_total_silence: '0xe1ee',
  do_not_disturb_on_total_silence_sharp: '0xe8ea',
  do_not_disturb_on_total_silence_rounded: '0xf6c9',
  do_not_disturb_on_total_silence_outlined: '0xefdc',
  do_not_step: '0xe1ef',
  do_not_step_sharp: '0xe8ec',
  do_not_step_rounded: '0xf6cb',
  do_not_step_outlined: '0xefde',
  do_not_touch: '0xe1f0',
  do_not_touch_sharp: '0xe8ed',
  do_not_touch_rounded: '0xf6cc',
  do_not_touch_outlined: '0xefdf',
  dock: '0xe1f1',
  dock_sharp: '0xe8ee',
  dock_rounded: '0xf6cd',
  dock_outlined: '0xefe0',
  document_scanner: '0xe1f2',
  document_scanner_sharp: '0xe8ef',
  document_scanner_rounded: '0xf6ce',
  document_scanner_outlined: '0xefe1',
  domain: '0xe1f3',
  domain_sharp: '0xe8f1',
  domain_rounded: '0xf6d0',
  domain_outlined: '0xefe3',
  domain_add: '0xf04f2',
  domain_add_sharp: '0xf03fe',
  domain_add_rounded: '0xf030b',
  domain_add_outlined: '0xf05ec',
  domain_disabled: '0xe1f4',
  domain_disabled_sharp: '0xe8f0',
  domain_disabled_rounded: '0xf6cf',
  domain_disabled_outlined: '0xefe2',
  domain_verification: '0xe1f5',
  domain_verification_sharp: '0xe8f2',
  domain_verification_rounded: '0xf6d1',
  domain_verification_outlined: '0xefe4',
  done: '0xe1f6',
  done_sharp: '0xe8f5',
  done_rounded: '0xf6d4',
  done_outlined: '0xefe7',
  done_all: '0xe1f7',
  done_all_sharp: '0xe8f3',
  done_all_rounded: '0xf6d2',
  done_all_outlined: '0xefe5',
  done_outline: '0xe1f8',
  done_outline_sharp: '0xe8f4',
  done_outline_rounded: '0xf6d3',
  done_outline_outlined: '0xefe6',
  donut_large: '0xe1f9',
  donut_large_sharp: '0xe8f6',
  donut_large_rounded: '0xf6d5',
  donut_large_outlined: '0xefe8',
  donut_small: '0xe1fa',
  donut_small_sharp: '0xe8f7',
  donut_small_rounded: '0xf6d6',
  donut_small_outlined: '0xefe9',
  door_back_door: '0xe1fb',
  door_back_door_sharp: '0xe8f8',
  door_back_door_rounded: '0xf6d7',
  door_back_door_outlined: '0xefea',
  door_front_door: '0xe1fc',
  door_front_door_sharp: '0xe8f9',
  door_front_door_rounded: '0xf6d8',
  door_front_door_outlined: '0xefeb',
  door_sliding: '0xe1fd',
  door_sliding_sharp: '0xe8fa',
  door_sliding_rounded: '0xf6d9',
  door_sliding_outlined: '0xefec',
  doorbell: '0xe1fe',
  doorbell_sharp: '0xe8fb',
  doorbell_rounded: '0xf6da',
  doorbell_outlined: '0xefed',
  double_arrow: '0xe1ff',
  double_arrow_sharp: '0xe8fc',
  double_arrow_rounded: '0xf6db',
  double_arrow_outlined: '0xefee',
  downhill_skiing: '0xe200',
  downhill_skiing_sharp: '0xe8fd',
  downhill_skiing_rounded: '0xf6dc',
  downhill_skiing_outlined: '0xefef',
  download: '0xe201',
  download_sharp: '0xe900',
  download_rounded: '0xf6df',
  download_outlined: '0xeff2',
  download_done: '0xe202',
  download_done_sharp: '0xe8fe',
  download_done_rounded: '0xf6dd',
  download_done_outlined: '0xeff0',
  download_for_offline: '0xe203',
  download_for_offline_sharp: '0xe8ff',
  download_for_offline_rounded: '0xf6de',
  download_for_offline_outlined: '0xeff1',
  downloading: '0xe204',
  downloading_sharp: '0xe901',
  downloading_rounded: '0xf6e0',
  downloading_outlined: '0xeff3',
  drafts: '0xe205',
  drafts_sharp: '0xe902',
  drafts_rounded: '0xf6e1',
  drafts_outlined: '0xeff4',
  drag_handle: '0xe206',
  drag_handle_sharp: '0xe903',
  drag_handle_rounded: '0xf6e2',
  drag_handle_outlined: '0xeff5',
  drag_indicator: '0xe207',
  drag_indicator_sharp: '0xe904',
  drag_indicator_rounded: '0xf6e3',
  drag_indicator_outlined: '0xeff6',
  draw: '0xf04f3',
  draw_sharp: '0xf03ff',
  draw_rounded: '0xf030c',
  draw_outlined: '0xf05ed',
  drive_eta: '0xe208',
  drive_eta_sharp: '0xe905',
  drive_eta_rounded: '0xf6e4',
  drive_eta_outlined: '0xeff7',
  drive_file_move: '0xe209',
  drive_file_move_sharp: '0xe906',
  drive_file_move_rounded: '0xf6e5',
  drive_file_move_outlined: '0xeff8',
  drive_file_move_outline: '0xe20a',
  drive_file_move_rtl: '0xf04f4',
  drive_file_move_rtl_sharp: '0xf0400',
  drive_file_move_rtl_rounded: '0xf030d',
  drive_file_move_rtl_outlined: '0xf05ee',
  drive_file_rename_outline: '0xe20b',
  drive_file_rename_outline_sharp: '0xe907',
  drive_file_rename_outline_rounded: '0xf6e6',
  drive_file_rename_outline_outlined: '0xeff9',
  drive_folder_upload: '0xe20c',
  drive_folder_upload_sharp: '0xe908',
  drive_folder_upload_rounded: '0xf6e7',
  drive_folder_upload_outlined: '0xeffa',
  dry: '0xe20d',
  dry_sharp: '0xe90a',
  dry_rounded: '0xf6e9',
  dry_outlined: '0xeffc',
  dry_cleaning: '0xe20e',
  dry_cleaning_sharp: '0xe909',
  dry_cleaning_rounded: '0xf6e8',
  dry_cleaning_outlined: '0xeffb',
  duo: '0xe20f',
  duo_sharp: '0xe90b',
  duo_rounded: '0xf6ea',
  duo_outlined: '0xeffd',
  dvr: '0xe210',
  dvr_sharp: '0xe90c',
  dvr_rounded: '0xf6eb',
  dvr_outlined: '0xeffe',
  dynamic_feed: '0xe211',
  dynamic_feed_sharp: '0xe90d',
  dynamic_feed_rounded: '0xf6ec',
  dynamic_feed_outlined: '0xefff',
  dynamic_form: '0xe212',
  dynamic_form_sharp: '0xe90e',
  dynamic_form_rounded: '0xf6ed',
  dynamic_form_outlined: '0xf000',
  e_mobiledata: '0xe213',
  e_mobiledata_sharp: '0xe90f',
  e_mobiledata_rounded: '0xf6ee',
  e_mobiledata_outlined: '0xf001',
  earbuds: '0xe214',
  earbuds_sharp: '0xe911',
  earbuds_rounded: '0xf6f0',
  earbuds_outlined: '0xf003',
  earbuds_battery: '0xe215',
  earbuds_battery_sharp: '0xe910',
  earbuds_battery_rounded: '0xf6ef',
  earbuds_battery_outlined: '0xf002',
  east: '0xe216',
  east_sharp: '0xe912',
  east_rounded: '0xf6f1',
  east_outlined: '0xf004',
  eco: '0xe217',
  eco_sharp: '0xe913',
  eco_rounded: '0xf6f2',
  eco_outlined: '0xf005',
  edgesensor_high: '0xe218',
  edgesensor_high_sharp: '0xe914',
  edgesensor_high_rounded: '0xf6f3',
  edgesensor_high_outlined: '0xf006',
  edgesensor_low: '0xe219',
  edgesensor_low_sharp: '0xe915',
  edgesensor_low_rounded: '0xf6f4',
  edgesensor_low_outlined: '0xf007',
  edit: '0xe21a',
  edit_sharp: '0xe91c',
  edit_rounded: '0xf6fb',
  edit_outlined: '0xf00d',
  edit_attributes: '0xe21b',
  edit_attributes_sharp: '0xe916',
  edit_attributes_rounded: '0xf6f5',
  edit_attributes_outlined: '0xf008',
  edit_calendar: '0xf04f5',
  edit_calendar_sharp: '0xf0401',
  edit_calendar_rounded: '0xf030e',
  edit_calendar_outlined: '0xf05ef',
  edit_document: '0xf085d',
  edit_location: '0xe21c',
  edit_location_sharp: '0xe918',
  edit_location_rounded: '0xf6f7',
  edit_location_outlined: '0xf00a',
  edit_location_alt: '0xe21d',
  edit_location_alt_sharp: '0xe917',
  edit_location_alt_rounded: '0xf6f6',
  edit_location_alt_outlined: '0xf009',
  edit_note: '0xf04f6',
  edit_note_sharp: '0xf0402',
  edit_note_rounded: '0xf030f',
  edit_note_outlined: '0xf05f0',
  edit_notifications: '0xe21e',
  edit_notifications_sharp: '0xe919',
  edit_notifications_rounded: '0xf6f8',
  edit_notifications_outlined: '0xf00b',
  edit_off: '0xe21f',
  edit_off_sharp: '0xe91a',
  edit_off_rounded: '0xf6f9',
  edit_off_outlined: '0xf00c',
  edit_road: '0xe220',
  edit_road_sharp: '0xe91b',
  edit_road_rounded: '0xf6fa',
  edit_road_outlined: '0xf00e',
  edit_square: '0xf085e',
  egg: '0xf04f8',
  egg_sharp: '0xf0404',
  egg_rounded: '0xf0311',
  egg_outlined: '0xf05f2',
  egg_alt: '0xf04f7',
  egg_alt_sharp: '0xf0403',
  egg_alt_rounded: '0xf0310',
  egg_alt_outlined: '0xf05f1',
  eject: '0xe221',
  eject_sharp: '0xe91d',
  eject_rounded: '0xf6fc',
  eject_outlined: '0xf00f',
  elderly: '0xe222',
  elderly_sharp: '0xe91e',
  elderly_rounded: '0xf6fd',
  elderly_outlined: '0xf010',
  elderly_woman: '0xf04f9',
  elderly_woman_sharp: '0xf0405',
  elderly_woman_rounded: '0xf0312',
  elderly_woman_outlined: '0xf05f3',
  electric_bike: '0xe223',
  electric_bike_sharp: '0xe91f',
  electric_bike_rounded: '0xf6fe',
  electric_bike_outlined: '0xf011',
  electric_bolt: '0xf079c',
  electric_bolt_sharp: '0xf0744',
  electric_bolt_rounded: '0xf07f4',
  electric_bolt_outlined: '0xf06ec',
  electric_car: '0xe224',
  electric_car_sharp: '0xe920',
  electric_car_rounded: '0xf6ff',
  electric_car_outlined: '0xf012',
  electric_meter: '0xf079d',
  electric_meter_sharp: '0xf0745',
  electric_meter_rounded: '0xf07f5',
  electric_meter_outlined: '0xf06ed',
  electric_moped: '0xe225',
  electric_moped_sharp: '0xe921',
  electric_moped_rounded: '0xf700',
  electric_moped_outlined: '0xf013',
  electric_rickshaw: '0xe226',
  electric_rickshaw_sharp: '0xe922',
  electric_rickshaw_rounded: '0xf701',
  electric_rickshaw_outlined: '0xf014',
  electric_scooter: '0xe227',
  electric_scooter_sharp: '0xe923',
  electric_scooter_rounded: '0xf702',
  electric_scooter_outlined: '0xf015',
  electrical_services: '0xe228',
  electrical_services_sharp: '0xe924',
  electrical_services_rounded: '0xf703',
  electrical_services_outlined: '0xf016',
  elevator: '0xe229',
  elevator_sharp: '0xe925',
  elevator_rounded: '0xf704',
  elevator_outlined: '0xf017',
  email: '0xe22a',
  email_sharp: '0xe926',
  email_rounded: '0xf705',
  email_outlined: '0xf018',
  emergency: '0xf04fa',
  emergency_sharp: '0xf0406',
  emergency_rounded: '0xf0313',
  emergency_outlined: '0xf05f4',
  emergency_recording: '0xf079e',
  emergency_recording_sharp: '0xf0746',
  emergency_recording_rounded: '0xf07f6',
  emergency_recording_outlined: '0xf06ee',
  emergency_share: '0xf079f',
  emergency_share_sharp: '0xf0747',
  emergency_share_rounded: '0xf07f7',
  emergency_share_outlined: '0xf06ef',
  emoji_emotions: '0xe22b',
  emoji_emotions_sharp: '0xe927',
  emoji_emotions_rounded: '0xf706',
  emoji_emotions_outlined: '0xf019',
  emoji_events: '0xe22c',
  emoji_events_sharp: '0xe928',
  emoji_events_rounded: '0xf707',
  emoji_events_outlined: '0xf01a',
  emoji_flags: '0xe22d',
  emoji_flags_sharp: '0xe929',
  emoji_flags_rounded: '0xf708',
  emoji_flags_outlined: '0xf01b',
  emoji_food_beverage: '0xe22e',
  emoji_food_beverage_sharp: '0xe92a',
  emoji_food_beverage_rounded: '0xf709',
  emoji_food_beverage_outlined: '0xf01c',
  emoji_nature: '0xe22f',
  emoji_nature_sharp: '0xe92b',
  emoji_nature_rounded: '0xf70a',
  emoji_nature_outlined: '0xf01d',
  emoji_objects: '0xe230',
  emoji_objects_sharp: '0xe92c',
  emoji_objects_rounded: '0xf70b',
  emoji_objects_outlined: '0xf01e',
  emoji_people: '0xe231',
  emoji_people_sharp: '0xe92d',
  emoji_people_rounded: '0xf70c',
  emoji_people_outlined: '0xf01f',
  emoji_symbols: '0xe232',
  emoji_symbols_sharp: '0xe92e',
  emoji_symbols_rounded: '0xf70d',
  emoji_symbols_outlined: '0xf020',
  emoji_transportation: '0xe233',
  emoji_transportation_sharp: '0xe92f',
  emoji_transportation_rounded: '0xf70e',
  emoji_transportation_outlined: '0xf021',
  energy_savings_leaf: '0xf07a0',
  energy_savings_leaf_sharp: '0xf0748',
  energy_savings_leaf_rounded: '0xf07f8',
  energy_savings_leaf_outlined: '0xf06f0',
  engineering: '0xe234',
  engineering_sharp: '0xe930',
  engineering_rounded: '0xf70f',
  engineering_outlined: '0xf022',
  enhance_photo_translate: '0xe131',
  enhance_photo_translate_sharp: '0xe82d',
  enhance_photo_translate_rounded: '0xf60c',
  enhance_photo_translate_outlined: '0xef1f',
  enhanced_encryption: '0xe235',
  enhanced_encryption_sharp: '0xe931',
  enhanced_encryption_rounded: '0xf710',
  enhanced_encryption_outlined: '0xf023',
  equalizer: '0xe236',
  equalizer_sharp: '0xe932',
  equalizer_rounded: '0xf711',
  equalizer_outlined: '0xf024',
  error: '0xe237',
  error_sharp: '0xe934',
  error_rounded: '0xf713',
  error_outlined: '0xf026',
  error_outline: '0xe238',
  error_outline_sharp: '0xe933',
  error_outline_rounded: '0xf712',
  error_outline_outlined: '0xf025',
  escalator: '0xe239',
  escalator_sharp: '0xe935',
  escalator_rounded: '0xf714',
  escalator_outlined: '0xf027',
  escalator_warning: '0xe23a',
  escalator_warning_sharp: '0xe936',
  escalator_warning_rounded: '0xf715',
  escalator_warning_outlined: '0xf028',
  euro: '0xe23b',
  euro_sharp: '0xe937',
  euro_rounded: '0xf716',
  euro_outlined: '0xf029',
  euro_symbol: '0xe23c',
  euro_symbol_sharp: '0xe938',
  euro_symbol_rounded: '0xf717',
  euro_symbol_outlined: '0xf02a',
  ev_station: '0xe23d',
  ev_station_sharp: '0xe939',
  ev_station_rounded: '0xf718',
  ev_station_outlined: '0xf02b',
  event: '0xe23e',
  event_sharp: '0xe93e',
  event_rounded: '0xf71c',
  event_outlined: '0xf02f',
  event_available: '0xe23f',
  event_available_sharp: '0xe93a',
  event_available_rounded: '0xf719',
  event_available_outlined: '0xf02c',
  event_busy: '0xe240',
  event_busy_sharp: '0xe93b',
  event_busy_rounded: '0xf71a',
  event_busy_outlined: '0xf02d',
  event_note: '0xe241',
  event_note_sharp: '0xe93c',
  event_note_rounded: '0xf71b',
  event_note_outlined: '0xf02e',
  event_repeat: '0xf04fb',
  event_repeat_sharp: '0xf0407',
  event_repeat_rounded: '0xf0314',
  event_repeat_outlined: '0xf05f5',
  event_seat: '0xe242',
  event_seat_sharp: '0xe93d',
  event_seat_rounded: '0xf71d',
  event_seat_outlined: '0xf030',
  exit_to_app: '0xe243',
  exit_to_app_sharp: '0xe93f',
  exit_to_app_rounded: '0xf71e',
  exit_to_app_outlined: '0xf031',
  expand: '0xe244',
  expand_sharp: '0xe942',
  expand_rounded: '0xf721',
  expand_outlined: '0xf034',
  expand_circle_down: '0xf04fc',
  expand_circle_down_sharp: '0xf0408',
  expand_circle_down_rounded: '0xf0315',
  expand_circle_down_outlined: '0xf05f6',
  expand_less: '0xe245',
  expand_less_sharp: '0xe940',
  expand_less_rounded: '0xf71f',
  expand_less_outlined: '0xf032',
  expand_more: '0xe246',
  expand_more_sharp: '0xe941',
  expand_more_rounded: '0xf720',
  expand_more_outlined: '0xf033',
  explicit: '0xe247',
  explicit_sharp: '0xe943',
  explicit_rounded: '0xf722',
  explicit_outlined: '0xf035',
  explore: '0xe248',
  explore_sharp: '0xe945',
  explore_rounded: '0xf724',
  explore_outlined: '0xf037',
  explore_off: '0xe249',
  explore_off_sharp: '0xe944',
  explore_off_rounded: '0xf723',
  explore_off_outlined: '0xf036',
  exposure: '0xe24a',
  exposure_sharp: '0xe94a',
  exposure_rounded: '0xf729',
  exposure_outlined: '0xf03a',
  exposure_minus_1: '0xe24b',
  exposure_minus_1_sharp: '0xe946',
  exposure_minus_1_rounded: '0xf725',
  exposure_minus_1_outlined: '0xf038',
  exposure_minus_2: '0xe24c',
  exposure_minus_2_sharp: '0xe947',
  exposure_minus_2_rounded: '0xf726',
  exposure_minus_2_outlined: '0xf039',
  exposure_neg_1: '0xe24b',
  exposure_neg_1_sharp: '0xe946',
  exposure_neg_1_rounded: '0xf725',
  exposure_neg_1_outlined: '0xf038',
  exposure_neg_2: '0xe24c',
  exposure_neg_2_sharp: '0xe947',
  exposure_neg_2_rounded: '0xf726',
  exposure_neg_2_outlined: '0xf039',
  exposure_plus_1: '0xe24d',
  exposure_plus_1_sharp: '0xe948',
  exposure_plus_1_rounded: '0xf727',
  exposure_plus_1_outlined: '0xf03b',
  exposure_plus_2: '0xe24e',
  exposure_plus_2_sharp: '0xe949',
  exposure_plus_2_rounded: '0xf728',
  exposure_plus_2_outlined: '0xf03c',
  exposure_zero: '0xe24f',
  exposure_zero_sharp: '0xe94b',
  exposure_zero_rounded: '0xf72a',
  exposure_zero_outlined: '0xf03d',
  extension: '0xe250',
  extension_sharp: '0xe94d',
  extension_rounded: '0xf72c',
  extension_outlined: '0xf03f',
  extension_off: '0xe251',
  extension_off_sharp: '0xe94c',
  extension_off_rounded: '0xf72b',
  extension_off_outlined: '0xf03e',
  face: '0xe252',
  face_sharp: '0xe950',
  face_rounded: '0xf72f',
  face_outlined: '0xf040',
  face_2: '0xf085f',
  face_2_sharp: '0xf083b',
  face_2_rounded: '0xf0884',
  face_2_outlined: '0xf08a2',
  face_3: '0xf0860',
  face_3_sharp: '0xf083c',
  face_3_rounded: '0xf0885',
  face_3_outlined: '0xf08a3',
  face_4: '0xf0861',
  face_4_sharp: '0xf083d',
  face_4_rounded: '0xf0886',
  face_4_outlined: '0xf08a4',
  face_5: '0xf0862',
  face_5_sharp: '0xf083e',
  face_5_rounded: '0xf0887',
  face_5_outlined: '0xf08a5',
  face_6: '0xf0863',
  face_6_sharp: '0xf083f',
  face_6_rounded: '0xf0888',
  face_6_outlined: '0xf08a6',
  face_retouching_natural: '0xe253',
  face_retouching_natural_sharp: '0xe94e',
  face_retouching_natural_rounded: '0xf72d',
  face_retouching_natural_outlined: '0xf041',
  face_retouching_off: '0xe254',
  face_retouching_off_sharp: '0xe94f',
  face_retouching_off_rounded: '0xf72e',
  face_retouching_off_outlined: '0xf042',
  face_unlock_sharp: '0xe951',
  face_unlock_rounded: '0xf730',
  face_unlock_outlined: '0xf043',
  facebook: '0xe255',
  facebook_sharp: '0xe952',
  facebook_rounded: '0xf731',
  facebook_outlined: '0xf044',
  fact_check: '0xe256',
  fact_check_sharp: '0xe953',
  fact_check_rounded: '0xf732',
  fact_check_outlined: '0xf045',
  factory: '0xf04fd',
  factory_sharp: '0xf0409',
  factory_rounded: '0xf0316',
  factory_outlined: '0xf05f7',
  family_restroom: '0xe257',
  family_restroom_sharp: '0xe954',
  family_restroom_rounded: '0xf733',
  family_restroom_outlined: '0xf046',
  fast_forward: '0xe258',
  fast_forward_sharp: '0xe955',
  fast_forward_rounded: '0xf734',
  fast_forward_outlined: '0xf047',
  fast_rewind: '0xe259',
  fast_rewind_sharp: '0xe956',
  fast_rewind_rounded: '0xf735',
  fast_rewind_outlined: '0xf048',
  fastfood: '0xe25a',
  fastfood_sharp: '0xe957',
  fastfood_rounded: '0xf736',
  fastfood_outlined: '0xf049',
  favorite: '0xe25b',
  favorite_sharp: '0xe959',
  favorite_rounded: '0xf738',
  favorite_outlined: '0xf04b',
  favorite_border: '0xe25c',
  favorite_border_sharp: '0xe958',
  favorite_border_rounded: '0xf737',
  favorite_border_outlined: '0xf04a',
  favorite_outline: '0xe25c',
  favorite_outline_sharp: '0xe958',
  favorite_outline_rounded: '0xf737',
  favorite_outline_outlined: '0xf04a',
  fax: '0xf04fe',
  fax_sharp: '0xf040a',
  fax_rounded: '0xf0317',
  fax_outlined: '0xf05f8',
  featured_play_list: '0xe25d',
  featured_play_list_sharp: '0xe95a',
  featured_play_list_rounded: '0xf739',
  featured_play_list_outlined: '0xf04c',
  featured_video: '0xe25e',
  featured_video_sharp: '0xe95b',
  featured_video_rounded: '0xf73a',
  featured_video_outlined: '0xf04d',
  feed: '0xe25f',
  feed_sharp: '0xe95c',
  feed_rounded: '0xf73b',
  feed_outlined: '0xf04e',
  feedback: '0xe260',
  feedback_sharp: '0xe95d',
  feedback_rounded: '0xf73c',
  feedback_outlined: '0xf04f',
  female: '0xe261',
  female_sharp: '0xe95e',
  female_rounded: '0xf73d',
  female_outlined: '0xf050',
  fence: '0xe262',
  fence_sharp: '0xe95f',
  fence_rounded: '0xf73e',
  fence_outlined: '0xf051',
  festival: '0xe263',
  festival_sharp: '0xe960',
  festival_rounded: '0xf73f',
  festival_outlined: '0xf052',
  fiber_dvr: '0xe264',
  fiber_dvr_sharp: '0xe961',
  fiber_dvr_rounded: '0xf740',
  fiber_dvr_outlined: '0xf053',
  fiber_manual_record: '0xe265',
  fiber_manual_record_sharp: '0xe962',
  fiber_manual_record_rounded: '0xf741',
  fiber_manual_record_outlined: '0xf054',
  fiber_new: '0xe266',
  fiber_new_sharp: '0xe963',
  fiber_new_rounded: '0xf742',
  fiber_new_outlined: '0xf055',
  fiber_pin: '0xe267',
  fiber_pin_sharp: '0xe964',
  fiber_pin_rounded: '0xf743',
  fiber_pin_outlined: '0xf056',
  fiber_smart_record: '0xe268',
  fiber_smart_record_sharp: '0xe965',
  fiber_smart_record_rounded: '0xf744',
  fiber_smart_record_outlined: '0xf057',
  file_copy: '0xe269',
  file_copy_sharp: '0xe966',
  file_copy_rounded: '0xf745',
  file_copy_outlined: '0xf058',
  file_download: '0xe26a',
  file_download_sharp: '0xe969',
  file_download_rounded: '0xf748',
  file_download_outlined: '0xf05b',
  file_download_done: '0xe26b',
  file_download_done_sharp: '0xe967',
  file_download_done_rounded: '0xf746',
  file_download_done_outlined: '0xf059',
  file_download_off: '0xe26c',
  file_download_off_sharp: '0xe968',
  file_download_off_rounded: '0xf747',
  file_download_off_outlined: '0xf05a',
  file_open: '0xf04ff',
  file_open_sharp: '0xf040b',
  file_open_rounded: '0xf0318',
  file_open_outlined: '0xf05f9',
  file_present: '0xe26d',
  file_present_sharp: '0xe96a',
  file_present_rounded: '0xf749',
  file_present_outlined: '0xf05c',
  file_upload: '0xe26e',
  file_upload_sharp: '0xe96b',
  file_upload_rounded: '0xf74a',
  file_upload_outlined: '0xf05d',
  file_upload_off: '0xf0864',
  filter: '0xe26f',
  filter_sharp: '0xe97e',
  filter_rounded: '0xf75d',
  filter_outlined: '0xf070',
  filter_1: '0xe270',
  filter_1_sharp: '0xe96c',
  filter_1_rounded: '0xf74b',
  filter_1_outlined: '0xf05e',
  filter_2: '0xe271',
  filter_2_sharp: '0xe96d',
  filter_2_rounded: '0xf74c',
  filter_2_outlined: '0xf05f',
  filter_3: '0xe272',
  filter_3_sharp: '0xe96e',
  filter_3_rounded: '0xf74d',
  filter_3_outlined: '0xf060',
  filter_4: '0xe273',
  filter_4_sharp: '0xe96f',
  filter_4_rounded: '0xf74e',
  filter_4_outlined: '0xf061',
  filter_5: '0xe274',
  filter_5_sharp: '0xe970',
  filter_5_rounded: '0xf74f',
  filter_5_outlined: '0xf062',
  filter_6: '0xe275',
  filter_6_sharp: '0xe971',
  filter_6_rounded: '0xf750',
  filter_6_outlined: '0xf063',
  filter_7: '0xe276',
  filter_7_sharp: '0xe972',
  filter_7_rounded: '0xf751',
  filter_7_outlined: '0xf064',
  filter_8: '0xe277',
  filter_8_sharp: '0xe973',
  filter_8_rounded: '0xf752',
  filter_8_outlined: '0xf065',
  filter_9: '0xe278',
  filter_9_sharp: '0xe975',
  filter_9_rounded: '0xf754',
  filter_9_outlined: '0xf066',
  filter_9_plus: '0xe279',
  filter_9_plus_sharp: '0xe974',
  filter_9_plus_rounded: '0xf753',
  filter_9_plus_outlined: '0xf067',
  filter_alt: '0xe27a',
  filter_alt_sharp: '0xe976',
  filter_alt_rounded: '0xf755',
  filter_alt_outlined: '0xf068',
  filter_alt_off: '0xf0500',
  filter_alt_off_sharp: '0xf040c',
  filter_alt_off_rounded: '0xf0319',
  filter_alt_off_outlined: '0xf05fa',
  filter_b_and_w: '0xe27b',
  filter_b_and_w_sharp: '0xe977',
  filter_b_and_w_rounded: '0xf756',
  filter_b_and_w_outlined: '0xf069',
  filter_center_focus: '0xe27c',
  filter_center_focus_sharp: '0xe978',
  filter_center_focus_rounded: '0xf757',
  filter_center_focus_outlined: '0xf06a',
  filter_drama: '0xe27d',
  filter_drama_sharp: '0xe979',
  filter_drama_rounded: '0xf758',
  filter_drama_outlined: '0xf06b',
  filter_frames: '0xe27e',
  filter_frames_sharp: '0xe97a',
  filter_frames_rounded: '0xf759',
  filter_frames_outlined: '0xf06c',
  filter_hdr: '0xe27f',
  filter_hdr_sharp: '0xe97b',
  filter_hdr_rounded: '0xf75a',
  filter_hdr_outlined: '0xf06d',
  filter_list: '0xe280',
  filter_list_sharp: '0xe97c',
  filter_list_rounded: '0xf75b',
  filter_list_outlined: '0xf06e',
  filter_list_alt: '0xe281',
  filter_list_off: '0xf0501',
  filter_list_off_sharp: '0xf040d',
  filter_list_off_rounded: '0xf031a',
  filter_list_off_outlined: '0xf05fb',
  filter_none: '0xe282',
  filter_none_sharp: '0xe97d',
  filter_none_rounded: '0xf75c',
  filter_none_outlined: '0xf06f',
  filter_tilt_shift: '0xe283',
  filter_tilt_shift_sharp: '0xe97f',
  filter_tilt_shift_rounded: '0xf75e',
  filter_tilt_shift_outlined: '0xf071',
  filter_vintage: '0xe284',
  filter_vintage_sharp: '0xe980',
  filter_vintage_rounded: '0xf75f',
  filter_vintage_outlined: '0xf072',
  find_in_page: '0xe285',
  find_in_page_sharp: '0xe981',
  find_in_page_rounded: '0xf760',
  find_in_page_outlined: '0xf073',
  find_replace: '0xe286',
  find_replace_sharp: '0xe982',
  find_replace_rounded: '0xf761',
  find_replace_outlined: '0xf074',
  fingerprint: '0xe287',
  fingerprint_sharp: '0xe983',
  fingerprint_rounded: '0xf762',
  fingerprint_outlined: '0xf075',
  fire_extinguisher: '0xe288',
  fire_extinguisher_sharp: '0xe984',
  fire_extinguisher_rounded: '0xf763',
  fire_extinguisher_outlined: '0xf076',
  fire_hydrant: '0xe289',
  fire_hydrant_alt: '0xf07a1',
  fire_hydrant_alt_sharp: '0xf0749',
  fire_hydrant_alt_rounded: '0xf07f9',
  fire_hydrant_alt_outlined: '0xf06f1',
  fire_truck: '0xf07a2',
  fire_truck_sharp: '0xf074a',
  fire_truck_rounded: '0xf07fa',
  fire_truck_outlined: '0xf06f2',
  fireplace: '0xe28a',
  fireplace_sharp: '0xe985',
  fireplace_rounded: '0xf764',
  fireplace_outlined: '0xf077',
  first_page: '0xe28b',
  first_page_sharp: '0xe986',
  first_page_rounded: '0xf765',
  first_page_outlined: '0xf078',
  fit_screen: '0xe28c',
  fit_screen_sharp: '0xe987',
  fit_screen_rounded: '0xf766',
  fit_screen_outlined: '0xf079',
  fitbit: '0xf0502',
  fitbit_sharp: '0xf040e',
  fitbit_rounded: '0xf031b',
  fitbit_outlined: '0xf05fc',
  fitness_center: '0xe28d',
  fitness_center_sharp: '0xe988',
  fitness_center_rounded: '0xf767',
  fitness_center_outlined: '0xf07a',
  flag: '0xe28e',
  flag_sharp: '0xe989',
  flag_rounded: '0xf768',
  flag_outlined: '0xf07b',
  flag_circle: '0xf0503',
  flag_circle_sharp: '0xf040f',
  flag_circle_rounded: '0xf031c',
  flag_circle_outlined: '0xf05fd',
  flaky: '0xe28f',
  flaky_sharp: '0xe98a',
  flaky_rounded: '0xf769',
  flaky_outlined: '0xf07c',
  flare: '0xe290',
  flare_sharp: '0xe98b',
  flare_rounded: '0xf76a',
  flare_outlined: '0xf07d',
  flash_auto: '0xe291',
  flash_auto_sharp: '0xe98c',
  flash_auto_rounded: '0xf76b',
  flash_auto_outlined: '0xf07e',
  flash_off: '0xe292',
  flash_off_sharp: '0xe98d',
  flash_off_rounded: '0xf76c',
  flash_off_outlined: '0xf07f',
  flash_on: '0xe293',
  flash_on_sharp: '0xe98e',
  flash_on_rounded: '0xf76d',
  flash_on_outlined: '0xf080',
  flashlight_off: '0xe294',
  flashlight_off_sharp: '0xe98f',
  flashlight_off_rounded: '0xf76e',
  flashlight_off_outlined: '0xf081',
  flashlight_on: '0xe295',
  flashlight_on_sharp: '0xe990',
  flashlight_on_rounded: '0xf76f',
  flashlight_on_outlined: '0xf082',
  flatware: '0xe296',
  flatware_sharp: '0xe991',
  flatware_rounded: '0xf770',
  flatware_outlined: '0xf083',
  flight: '0xe297',
  flight_sharp: '0xe993',
  flight_rounded: '0xf772',
  flight_outlined: '0xf085',
  flight_class: '0xf0504',
  flight_class_sharp: '0xf0410',
  flight_class_rounded: '0xf031d',
  flight_class_outlined: '0xf05fe',
  flight_land: '0xe298',
  flight_land_sharp: '0xe992',
  flight_land_rounded: '0xf771',
  flight_land_outlined: '0xf084',
  flight_takeoff: '0xe299',
  flight_takeoff_sharp: '0xe994',
  flight_takeoff_rounded: '0xf773',
  flight_takeoff_outlined: '0xf086',
  flip: '0xe29a',
  flip_sharp: '0xe997',
  flip_rounded: '0xf776',
  flip_outlined: '0xf089',
  flip_camera_android: '0xe29b',
  flip_camera_android_sharp: '0xe995',
  flip_camera_android_rounded: '0xf774',
  flip_camera_android_outlined: '0xf087',
  flip_camera_ios: '0xe29c',
  flip_camera_ios_sharp: '0xe996',
  flip_camera_ios_rounded: '0xf775',
  flip_camera_ios_outlined: '0xf088',
  flip_to_back: '0xe29d',
  flip_to_back_sharp: '0xe998',
  flip_to_back_rounded: '0xf777',
  flip_to_back_outlined: '0xf08a',
  flip_to_front: '0xe29e',
  flip_to_front_sharp: '0xe999',
  flip_to_front_rounded: '0xf778',
  flip_to_front_outlined: '0xf08b',
  flood: '0xf07a3',
  flood_sharp: '0xf074b',
  flood_rounded: '0xf07fb',
  flood_outlined: '0xf06f3',
  flourescent: '0xf0865',
  flourescent_sharp: '0xf0840',
  flourescent_rounded: '0xf0889',
  flourescent_outlined: '0xf08a7',
  fluorescent: '0xf0865',
  fluorescent_sharp: '0xf0840',
  fluorescent_rounded: '0xf0889',
  fluorescent_outlined: '0xf08a7',
  flutter_dash: '0xe2a0',
  flutter_dash_sharp: '0xe99b',
  flutter_dash_rounded: '0xf77a',
  flutter_dash_outlined: '0xf08d',
  fmd_bad: '0xe2a1',
  fmd_bad_sharp: '0xe99c',
  fmd_bad_rounded: '0xf77b',
  fmd_bad_outlined: '0xf08e',
  fmd_good: '0xe2a2',
  fmd_good_sharp: '0xe99d',
  fmd_good_rounded: '0xf77c',
  fmd_good_outlined: '0xf08f',
  foggy: '0xf0505',
  folder: '0xe2a3',
  folder_sharp: '0xe9a0',
  folder_rounded: '0xf77e',
  folder_outlined: '0xf091',
  folder_copy: '0xf0506',
  folder_copy_sharp: '0xf0411',
  folder_copy_rounded: '0xf031e',
  folder_copy_outlined: '0xf05ff',
  folder_delete: '0xf0507',
  folder_delete_sharp: '0xf0412',
  folder_delete_rounded: '0xf031f',
  folder_delete_outlined: '0xf0600',
  folder_off: '0xf0508',
  folder_off_sharp: '0xf0413',
  folder_off_rounded: '0xf0320',
  folder_off_outlined: '0xf0601',
  folder_open: '0xe2a4',
  folder_open_sharp: '0xe99e',
  folder_open_rounded: '0xf77d',
  folder_open_outlined: '0xf090',
  folder_shared: '0xe2a5',
  folder_shared_sharp: '0xe99f',
  folder_shared_rounded: '0xf77f',
  folder_shared_outlined: '0xf092',
  folder_special: '0xe2a6',
  folder_special_sharp: '0xe9a1',
  folder_special_rounded: '0xf780',
  folder_special_outlined: '0xf093',
  folder_zip: '0xf0509',
  folder_zip_sharp: '0xf0414',
  folder_zip_rounded: '0xf0321',
  folder_zip_outlined: '0xf0602',
  follow_the_signs: '0xe2a7',
  follow_the_signs_sharp: '0xe9a2',
  follow_the_signs_rounded: '0xf781',
  follow_the_signs_outlined: '0xf094',
  font_download: '0xe2a8',
  font_download_sharp: '0xe9a4',
  font_download_rounded: '0xf783',
  font_download_outlined: '0xf096',
  font_download_off: '0xe2a9',
  font_download_off_sharp: '0xe9a3',
  font_download_off_rounded: '0xf782',
  font_download_off_outlined: '0xf095',
  food_bank: '0xe2aa',
  food_bank_sharp: '0xe9a5',
  food_bank_rounded: '0xf784',
  food_bank_outlined: '0xf097',
  forest: '0xf050a',
  forest_sharp: '0xf0415',
  forest_rounded: '0xf0322',
  forest_outlined: '0xf0603',
  fork_left: '0xf050b',
  fork_left_sharp: '0xf0416',
  fork_left_rounded: '0xf0323',
  fork_left_outlined: '0xf0604',
  fork_right: '0xf050c',
  fork_right_sharp: '0xf0417',
  fork_right_rounded: '0xf0324',
  fork_right_outlined: '0xf0605',
  forklift: '0xf0866',
  format_align_center: '0xe2ab',
  format_align_center_sharp: '0xe9a6',
  format_align_center_rounded: '0xf785',
  format_align_center_outlined: '0xf098',
  format_align_justify: '0xe2ac',
  format_align_justify_sharp: '0xe9a7',
  format_align_justify_rounded: '0xf786',
  format_align_justify_outlined: '0xf099',
  format_align_left: '0xe2ad',
  format_align_left_sharp: '0xe9a8',
  format_align_left_rounded: '0xf787',
  format_align_left_outlined: '0xf09a',
  format_align_right: '0xe2ae',
  format_align_right_sharp: '0xe9a9',
  format_align_right_rounded: '0xf788',
  format_align_right_outlined: '0xf09b',
  format_bold: '0xe2af',
  format_bold_sharp: '0xe9aa',
  format_bold_rounded: '0xf789',
  format_bold_outlined: '0xf09c',
  format_clear: '0xe2b0',
  format_clear_sharp: '0xe9ab',
  format_clear_rounded: '0xf78a',
  format_clear_outlined: '0xf09d',
  format_color_fill: '0xe2b1',
  format_color_fill_sharp: '0xe9ac',
  format_color_fill_rounded: '0xf78b',
  format_color_fill_outlined: '0xf09e',
  format_color_reset: '0xe2b2',
  format_color_reset_sharp: '0xe9ad',
  format_color_reset_rounded: '0xf78c',
  format_color_reset_outlined: '0xf09f',
  format_color_text: '0xe2b3',
  format_color_text_sharp: '0xe9ae',
  format_color_text_rounded: '0xf78d',
  format_color_text_outlined: '0xf0a0',
  format_indent_decrease: '0xe2b4',
  format_indent_decrease_sharp: '0xe9af',
  format_indent_decrease_rounded: '0xf78e',
  format_indent_decrease_outlined: '0xf0a1',
  format_indent_increase: '0xe2b5',
  format_indent_increase_sharp: '0xe9b0',
  format_indent_increase_rounded: '0xf78f',
  format_indent_increase_outlined: '0xf0a2',
  format_italic: '0xe2b6',
  format_italic_sharp: '0xe9b1',
  format_italic_rounded: '0xf790',
  format_italic_outlined: '0xf0a3',
  format_line_spacing: '0xe2b7',
  format_line_spacing_sharp: '0xe9b2',
  format_line_spacing_rounded: '0xf791',
  format_line_spacing_outlined: '0xf0a4',
  format_list_bulleted: '0xe2b8',
  format_list_bulleted_sharp: '0xe9b3',
  format_list_bulleted_rounded: '0xf792',
  format_list_bulleted_outlined: '0xf0a5',
  format_list_bulleted_add: '0xf0867',
  format_list_numbered: '0xe2b9',
  format_list_numbered_sharp: '0xe9b5',
  format_list_numbered_rounded: '0xf793',
  format_list_numbered_outlined: '0xf0a6',
  format_list_numbered_rtl: '0xe2ba',
  format_list_numbered_rtl_sharp: '0xe9b4',
  format_list_numbered_rtl_rounded: '0xf794',
  format_list_numbered_rtl_outlined: '0xf0a7',
  format_overline: '0xf050d',
  format_overline_sharp: '0xf0418',
  format_overline_rounded: '0xf0325',
  format_overline_outlined: '0xf0606',
  format_paint: '0xe2bb',
  format_paint_sharp: '0xe9b6',
  format_paint_rounded: '0xf795',
  format_paint_outlined: '0xf0a8',
  format_quote: '0xe2bc',
  format_quote_sharp: '0xe9b7',
  format_quote_rounded: '0xf796',
  format_quote_outlined: '0xf0a9',
  format_shapes: '0xe2bd',
  format_shapes_sharp: '0xe9b8',
  format_shapes_rounded: '0xf797',
  format_shapes_outlined: '0xf0aa',
  format_size: '0xe2be',
  format_size_sharp: '0xe9b9',
  format_size_rounded: '0xf798',
  format_size_outlined: '0xf0ab',
  format_strikethrough: '0xe2bf',
  format_strikethrough_sharp: '0xe9ba',
  format_strikethrough_rounded: '0xf799',
  format_strikethrough_outlined: '0xf0ac',
  format_textdirection_l_to_r: '0xe2c0',
  format_textdirection_l_to_r_sharp: '0xe9bb',
  format_textdirection_l_to_r_rounded: '0xf79a',
  format_textdirection_l_to_r_outlined: '0xf0ad',
  format_textdirection_r_to_l: '0xe2c1',
  format_textdirection_r_to_l_sharp: '0xe9bc',
  format_textdirection_r_to_l_rounded: '0xf79b',
  format_textdirection_r_to_l_outlined: '0xf0ae',
  format_underline: '0xe2c2',
  format_underline_sharp: '0xe9bd',
  format_underline_rounded: '0xf79c',
  format_underline_outlined: '0xf0af',
  format_underlined: '0xe2c2',
  format_underlined_sharp: '0xe9bd',
  format_underlined_rounded: '0xf79c',
  format_underlined_outlined: '0xf0af',
  fort: '0xf050e',
  fort_sharp: '0xf0419',
  fort_rounded: '0xf0326',
  fort_outlined: '0xf0607',
  forum: '0xe2c3',
  forum_sharp: '0xe9be',
  forum_rounded: '0xf79d',
  forum_outlined: '0xf0b0',
  forward: '0xe2c4',
  forward_sharp: '0xe9c2',
  forward_rounded: '0xf7a1',
  forward_outlined: '0xf0b4',
  forward_10: '0xe2c5',
  forward_10_sharp: '0xe9bf',
  forward_10_rounded: '0xf79e',
  forward_10_outlined: '0xf0b1',
  forward_30: '0xe2c6',
  forward_30_sharp: '0xe9c0',
  forward_30_rounded: '0xf79f',
  forward_30_outlined: '0xf0b2',
  forward_5: '0xe2c7',
  forward_5_sharp: '0xe9c1',
  forward_5_rounded: '0xf7a0',
  forward_5_outlined: '0xf0b3',
  forward_to_inbox: '0xe2c8',
  forward_to_inbox_sharp: '0xe9c3',
  forward_to_inbox_rounded: '0xf7a2',
  forward_to_inbox_outlined: '0xf0b5',
  foundation: '0xe2c9',
  foundation_sharp: '0xe9c4',
  foundation_rounded: '0xf7a3',
  foundation_outlined: '0xf0b6',
  free_breakfast: '0xe2ca',
  free_breakfast_sharp: '0xe9c5',
  free_breakfast_rounded: '0xf7a4',
  free_breakfast_outlined: '0xf0b7',
  free_cancellation: '0xf050f',
  free_cancellation_sharp: '0xf041a',
  free_cancellation_rounded: '0xf0327',
  free_cancellation_outlined: '0xf0608',
  front_hand: '0xf0510',
  front_hand_sharp: '0xf041b',
  front_hand_rounded: '0xf0328',
  front_hand_outlined: '0xf0609',
  front_loader: '0xf0868',
  fullscreen: '0xe2cb',
  fullscreen_sharp: '0xe9c7',
  fullscreen_rounded: '0xf7a6',
  fullscreen_outlined: '0xf0b9',
  fullscreen_exit: '0xe2cc',
  fullscreen_exit_sharp: '0xe9c6',
  fullscreen_exit_rounded: '0xf7a5',
  fullscreen_exit_outlined: '0xf0b8',
  functions: '0xe2cd',
  functions_sharp: '0xe9c8',
  functions_rounded: '0xf7a7',
  functions_outlined: '0xf0ba',
  g_mobiledata: '0xe2ce',
  g_mobiledata_sharp: '0xe9c9',
  g_mobiledata_rounded: '0xf7a8',
  g_mobiledata_outlined: '0xf0bb',
  g_translate: '0xe2cf',
  g_translate_sharp: '0xe9ca',
  g_translate_rounded: '0xf7a9',
  g_translate_outlined: '0xf0bc',
  gamepad: '0xe2d0',
  gamepad_sharp: '0xe9cb',
  gamepad_rounded: '0xf7aa',
  gamepad_outlined: '0xf0bd',
  games: '0xe2d1',
  games_sharp: '0xe9cc',
  games_rounded: '0xf7ab',
  games_outlined: '0xf0be',
  garage: '0xe2d2',
  garage_sharp: '0xe9cd',
  garage_rounded: '0xf7ac',
  garage_outlined: '0xf0bf',
  gas_meter: '0xf07a4',
  gas_meter_sharp: '0xf074c',
  gas_meter_rounded: '0xf07fc',
  gas_meter_outlined: '0xf06f4',
  gavel: '0xe2d3',
  gavel_sharp: '0xe9ce',
  gavel_rounded: '0xf7ad',
  gavel_outlined: '0xf0c0',
  generating_tokens: '0xf0511',
  generating_tokens_sharp: '0xf041c',
  generating_tokens_rounded: '0xf0329',
  generating_tokens_outlined: '0xf060a',
  gesture: '0xe2d4',
  gesture_sharp: '0xe9cf',
  gesture_rounded: '0xf7ae',
  gesture_outlined: '0xf0c1',
  get_app: '0xe2d5',
  get_app_sharp: '0xe9d0',
  get_app_rounded: '0xf7af',
  get_app_outlined: '0xf0c2',
  gif: '0xe2d6',
  gif_sharp: '0xe9d1',
  gif_rounded: '0xf7b0',
  gif_outlined: '0xf0c3',
  gif_box: '0xf0512',
  gif_box_sharp: '0xf041d',
  gif_box_rounded: '0xf032a',
  gif_box_outlined: '0xf060b',
  girl: '0xf0513',
  girl_sharp: '0xf041e',
  girl_rounded: '0xf032b',
  girl_outlined: '0xf060c',
  gite: '0xe2d7',
  gite_sharp: '0xe9d2',
  gite_rounded: '0xf7b1',
  gite_outlined: '0xf0c4',
  golf_course: '0xe2d8',
  golf_course_sharp: '0xe9d3',
  golf_course_rounded: '0xf7b2',
  golf_course_outlined: '0xf0c5',
  gpp_bad: '0xe2d9',
  gpp_bad_sharp: '0xe9d4',
  gpp_bad_rounded: '0xf7b3',
  gpp_bad_outlined: '0xf0c6',
  gpp_good: '0xe2da',
  gpp_good_sharp: '0xe9d5',
  gpp_good_rounded: '0xf7b4',
  gpp_good_outlined: '0xf0c7',
  gpp_maybe: '0xe2db',
  gpp_maybe_sharp: '0xe9d6',
  gpp_maybe_rounded: '0xf7b5',
  gpp_maybe_outlined: '0xf0c8',
  gps_fixed: '0xe2dc',
  gps_fixed_sharp: '0xe9d7',
  gps_fixed_rounded: '0xf7b6',
  gps_fixed_outlined: '0xf0c9',
  gps_not_fixed: '0xe2dd',
  gps_not_fixed_sharp: '0xe9d8',
  gps_not_fixed_rounded: '0xf7b7',
  gps_not_fixed_outlined: '0xf0ca',
  gps_off: '0xe2de',
  gps_off_sharp: '0xe9d9',
  gps_off_rounded: '0xf7b8',
  gps_off_outlined: '0xf0cb',
  grade: '0xe2df',
  grade_sharp: '0xe9da',
  grade_rounded: '0xf7b9',
  grade_outlined: '0xf0cc',
  gradient: '0xe2e0',
  gradient_sharp: '0xe9db',
  gradient_rounded: '0xf7ba',
  gradient_outlined: '0xf0cd',
  grading: '0xe2e1',
  grading_sharp: '0xe9dc',
  grading_rounded: '0xf7bb',
  grading_outlined: '0xf0ce',
  grain: '0xe2e2',
  grain_sharp: '0xe9dd',
  grain_rounded: '0xf7bc',
  grain_outlined: '0xf0cf',
  graphic_eq: '0xe2e3',
  graphic_eq_sharp: '0xe9de',
  graphic_eq_rounded: '0xf7bd',
  graphic_eq_outlined: '0xf0d0',
  grass: '0xe2e4',
  grass_sharp: '0xe9df',
  grass_rounded: '0xf7be',
  grass_outlined: '0xf0d1',
  grid_3x3: '0xe2e5',
  grid_3x3_sharp: '0xe9e0',
  grid_3x3_rounded: '0xf7bf',
  grid_3x3_outlined: '0xf0d2',
  grid_4x4: '0xe2e6',
  grid_4x4_sharp: '0xe9e1',
  grid_4x4_rounded: '0xf7c0',
  grid_4x4_outlined: '0xf0d3',
  grid_goldenratio: '0xe2e7',
  grid_goldenratio_sharp: '0xe9e2',
  grid_goldenratio_rounded: '0xf7c1',
  grid_goldenratio_outlined: '0xf0d4',
  grid_off: '0xe2e8',
  grid_off_sharp: '0xe9e3',
  grid_off_rounded: '0xf7c2',
  grid_off_outlined: '0xf0d5',
  grid_on: '0xe2e9',
  grid_on_sharp: '0xe9e4',
  grid_on_rounded: '0xf7c3',
  grid_on_outlined: '0xf0d6',
  grid_view: '0xe2ea',
  grid_view_sharp: '0xe9e5',
  grid_view_rounded: '0xf7c4',
  grid_view_outlined: '0xf0d7',
  group: '0xe2eb',
  group_sharp: '0xe9e7',
  group_rounded: '0xf7c6',
  group_outlined: '0xf0d9',
  group_add: '0xe2ec',
  group_add_sharp: '0xe9e6',
  group_add_rounded: '0xf7c5',
  group_add_outlined: '0xf0d8',
  group_off: '0xf0514',
  group_off_sharp: '0xf041f',
  group_off_rounded: '0xf032c',
  group_off_outlined: '0xf060d',
  group_remove: '0xf0515',
  group_remove_sharp: '0xf0420',
  group_remove_rounded: '0xf032d',
  group_remove_outlined: '0xf060e',
  group_work: '0xe2ed',
  group_work_sharp: '0xe9e8',
  group_work_rounded: '0xf7c7',
  group_work_outlined: '0xf0da',
  groups: '0xe2ee',
  groups_sharp: '0xe9e9',
  groups_rounded: '0xf7c8',
  groups_outlined: '0xf0db',
  groups_2: '0xf0869',
  groups_2_sharp: '0xf0841',
  groups_2_rounded: '0xf088a',
  groups_2_outlined: '0xf08a8',
  groups_3: '0xf086a',
  groups_3_sharp: '0xf0842',
  groups_3_rounded: '0xf088b',
  groups_3_outlined: '0xf08a9',
  h_mobiledata: '0xe2ef',
  h_mobiledata_sharp: '0xe9ea',
  h_mobiledata_rounded: '0xf7c9',
  h_mobiledata_outlined: '0xf0dc',
  h_plus_mobiledata: '0xe2f0',
  h_plus_mobiledata_sharp: '0xe9eb',
  h_plus_mobiledata_rounded: '0xf7ca',
  h_plus_mobiledata_outlined: '0xf0dd',
  hail: '0xe2f1',
  hail_sharp: '0xe9ec',
  hail_rounded: '0xf7cb',
  hail_outlined: '0xf0de',
  handshake: '0xf06be',
  handshake_sharp: '0xf06b1',
  handshake_rounded: '0xf06cb',
  handshake_outlined: '0xf06a4',
  handyman: '0xe2f2',
  handyman_sharp: '0xe9ed',
  handyman_rounded: '0xf7cc',
  handyman_outlined: '0xf0df',
  hardware: '0xe2f3',
  hardware_sharp: '0xe9ee',
  hardware_rounded: '0xf7cd',
  hardware_outlined: '0xf0e0',
  hd: '0xe2f4',
  hd_sharp: '0xe9ef',
  hd_rounded: '0xf7ce',
  hd_outlined: '0xf0e1',
  hdr_auto: '0xe2f5',
  hdr_auto_sharp: '0xe9f1',
  hdr_auto_rounded: '0xf7cf',
  hdr_auto_outlined: '0xf0e2',
  hdr_auto_select: '0xe2f6',
  hdr_auto_select_sharp: '0xe9f0',
  hdr_auto_select_rounded: '0xf7d0',
  hdr_auto_select_outlined: '0xf0e3',
  hdr_enhanced_select: '0xe2f7',
  hdr_enhanced_select_sharp: '0xe9f2',
  hdr_enhanced_select_rounded: '0xf7d1',
  hdr_enhanced_select_outlined: '0xf0e4',
  hdr_off: '0xe2f8',
  hdr_off_sharp: '0xe9f4',
  hdr_off_rounded: '0xf7d2',
  hdr_off_outlined: '0xf0e5',
  hdr_off_select: '0xe2f9',
  hdr_off_select_sharp: '0xe9f3',
  hdr_off_select_rounded: '0xf7d3',
  hdr_off_select_outlined: '0xf0e6',
  hdr_on: '0xe2fa',
  hdr_on_sharp: '0xe9f6',
  hdr_on_rounded: '0xf7d4',
  hdr_on_outlined: '0xf0e7',
  hdr_on_select: '0xe2fb',
  hdr_on_select_sharp: '0xe9f5',
  hdr_on_select_rounded: '0xf7d5',
  hdr_on_select_outlined: '0xf0e8',
  hdr_plus: '0xe2fc',
  hdr_plus_sharp: '0xe9f7',
  hdr_plus_rounded: '0xf7d6',
  hdr_plus_outlined: '0xf0e9',
  hdr_strong: '0xe2fd',
  hdr_strong_sharp: '0xe9f8',
  hdr_strong_rounded: '0xf7d7',
  hdr_strong_outlined: '0xf0ea',
  hdr_weak: '0xe2fe',
  hdr_weak_sharp: '0xe9f9',
  hdr_weak_rounded: '0xf7d8',
  hdr_weak_outlined: '0xf0eb',
  headphones: '0xe2ff',
  headphones_sharp: '0xe9fb',
  headphones_rounded: '0xf7da',
  headphones_outlined: '0xf0ed',
  headphones_battery: '0xe300',
  headphones_battery_sharp: '0xe9fa',
  headphones_battery_rounded: '0xf7d9',
  headphones_battery_outlined: '0xf0ec',
  headset: '0xe301',
  headset_sharp: '0xe9fe',
  headset_rounded: '0xf7dd',
  headset_outlined: '0xf0f0',
  headset_mic: '0xe302',
  headset_mic_sharp: '0xe9fc',
  headset_mic_rounded: '0xf7db',
  headset_mic_outlined: '0xf0ee',
  headset_off: '0xe303',
  headset_off_sharp: '0xe9fd',
  headset_off_rounded: '0xf7dc',
  headset_off_outlined: '0xf0ef',
  healing: '0xe304',
  healing_sharp: '0xe9ff',
  healing_rounded: '0xf7de',
  healing_outlined: '0xf0f1',
  health_and_safety: '0xe305',
  health_and_safety_sharp: '0xea00',
  health_and_safety_rounded: '0xf7df',
  health_and_safety_outlined: '0xf0f2',
  hearing: '0xe306',
  hearing_sharp: '0xea02',
  hearing_rounded: '0xf7e1',
  hearing_outlined: '0xf0f4',
  hearing_disabled: '0xe307',
  hearing_disabled_sharp: '0xea01',
  hearing_disabled_rounded: '0xf7e0',
  hearing_disabled_outlined: '0xf0f3',
  heart_broken: '0xf0516',
  heart_broken_sharp: '0xf0421',
  heart_broken_rounded: '0xf032e',
  heart_broken_outlined: '0xf060f',
  heat_pump: '0xf07a5',
  heat_pump_sharp: '0xf074d',
  heat_pump_rounded: '0xf07fd',
  heat_pump_outlined: '0xf06f5',
  height: '0xe308',
  height_sharp: '0xea03',
  height_rounded: '0xf7e2',
  height_outlined: '0xf0f5',
  help: '0xe309',
  help_sharp: '0xea06',
  help_rounded: '0xf7e5',
  help_outlined: '0xf0f8',
  help_center: '0xe30a',
  help_center_sharp: '0xea04',
  help_center_rounded: '0xf7e3',
  help_center_outlined: '0xf0f6',
  help_outline: '0xe30b',
  help_outline_sharp: '0xea05',
  help_outline_rounded: '0xf7e4',
  help_outline_outlined: '0xf0f7',
  hevc: '0xe30c',
  hevc_sharp: '0xea07',
  hevc_rounded: '0xf7e6',
  hevc_outlined: '0xf0f9',
  hexagon: '0xf0517',
  hexagon_sharp: '0xf0422',
  hexagon_rounded: '0xf032f',
  hexagon_outlined: '0xf0610',
  hide_image: '0xe30d',
  hide_image_sharp: '0xea08',
  hide_image_rounded: '0xf7e7',
  hide_image_outlined: '0xf0fa',
  hide_source: '0xe30e',
  hide_source_sharp: '0xea09',
  hide_source_rounded: '0xf7e8',
  hide_source_outlined: '0xf0fb',
  high_quality: '0xe30f',
  high_quality_sharp: '0xea0a',
  high_quality_rounded: '0xf7e9',
  high_quality_outlined: '0xf0fc',
  highlight: '0xe310',
  highlight_sharp: '0xea0d',
  highlight_rounded: '0xf7ec',
  highlight_outlined: '0xf0ff',
  highlight_alt: '0xe311',
  highlight_alt_sharp: '0xea0b',
  highlight_alt_rounded: '0xf7ea',
  highlight_alt_outlined: '0xf0fd',
  highlight_off: '0xe312',
  highlight_off_sharp: '0xea0c',
  highlight_off_rounded: '0xf7eb',
  highlight_off_outlined: '0xf0fe',
  highlight_remove: '0xe312',
  highlight_remove_sharp: '0xea0c',
  highlight_remove_rounded: '0xf7eb',
  highlight_remove_outlined: '0xf0fe',
  hiking: '0xe313',
  hiking_sharp: '0xea0e',
  hiking_rounded: '0xf7ed',
  hiking_outlined: '0xf100',
  history: '0xe314',
  history_sharp: '0xea10',
  history_rounded: '0xf7ef',
  history_outlined: '0xf102',
  history_edu: '0xe315',
  history_edu_sharp: '0xea0f',
  history_edu_rounded: '0xf7ee',
  history_edu_outlined: '0xf101',
  history_toggle_off: '0xe316',
  history_toggle_off_sharp: '0xea11',
  history_toggle_off_rounded: '0xf7f0',
  history_toggle_off_outlined: '0xf103',
  hive: '0xf0518',
  hive_sharp: '0xf0423',
  hive_rounded: '0xf0330',
  hive_outlined: '0xf0611',
  hls: '0xf0519',
  hls_sharp: '0xf0425',
  hls_rounded: '0xf0332',
  hls_outlined: '0xf0613',
  hls_off: '0xf051a',
  hls_off_sharp: '0xf0424',
  hls_off_rounded: '0xf0331',
  hls_off_outlined: '0xf0612',
  holiday_village: '0xe317',
  holiday_village_sharp: '0xea12',
  holiday_village_rounded: '0xf7f1',
  holiday_village_outlined: '0xf104',
  home: '0xe318',
  home_sharp: '0xea16',
  home_rounded: '0xf7f5',
  home_outlined: '0xf107',
  home_filled: '0xe319',
  home_max: '0xe31a',
  home_max_sharp: '0xea13',
  home_max_rounded: '0xf7f2',
  home_max_outlined: '0xf105',
  home_mini: '0xe31b',
  home_mini_sharp: '0xea14',
  home_mini_rounded: '0xf7f3',
  home_mini_outlined: '0xf106',
  home_repair_service: '0xe31c',
  home_repair_service_sharp: '0xea15',
  home_repair_service_rounded: '0xf7f4',
  home_repair_service_outlined: '0xf108',
  home_work: '0xe31d',
  home_work_sharp: '0xea17',
  home_work_rounded: '0xf7f6',
  home_work_outlined: '0xf109',
  horizontal_distribute: '0xe31e',
  horizontal_distribute_sharp: '0xea18',
  horizontal_distribute_rounded: '0xf7f7',
  horizontal_distribute_outlined: '0xf10a',
  horizontal_rule: '0xe31f',
  horizontal_rule_sharp: '0xea19',
  horizontal_rule_rounded: '0xf7f8',
  horizontal_rule_outlined: '0xf10b',
  horizontal_split: '0xe320',
  horizontal_split_sharp: '0xea1a',
  horizontal_split_rounded: '0xf7f9',
  horizontal_split_outlined: '0xf10c',
  hot_tub: '0xe321',
  hot_tub_sharp: '0xea1b',
  hot_tub_rounded: '0xf7fa',
  hot_tub_outlined: '0xf10d',
  hotel: '0xe322',
  hotel_sharp: '0xea1c',
  hotel_rounded: '0xf7fb',
  hotel_outlined: '0xf10e',
  hotel_class: '0xf051b',
  hotel_class_sharp: '0xf0426',
  hotel_class_rounded: '0xf0333',
  hotel_class_outlined: '0xf0614',
  hourglass_bottom: '0xe323',
  hourglass_bottom_sharp: '0xea1d',
  hourglass_bottom_rounded: '0xf7fc',
  hourglass_bottom_outlined: '0xf10f',
  hourglass_disabled: '0xe324',
  hourglass_disabled_sharp: '0xea1e',
  hourglass_disabled_rounded: '0xf7fd',
  hourglass_disabled_outlined: '0xf110',
  hourglass_empty: '0xe325',
  hourglass_empty_sharp: '0xea1f',
  hourglass_empty_rounded: '0xf7fe',
  hourglass_empty_outlined: '0xf111',
  hourglass_full: '0xe326',
  hourglass_full_sharp: '0xea20',
  hourglass_full_rounded: '0xf7ff',
  hourglass_full_outlined: '0xf112',
  hourglass_top: '0xe327',
  hourglass_top_sharp: '0xea21',
  hourglass_top_rounded: '0xf800',
  hourglass_top_outlined: '0xf113',
  house: '0xe328',
  house_sharp: '0xea22',
  house_rounded: '0xf801',
  house_outlined: '0xf114',
  house_siding: '0xe329',
  house_siding_sharp: '0xea23',
  house_siding_rounded: '0xf802',
  house_siding_outlined: '0xf115',
  houseboat: '0xe32a',
  houseboat_sharp: '0xea24',
  houseboat_rounded: '0xf803',
  houseboat_outlined: '0xf116',
  how_to_reg: '0xe32b',
  how_to_reg_sharp: '0xea25',
  how_to_reg_rounded: '0xf804',
  how_to_reg_outlined: '0xf117',
  how_to_vote: '0xe32c',
  how_to_vote_sharp: '0xea26',
  how_to_vote_rounded: '0xf805',
  how_to_vote_outlined: '0xf118',
  html: '0xf051c',
  html_sharp: '0xf0427',
  html_rounded: '0xf0334',
  html_outlined: '0xf0615',
  http: '0xe32d',
  http_sharp: '0xea27',
  http_rounded: '0xf806',
  http_outlined: '0xf119',
  https: '0xe32e',
  https_sharp: '0xea28',
  https_rounded: '0xf807',
  https_outlined: '0xf11a',
  hub: '0xf051d',
  hub_sharp: '0xf0428',
  hub_rounded: '0xf0335',
  hub_outlined: '0xf0616',
  hvac: '0xe32f',
  hvac_sharp: '0xea29',
  hvac_rounded: '0xf808',
  hvac_outlined: '0xf11b',
  ice_skating: '0xe330',
  ice_skating_sharp: '0xea2a',
  ice_skating_rounded: '0xf809',
  ice_skating_outlined: '0xf11c',
  icecream: '0xe331',
  icecream_sharp: '0xea2b',
  icecream_rounded: '0xf80a',
  icecream_outlined: '0xf11d',
  image: '0xe332',
  image_sharp: '0xea2f',
  image_rounded: '0xf80d',
  image_outlined: '0xf120',
  image_aspect_ratio: '0xe333',
  image_aspect_ratio_sharp: '0xea2c',
  image_aspect_ratio_rounded: '0xf80b',
  image_aspect_ratio_outlined: '0xf11e',
  image_not_supported: '0xe334',
  image_not_supported_sharp: '0xea2d',
  image_not_supported_rounded: '0xf80c',
  image_not_supported_outlined: '0xf11f',
  image_search: '0xe335',
  image_search_sharp: '0xea2e',
  image_search_rounded: '0xf80e',
  image_search_outlined: '0xf121',
  imagesearch_roller: '0xe336',
  imagesearch_roller_sharp: '0xea30',
  imagesearch_roller_rounded: '0xf80f',
  imagesearch_roller_outlined: '0xf122',
  import_contacts: '0xe337',
  import_contacts_sharp: '0xea31',
  import_contacts_rounded: '0xf810',
  import_contacts_outlined: '0xf123',
  import_export: '0xe338',
  import_export_sharp: '0xea32',
  import_export_rounded: '0xf811',
  import_export_outlined: '0xf124',
  important_devices: '0xe339',
  important_devices_sharp: '0xea33',
  important_devices_rounded: '0xf812',
  important_devices_outlined: '0xf125',
  inbox: '0xe33a',
  inbox_sharp: '0xea34',
  inbox_rounded: '0xf813',
  inbox_outlined: '0xf126',
  incomplete_circle: '0xf051e',
  incomplete_circle_sharp: '0xf0429',
  incomplete_circle_rounded: '0xf0336',
  incomplete_circle_outlined: '0xf0617',
  indeterminate_check_box: '0xe33b',
  indeterminate_check_box_sharp: '0xea35',
  indeterminate_check_box_rounded: '0xf814',
  indeterminate_check_box_outlined: '0xf127',
  info: '0xe33c',
  info_sharp: '0xea37',
  info_rounded: '0xf816',
  info_outlined: '0xf128',
  info_outline: '0xe33d',
  info_outline_sharp: '0xea36',
  info_outline_rounded: '0xf815',
  input: '0xe33e',
  input_sharp: '0xea38',
  input_rounded: '0xf817',
  input_outlined: '0xf129',
  insert_chart: '0xe33f',
  insert_chart_sharp: '0xea3a',
  insert_chart_rounded: '0xf819',
  insert_chart_outlined: '0xf12a',
  insert_chart_outlined_sharp: '0xea39',
  insert_chart_outlined_rounded: '0xf818',
  insert_chart_outlined_outlined: '0xf12b',
  insert_comment: '0xe341',
  insert_comment_sharp: '0xea3b',
  insert_comment_rounded: '0xf81a',
  insert_comment_outlined: '0xf12c',
  insert_drive_file: '0xe342',
  insert_drive_file_sharp: '0xea3c',
  insert_drive_file_rounded: '0xf81b',
  insert_drive_file_outlined: '0xf12d',
  insert_emoticon: '0xe343',
  insert_emoticon_sharp: '0xea3d',
  insert_emoticon_rounded: '0xf81c',
  insert_emoticon_outlined: '0xf12e',
  insert_invitation: '0xe344',
  insert_invitation_sharp: '0xea3e',
  insert_invitation_rounded: '0xf81d',
  insert_invitation_outlined: '0xf12f',
  insert_link: '0xe345',
  insert_link_sharp: '0xea3f',
  insert_link_rounded: '0xf81e',
  insert_link_outlined: '0xf130',
  insert_page_break: '0xf0520',
  insert_page_break_sharp: '0xf042a',
  insert_page_break_rounded: '0xf0337',
  insert_page_break_outlined: '0xf0618',
  insert_photo: '0xe346',
  insert_photo_sharp: '0xea40',
  insert_photo_rounded: '0xf81f',
  insert_photo_outlined: '0xf131',
  insights: '0xe347',
  insights_sharp: '0xea41',
  insights_rounded: '0xf820',
  insights_outlined: '0xf132',
  install_desktop: '0xf0521',
  install_desktop_sharp: '0xf042b',
  install_desktop_rounded: '0xf0338',
  install_desktop_outlined: '0xf0619',
  install_mobile: '0xf0522',
  install_mobile_sharp: '0xf042c',
  install_mobile_rounded: '0xf0339',
  install_mobile_outlined: '0xf061a',
  integration_instructions: '0xe348',
  integration_instructions_sharp: '0xea42',
  integration_instructions_rounded: '0xf821',
  integration_instructions_outlined: '0xf133',
  interests: '0xf0523',
  interests_sharp: '0xf042d',
  interests_rounded: '0xf033a',
  interests_outlined: '0xf061b',
  interpreter_mode: '0xf0524',
  interpreter_mode_sharp: '0xf042e',
  interpreter_mode_rounded: '0xf033b',
  interpreter_mode_outlined: '0xf061c',
  inventory: '0xe349',
  inventory_sharp: '0xea44',
  inventory_rounded: '0xf823',
  inventory_outlined: '0xf135',
  inventory_2: '0xe34a',
  inventory_2_sharp: '0xea43',
  inventory_2_rounded: '0xf822',
  inventory_2_outlined: '0xf134',
  invert_colors: '0xe34b',
  invert_colors_sharp: '0xea46',
  invert_colors_rounded: '0xf825',
  invert_colors_outlined: '0xf137',
  invert_colors_off: '0xe34c',
  invert_colors_off_sharp: '0xea45',
  invert_colors_off_rounded: '0xf824',
  invert_colors_off_outlined: '0xf136',
  invert_colors_on: '0xe34b',
  invert_colors_on_sharp: '0xea46',
  invert_colors_on_rounded: '0xf825',
  invert_colors_on_outlined: '0xf137',
  ios_share: '0xe34d',
  ios_share_sharp: '0xea47',
  ios_share_rounded: '0xf826',
  ios_share_outlined: '0xf138',
  iron: '0xe34e',
  iron_sharp: '0xea48',
  iron_rounded: '0xf827',
  iron_outlined: '0xf139',
  iso: '0xe34f',
  iso_sharp: '0xea49',
  iso_rounded: '0xf828',
  iso_outlined: '0xf13a',
  javascript: '0xf0525',
  javascript_sharp: '0xf042f',
  javascript_rounded: '0xf033c',
  javascript_outlined: '0xf061d',
  join_full: '0xf0526',
  join_full_sharp: '0xf0430',
  join_full_rounded: '0xf033d',
  join_full_outlined: '0xf061e',
  join_inner: '0xf0527',
  join_inner_sharp: '0xf0431',
  join_inner_rounded: '0xf033e',
  join_inner_outlined: '0xf061f',
  join_left: '0xf0528',
  join_left_sharp: '0xf0432',
  join_left_rounded: '0xf033f',
  join_left_outlined: '0xf0620',
  join_right: '0xf0529',
  join_right_sharp: '0xf0433',
  join_right_rounded: '0xf0340',
  join_right_outlined: '0xf0621',
  kayaking: '0xe350',
  kayaking_sharp: '0xea4a',
  kayaking_rounded: '0xf829',
  kayaking_outlined: '0xf13b',
  kebab_dining: '0xf052a',
  kebab_dining_sharp: '0xf0434',
  kebab_dining_rounded: '0xf0341',
  kebab_dining_outlined: '0xf0622',
  key: '0xf052b',
  key_sharp: '0xf0436',
  key_rounded: '0xf0343',
  key_outlined: '0xf0624',
  key_off: '0xf052c',
  key_off_sharp: '0xf0435',
  key_off_rounded: '0xf0342',
  key_off_outlined: '0xf0623',
  keyboard: '0xe351',
  keyboard_sharp: '0xea54',
  keyboard_rounded: '0xf833',
  keyboard_outlined: '0xf144',
  keyboard_alt: '0xe352',
  keyboard_alt_sharp: '0xea4b',
  keyboard_alt_rounded: '0xf82a',
  keyboard_alt_outlined: '0xf13c',
  keyboard_arrow_down: '0xe353',
  keyboard_arrow_down_sharp: '0xea4c',
  keyboard_arrow_down_rounded: '0xf82b',
  keyboard_arrow_down_outlined: '0xf13d',
  keyboard_arrow_left: '0xe354',
  keyboard_arrow_left_sharp: '0xea4d',
  keyboard_arrow_left_rounded: '0xf82c',
  keyboard_arrow_left_outlined: '0xf13e',
  keyboard_arrow_right: '0xe355',
  keyboard_arrow_right_sharp: '0xea4e',
  keyboard_arrow_right_rounded: '0xf82d',
  keyboard_arrow_right_outlined: '0xf13f',
  keyboard_arrow_up: '0xe356',
  keyboard_arrow_up_sharp: '0xea4f',
  keyboard_arrow_up_rounded: '0xf82e',
  keyboard_arrow_up_outlined: '0xf140',
  keyboard_backspace: '0xe357',
  keyboard_backspace_sharp: '0xea50',
  keyboard_backspace_rounded: '0xf82f',
  keyboard_backspace_outlined: '0xf141',
  keyboard_capslock: '0xe358',
  keyboard_capslock_sharp: '0xea51',
  keyboard_capslock_rounded: '0xf830',
  keyboard_capslock_outlined: '0xf142',
  keyboard_command_key: '0xf052d',
  keyboard_command_key_sharp: '0xf0437',
  keyboard_command_key_rounded: '0xf0344',
  keyboard_command_key_outlined: '0xf0625',
  keyboard_control: '0xe402',
  keyboard_control_sharp: '0xeafa',
  keyboard_control_rounded: '0xf8d9',
  keyboard_control_outlined: '0xf1e7',
  keyboard_control_key: '0xf052e',
  keyboard_control_key_sharp: '0xf0438',
  keyboard_control_key_rounded: '0xf0345',
  keyboard_control_key_outlined: '0xf0626',
  keyboard_double_arrow_down: '0xf052f',
  keyboard_double_arrow_down_sharp: '0xf0439',
  keyboard_double_arrow_down_rounded: '0xf0346',
  keyboard_double_arrow_down_outlined: '0xf0627',
  keyboard_double_arrow_left: '0xf0530',
  keyboard_double_arrow_left_sharp: '0xf043a',
  keyboard_double_arrow_left_rounded: '0xf0347',
  keyboard_double_arrow_left_outlined: '0xf0628',
  keyboard_double_arrow_right: '0xf0531',
  keyboard_double_arrow_right_sharp: '0xf043b',
  keyboard_double_arrow_right_rounded: '0xf0348',
  keyboard_double_arrow_right_outlined: '0xf0629',
  keyboard_double_arrow_up: '0xf0532',
  keyboard_double_arrow_up_sharp: '0xf043c',
  keyboard_double_arrow_up_rounded: '0xf0349',
  keyboard_double_arrow_up_outlined: '0xf062a',
  keyboard_hide: '0xe359',
  keyboard_hide_sharp: '0xea52',
  keyboard_hide_rounded: '0xf831',
  keyboard_hide_outlined: '0xf143',
  keyboard_option_key: '0xf0533',
  keyboard_option_key_sharp: '0xf043d',
  keyboard_option_key_rounded: '0xf034a',
  keyboard_option_key_outlined: '0xf062b',
  keyboard_return: '0xe35a',
  keyboard_return_sharp: '0xea53',
  keyboard_return_rounded: '0xf832',
  keyboard_return_outlined: '0xf145',
  keyboard_tab: '0xe35b',
  keyboard_tab_sharp: '0xea55',
  keyboard_tab_rounded: '0xf834',
  keyboard_tab_outlined: '0xf146',
  keyboard_voice: '0xe35c',
  keyboard_voice_sharp: '0xea56',
  keyboard_voice_rounded: '0xf835',
  keyboard_voice_outlined: '0xf147',
  king_bed: '0xe35d',
  king_bed_sharp: '0xea57',
  king_bed_rounded: '0xf836',
  king_bed_outlined: '0xf148',
  kitchen: '0xe35e',
  kitchen_sharp: '0xea58',
  kitchen_rounded: '0xf837',
  kitchen_outlined: '0xf149',
  kitesurfing: '0xe35f',
  kitesurfing_sharp: '0xea59',
  kitesurfing_rounded: '0xf838',
  kitesurfing_outlined: '0xf14a',
  label: '0xe360',
  label_sharp: '0xea5e',
  label_rounded: '0xf83d',
  label_outlined: '0xf14d',
  label_important: '0xe361',
  label_important_sharp: '0xea5b',
  label_important_rounded: '0xf83a',
  label_important_outlined: '0xf14b',
  label_important_outline: '0xe362',
  label_important_outline_sharp: '0xea5a',
  label_important_outline_rounded: '0xf839',
  label_off: '0xe363',
  label_off_sharp: '0xea5c',
  label_off_rounded: '0xf83b',
  label_off_outlined: '0xf14c',
  label_outline: '0xe364',
  label_outline_sharp: '0xea5d',
  label_outline_rounded: '0xf83c',
  lan: '0xf0534',
  lan_sharp: '0xf043e',
  lan_rounded: '0xf034b',
  lan_outlined: '0xf062c',
  landscape: '0xe365',
  landscape_sharp: '0xea5f',
  landscape_rounded: '0xf83e',
  landscape_outlined: '0xf14e',
  landslide: '0xf07a6',
  landslide_sharp: '0xf074e',
  landslide_rounded: '0xf07fe',
  landslide_outlined: '0xf06f6',
  language: '0xe366',
  language_sharp: '0xea60',
  language_rounded: '0xf83f',
  language_outlined: '0xf14f',
  laptop: '0xe367',
  laptop_sharp: '0xea63',
  laptop_rounded: '0xf842',
  laptop_outlined: '0xf152',
  laptop_chromebook: '0xe368',
  laptop_chromebook_sharp: '0xea61',
  laptop_chromebook_rounded: '0xf840',
  laptop_chromebook_outlined: '0xf150',
  laptop_mac: '0xe369',
  laptop_mac_sharp: '0xea62',
  laptop_mac_rounded: '0xf841',
  laptop_mac_outlined: '0xf151',
  laptop_windows: '0xe36a',
  laptop_windows_sharp: '0xea64',
  laptop_windows_rounded: '0xf843',
  laptop_windows_outlined: '0xf153',
  last_page: '0xe36b',
  last_page_sharp: '0xea65',
  last_page_rounded: '0xf844',
  last_page_outlined: '0xf154',
  launch: '0xe36c',
  launch_sharp: '0xea66',
  launch_rounded: '0xf845',
  launch_outlined: '0xf155',
  layers: '0xe36d',
  layers_sharp: '0xea68',
  layers_rounded: '0xf847',
  layers_outlined: '0xf157',
  layers_clear: '0xe36e',
  layers_clear_sharp: '0xea67',
  layers_clear_rounded: '0xf846',
  layers_clear_outlined: '0xf156',
  leaderboard: '0xe36f',
  leaderboard_sharp: '0xea69',
  leaderboard_rounded: '0xf848',
  leaderboard_outlined: '0xf158',
  leak_add: '0xe370',
  leak_add_sharp: '0xea6a',
  leak_add_rounded: '0xf849',
  leak_add_outlined: '0xf159',
  leak_remove: '0xe371',
  leak_remove_sharp: '0xea6b',
  leak_remove_rounded: '0xf84a',
  leak_remove_outlined: '0xf15a',
  leave_bags_at_home: '0xe439',
  leave_bags_at_home_sharp: '0xeb32',
  leave_bags_at_home_rounded: '0xf0011',
  leave_bags_at_home_outlined: '0xf21f',
  legend_toggle: '0xe372',
  legend_toggle_sharp: '0xea6c',
  legend_toggle_rounded: '0xf84b',
  legend_toggle_outlined: '0xf15b',
  lens: '0xe373',
  lens_sharp: '0xea6e',
  lens_rounded: '0xf84d',
  lens_outlined: '0xf15d',
  lens_blur: '0xe374',
  lens_blur_sharp: '0xea6d',
  lens_blur_rounded: '0xf84c',
  lens_blur_outlined: '0xf15c',
  library_add: '0xe375',
  library_add_sharp: '0xea70',
  library_add_rounded: '0xf84f',
  library_add_outlined: '0xf15f',
  library_add_check: '0xe376',
  library_add_check_sharp: '0xea6f',
  library_add_check_rounded: '0xf84e',
  library_add_check_outlined: '0xf15e',
  library_books: '0xe377',
  library_books_sharp: '0xea71',
  library_books_rounded: '0xf850',
  library_books_outlined: '0xf160',
  library_music: '0xe378',
  library_music_sharp: '0xea72',
  library_music_rounded: '0xf851',
  library_music_outlined: '0xf161',
  light: '0xe379',
  light_sharp: '0xea74',
  light_rounded: '0xf853',
  light_outlined: '0xf163',
  light_mode: '0xe37a',
  light_mode_sharp: '0xea73',
  light_mode_rounded: '0xf852',
  light_mode_outlined: '0xf162',
  lightbulb: '0xe37b',
  lightbulb_sharp: '0xea76',
  lightbulb_rounded: '0xf855',
  lightbulb_outlined: '0xf164',
  lightbulb_circle: '0xf07a7',
  lightbulb_circle_sharp: '0xf074f',
  lightbulb_circle_rounded: '0xf07ff',
  lightbulb_circle_outlined: '0xf06f7',
  lightbulb_outline: '0xe37c',
  lightbulb_outline_sharp: '0xea75',
  lightbulb_outline_rounded: '0xf854',
  line_axis: '0xf0535',
  line_axis_sharp: '0xf043f',
  line_axis_rounded: '0xf034c',
  line_axis_outlined: '0xf062d',
  line_style: '0xe37d',
  line_style_sharp: '0xea77',
  line_style_rounded: '0xf856',
  line_style_outlined: '0xf165',
  line_weight: '0xe37e',
  line_weight_sharp: '0xea78',
  line_weight_rounded: '0xf857',
  line_weight_outlined: '0xf166',
  linear_scale: '0xe37f',
  linear_scale_sharp: '0xea79',
  linear_scale_rounded: '0xf858',
  linear_scale_outlined: '0xf167',
  link: '0xe380',
  link_sharp: '0xea7b',
  link_rounded: '0xf85a',
  link_outlined: '0xf169',
  link_off: '0xe381',
  link_off_sharp: '0xea7a',
  link_off_rounded: '0xf859',
  link_off_outlined: '0xf168',
  linked_camera: '0xe382',
  linked_camera_sharp: '0xea7c',
  linked_camera_rounded: '0xf85b',
  linked_camera_outlined: '0xf16a',
  liquor: '0xe383',
  liquor_sharp: '0xea7d',
  liquor_rounded: '0xf85c',
  liquor_outlined: '0xf16b',
  list: '0xe384',
  list_sharp: '0xea7f',
  list_rounded: '0xf85e',
  list_outlined: '0xf16d',
  list_alt: '0xe385',
  list_alt_sharp: '0xea7e',
  list_alt_rounded: '0xf85d',
  list_alt_outlined: '0xf16c',
  live_help: '0xe386',
  live_help_sharp: '0xea80',
  live_help_rounded: '0xf85f',
  live_help_outlined: '0xf16e',
  live_tv: '0xe387',
  live_tv_sharp: '0xea81',
  live_tv_rounded: '0xf860',
  live_tv_outlined: '0xf16f',
  living: '0xe388',
  living_sharp: '0xea82',
  living_rounded: '0xf861',
  living_outlined: '0xf170',
  local_activity: '0xe389',
  local_activity_sharp: '0xea83',
  local_activity_rounded: '0xf862',
  local_activity_outlined: '0xf171',
  local_airport: '0xe38a',
  local_airport_sharp: '0xea84',
  local_airport_rounded: '0xf863',
  local_airport_outlined: '0xf172',
  local_atm: '0xe38b',
  local_atm_sharp: '0xea85',
  local_atm_rounded: '0xf864',
  local_atm_outlined: '0xf173',
  local_attraction: '0xe389',
  local_attraction_sharp: '0xea83',
  local_attraction_rounded: '0xf862',
  local_attraction_outlined: '0xf171',
  local_bar: '0xe38c',
  local_bar_sharp: '0xea86',
  local_bar_rounded: '0xf865',
  local_bar_outlined: '0xf174',
  local_cafe: '0xe38d',
  local_cafe_sharp: '0xea87',
  local_cafe_rounded: '0xf866',
  local_cafe_outlined: '0xf175',
  local_car_wash: '0xe38e',
  local_car_wash_sharp: '0xea88',
  local_car_wash_rounded: '0xf867',
  local_car_wash_outlined: '0xf176',
  local_convenience_store: '0xe38f',
  local_convenience_store_sharp: '0xea89',
  local_convenience_store_rounded: '0xf868',
  local_convenience_store_outlined: '0xf177',
  local_dining: '0xe390',
  local_dining_sharp: '0xea8a',
  local_dining_rounded: '0xf869',
  local_dining_outlined: '0xf178',
  local_drink: '0xe391',
  local_drink_sharp: '0xea8b',
  local_drink_rounded: '0xf86a',
  local_drink_outlined: '0xf179',
  local_fire_department: '0xe392',
  local_fire_department_sharp: '0xea8c',
  local_fire_department_rounded: '0xf86b',
  local_fire_department_outlined: '0xf17a',
  local_florist: '0xe393',
  local_florist_sharp: '0xea8d',
  local_florist_rounded: '0xf86c',
  local_florist_outlined: '0xf17b',
  local_gas_station: '0xe394',
  local_gas_station_sharp: '0xea8e',
  local_gas_station_rounded: '0xf86d',
  local_gas_station_outlined: '0xf17c',
  local_grocery_store: '0xe395',
  local_grocery_store_sharp: '0xea8f',
  local_grocery_store_rounded: '0xf86e',
  local_grocery_store_outlined: '0xf17d',
  local_hospital: '0xe396',
  local_hospital_sharp: '0xea90',
  local_hospital_rounded: '0xf86f',
  local_hospital_outlined: '0xf17e',
  local_hotel: '0xe397',
  local_hotel_sharp: '0xea91',
  local_hotel_rounded: '0xf870',
  local_hotel_outlined: '0xf17f',
  local_laundry_service: '0xe398',
  local_laundry_service_sharp: '0xea92',
  local_laundry_service_rounded: '0xf871',
  local_laundry_service_outlined: '0xf180',
  local_library: '0xe399',
  local_library_sharp: '0xea93',
  local_library_rounded: '0xf872',
  local_library_outlined: '0xf181',
  local_mall: '0xe39a',
  local_mall_sharp: '0xea94',
  local_mall_rounded: '0xf873',
  local_mall_outlined: '0xf182',
  local_movies: '0xe39b',
  local_movies_sharp: '0xea95',
  local_movies_rounded: '0xf874',
  local_movies_outlined: '0xf183',
  local_offer: '0xe39c',
  local_offer_sharp: '0xea96',
  local_offer_rounded: '0xf875',
  local_offer_outlined: '0xf184',
  local_parking: '0xe39d',
  local_parking_sharp: '0xea97',
  local_parking_rounded: '0xf876',
  local_parking_outlined: '0xf185',
  local_pharmacy: '0xe39e',
  local_pharmacy_sharp: '0xea98',
  local_pharmacy_rounded: '0xf877',
  local_pharmacy_outlined: '0xf186',
  local_phone: '0xe39f',
  local_phone_sharp: '0xea99',
  local_phone_rounded: '0xf878',
  local_phone_outlined: '0xf187',
  local_pizza: '0xe3a0',
  local_pizza_sharp: '0xea9a',
  local_pizza_rounded: '0xf879',
  local_pizza_outlined: '0xf188',
  local_play: '0xe3a1',
  local_play_sharp: '0xea9b',
  local_play_rounded: '0xf87a',
  local_play_outlined: '0xf189',
  local_police: '0xe3a2',
  local_police_sharp: '0xea9c',
  local_police_rounded: '0xf87b',
  local_police_outlined: '0xf18a',
  local_post_office: '0xe3a3',
  local_post_office_sharp: '0xea9d',
  local_post_office_rounded: '0xf87c',
  local_post_office_outlined: '0xf18b',
  local_print_shop: '0xe3a4',
  local_print_shop_sharp: '0xea9e',
  local_print_shop_rounded: '0xf87d',
  local_print_shop_outlined: '0xf18c',
  local_printshop: '0xe3a4',
  local_printshop_sharp: '0xea9e',
  local_printshop_rounded: '0xf87d',
  local_printshop_outlined: '0xf18c',
  local_restaurant: '0xe390',
  local_restaurant_sharp: '0xea8a',
  local_restaurant_rounded: '0xf869',
  local_restaurant_outlined: '0xf178',
  local_see: '0xe3a5',
  local_see_sharp: '0xea9f',
  local_see_rounded: '0xf87e',
  local_see_outlined: '0xf18d',
  local_shipping: '0xe3a6',
  local_shipping_sharp: '0xeaa0',
  local_shipping_rounded: '0xf87f',
  local_shipping_outlined: '0xf18e',
  local_taxi: '0xe3a7',
  local_taxi_sharp: '0xeaa1',
  local_taxi_rounded: '0xf880',
  local_taxi_outlined: '0xf18f',
  location_city: '0xe3a8',
  location_city_sharp: '0xeaa2',
  location_city_rounded: '0xf881',
  location_city_outlined: '0xf190',
  location_disabled: '0xe3a9',
  location_disabled_sharp: '0xeaa3',
  location_disabled_rounded: '0xf882',
  location_disabled_outlined: '0xf191',
  location_history: '0xe498',
  location_history_sharp: '0xeb8f',
  location_history_rounded: '0xf006e',
  location_history_outlined: '0xf27d',
  location_off: '0xe3aa',
  location_off_sharp: '0xeaa4',
  location_off_rounded: '0xf883',
  location_off_outlined: '0xf192',
  location_on: '0xe3ab',
  location_on_sharp: '0xeaa5',
  location_on_rounded: '0xf884',
  location_on_outlined: '0xf193',
  location_pin: '0xe3ac',
  location_searching: '0xe3ad',
  location_searching_sharp: '0xeaa6',
  location_searching_rounded: '0xf885',
  location_searching_outlined: '0xf194',
  lock: '0xe3ae',
  lock_sharp: '0xeaaa',
  lock_rounded: '0xf889',
  lock_outlined: '0xf197',
  lock_clock: '0xe3af',
  lock_clock_sharp: '0xeaa7',
  lock_clock_rounded: '0xf886',
  lock_clock_outlined: '0xf195',
  lock_open: '0xe3b0',
  lock_open_sharp: '0xeaa8',
  lock_open_rounded: '0xf887',
  lock_open_outlined: '0xf196',
  lock_outline: '0xe3b1',
  lock_outline_sharp: '0xeaa9',
  lock_outline_rounded: '0xf888',
  lock_person: '0xf07a8',
  lock_person_sharp: '0xf0750',
  lock_person_rounded: '0xf0800',
  lock_person_outlined: '0xf06f8',
  lock_reset: '0xf0536',
  lock_reset_sharp: '0xf0440',
  lock_reset_rounded: '0xf034d',
  lock_reset_outlined: '0xf062e',
  login: '0xe3b2',
  login_sharp: '0xeaab',
  login_rounded: '0xf88a',
  login_outlined: '0xf198',
  logo_dev: '0xf0537',
  logo_dev_sharp: '0xf0441',
  logo_dev_rounded: '0xf034e',
  logo_dev_outlined: '0xf062f',
  logout: '0xe3b3',
  logout_sharp: '0xeaac',
  logout_rounded: '0xf88b',
  logout_outlined: '0xf199',
  looks: '0xe3b4',
  looks_sharp: '0xeab2',
  looks_rounded: '0xf891',
  looks_outlined: '0xf19f',
  looks_3: '0xe3b5',
  looks_3_sharp: '0xeaad',
  looks_3_rounded: '0xf88c',
  looks_3_outlined: '0xf19a',
  looks_4: '0xe3b6',
  looks_4_sharp: '0xeaae',
  looks_4_rounded: '0xf88d',
  looks_4_outlined: '0xf19b',
  looks_5: '0xe3b7',
  looks_5_sharp: '0xeaaf',
  looks_5_rounded: '0xf88e',
  looks_5_outlined: '0xf19c',
  looks_6: '0xe3b8',
  looks_6_sharp: '0xeab0',
  looks_6_rounded: '0xf88f',
  looks_6_outlined: '0xf19d',
  looks_one: '0xe3b9',
  looks_one_sharp: '0xeab1',
  looks_one_rounded: '0xf890',
  looks_one_outlined: '0xf19e',
  looks_two: '0xe3ba',
  looks_two_sharp: '0xeab3',
  looks_two_rounded: '0xf892',
  looks_two_outlined: '0xf1a0',
  loop: '0xe3bb',
  loop_sharp: '0xeab4',
  loop_rounded: '0xf893',
  loop_outlined: '0xf1a1',
  loupe: '0xe3bc',
  loupe_sharp: '0xeab5',
  loupe_rounded: '0xf894',
  loupe_outlined: '0xf1a2',
  low_priority: '0xe3bd',
  low_priority_sharp: '0xeab6',
  low_priority_rounded: '0xf895',
  low_priority_outlined: '0xf1a3',
  loyalty: '0xe3be',
  loyalty_sharp: '0xeab7',
  loyalty_rounded: '0xf896',
  loyalty_outlined: '0xf1a4',
  lte_mobiledata: '0xe3bf',
  lte_mobiledata_sharp: '0xeab8',
  lte_mobiledata_rounded: '0xf897',
  lte_mobiledata_outlined: '0xf1a5',
  lte_plus_mobiledata: '0xe3c0',
  lte_plus_mobiledata_sharp: '0xeab9',
  lte_plus_mobiledata_rounded: '0xf898',
  lte_plus_mobiledata_outlined: '0xf1a6',
  luggage: '0xe3c1',
  luggage_sharp: '0xeaba',
  luggage_rounded: '0xf899',
  luggage_outlined: '0xf1a7',
  lunch_dining: '0xe3c2',
  lunch_dining_sharp: '0xeabb',
  lunch_dining_rounded: '0xf89a',
  lunch_dining_outlined: '0xf1a8',
  lyrics: '0xf07a9',
  lyrics_sharp: '0xf0751',
  lyrics_rounded: '0xf0801',
  lyrics_outlined: '0xf06f9',
  macro_off: '0xf086b',
  macro_off_sharp: '0xf0843',
  macro_off_rounded: '0xf088c',
  macro_off_outlined: '0xf08aa',
  mail: '0xe3c3',
  mail_sharp: '0xeabd',
  mail_rounded: '0xf89c',
  mail_outlined: '0xf1aa',
  mail_lock: '0xf07aa',
  mail_lock_sharp: '0xf0752',
  mail_lock_rounded: '0xf0802',
  mail_lock_outlined: '0xf06fa',
  mail_outline: '0xe3c4',
  mail_outline_sharp: '0xeabc',
  mail_outline_rounded: '0xf89b',
  mail_outline_outlined: '0xf1a9',
  male: '0xe3c5',
  male_sharp: '0xeabe',
  male_rounded: '0xf89d',
  male_outlined: '0xf1ab',
  man: '0xf0538',
  man_sharp: '0xf0442',
  man_rounded: '0xf034f',
  man_outlined: '0xf0630',
  man_2: '0xf086c',
  man_2_sharp: '0xf0844',
  man_2_rounded: '0xf088d',
  man_2_outlined: '0xf08ab',
  man_3: '0xf086d',
  man_3_sharp: '0xf0845',
  man_3_rounded: '0xf088e',
  man_3_outlined: '0xf08ac',
  man_4: '0xf086e',
  man_4_sharp: '0xf0846',
  man_4_rounded: '0xf088f',
  man_4_outlined: '0xf08ad',
  manage_accounts: '0xe3c6',
  manage_accounts_sharp: '0xeabf',
  manage_accounts_rounded: '0xf89e',
  manage_accounts_outlined: '0xf1ac',
  manage_history: '0xf07ab',
  manage_history_sharp: '0xf0753',
  manage_history_rounded: '0xf0803',
  manage_history_outlined: '0xf06fb',
  manage_search: '0xe3c7',
  manage_search_sharp: '0xeac0',
  manage_search_rounded: '0xf89f',
  manage_search_outlined: '0xf1ad',
  map: '0xe3c8',
  map_sharp: '0xeac1',
  map_rounded: '0xf8a0',
  map_outlined: '0xf1ae',
  maps_home_work: '0xe3c9',
  maps_home_work_sharp: '0xeac2',
  maps_home_work_rounded: '0xf8a1',
  maps_home_work_outlined: '0xf1af',
  maps_ugc: '0xe3ca',
  maps_ugc_sharp: '0xeac3',
  maps_ugc_rounded: '0xf8a2',
  maps_ugc_outlined: '0xf1b0',
  margin: '0xe3cb',
  margin_sharp: '0xeac4',
  margin_rounded: '0xf8a3',
  margin_outlined: '0xf1b1',
  mark_as_unread: '0xe3cc',
  mark_as_unread_sharp: '0xeac5',
  mark_as_unread_rounded: '0xf8a4',
  mark_as_unread_outlined: '0xf1b2',
  mark_chat_read: '0xe3cd',
  mark_chat_read_sharp: '0xeac6',
  mark_chat_read_rounded: '0xf8a5',
  mark_chat_read_outlined: '0xf1b3',
  mark_chat_unread: '0xe3ce',
  mark_chat_unread_sharp: '0xeac7',
  mark_chat_unread_rounded: '0xf8a6',
  mark_chat_unread_outlined: '0xf1b4',
  mark_email_read: '0xe3cf',
  mark_email_read_sharp: '0xeac8',
  mark_email_read_rounded: '0xf8a7',
  mark_email_read_outlined: '0xf1b5',
  mark_email_unread: '0xe3d0',
  mark_email_unread_sharp: '0xeac9',
  mark_email_unread_rounded: '0xf8a8',
  mark_email_unread_outlined: '0xf1b6',
  mark_unread_chat_alt: '0xf0539',
  mark_unread_chat_alt_sharp: '0xf0443',
  mark_unread_chat_alt_rounded: '0xf0350',
  mark_unread_chat_alt_outlined: '0xf0631',
  markunread: '0xe3d1',
  markunread_sharp: '0xeacb',
  markunread_rounded: '0xf8aa',
  markunread_outlined: '0xf1b8',
  markunread_mailbox: '0xe3d2',
  markunread_mailbox_sharp: '0xeaca',
  markunread_mailbox_rounded: '0xf8a9',
  markunread_mailbox_outlined: '0xf1b7',
  masks: '0xe3d3',
  masks_sharp: '0xeacc',
  masks_rounded: '0xf8ab',
  masks_outlined: '0xf1b9',
  maximize: '0xe3d4',
  maximize_sharp: '0xeacd',
  maximize_rounded: '0xf8ac',
  maximize_outlined: '0xf1ba',
  media_bluetooth_off: '0xe3d5',
  media_bluetooth_off_sharp: '0xeace',
  media_bluetooth_off_rounded: '0xf8ad',
  media_bluetooth_off_outlined: '0xf1bb',
  media_bluetooth_on: '0xe3d6',
  media_bluetooth_on_sharp: '0xeacf',
  media_bluetooth_on_rounded: '0xf8ae',
  media_bluetooth_on_outlined: '0xf1bc',
  mediation: '0xe3d7',
  mediation_sharp: '0xead0',
  mediation_rounded: '0xf8af',
  mediation_outlined: '0xf1bd',
  medical_information: '0xf07ac',
  medical_information_sharp: '0xf0754',
  medical_information_rounded: '0xf0804',
  medical_information_outlined: '0xf06fc',
  medical_services: '0xe3d8',
  medical_services_sharp: '0xead1',
  medical_services_rounded: '0xf8b0',
  medical_services_outlined: '0xf1be',
  medication: '0xe3d9',
  medication_sharp: '0xead2',
  medication_rounded: '0xf8b1',
  medication_outlined: '0xf1bf',
  medication_liquid: '0xf053a',
  medication_liquid_sharp: '0xf0444',
  medication_liquid_rounded: '0xf0351',
  medication_liquid_outlined: '0xf0632',
  meeting_room: '0xe3da',
  meeting_room_sharp: '0xead3',
  meeting_room_rounded: '0xf8b2',
  meeting_room_outlined: '0xf1c0',
  memory: '0xe3db',
  memory_sharp: '0xead4',
  memory_rounded: '0xf8b3',
  memory_outlined: '0xf1c1',
  menu: '0xe3dc',
  menu_sharp: '0xead7',
  menu_rounded: '0xf8b6',
  menu_outlined: '0xf1c4',
  menu_book: '0xe3dd',
  menu_book_sharp: '0xead5',
  menu_book_rounded: '0xf8b4',
  menu_book_outlined: '0xf1c2',
  menu_open: '0xe3de',
  menu_open_sharp: '0xead6',
  menu_open_rounded: '0xf8b5',
  menu_open_outlined: '0xf1c3',
  merge: '0xf053b',
  merge_sharp: '0xf0445',
  merge_rounded: '0xf0352',
  merge_outlined: '0xf0633',
  merge_type: '0xe3df',
  merge_type_sharp: '0xead8',
  merge_type_rounded: '0xf8b7',
  merge_type_outlined: '0xf1c5',
  message: '0xe3e0',
  message_sharp: '0xead9',
  message_rounded: '0xf8b8',
  message_outlined: '0xf1c6',
  messenger: '0xe154',
  messenger_sharp: '0xe851',
  messenger_rounded: '0xf630',
  messenger_outlined: '0xef43',
  messenger_outline: '0xe155',
  messenger_outline_sharp: '0xe850',
  messenger_outline_rounded: '0xf62f',
  messenger_outline_outlined: '0xef42',
  mic: '0xe3e1',
  mic_sharp: '0xeade',
  mic_rounded: '0xf8bd',
  mic_outlined: '0xf1cb',
  mic_external_off: '0xe3e2',
  mic_external_off_sharp: '0xeada',
  mic_external_off_rounded: '0xf8b9',
  mic_external_off_outlined: '0xf1c7',
  mic_external_on: '0xe3e3',
  mic_external_on_sharp: '0xeadb',
  mic_external_on_rounded: '0xf8ba',
  mic_external_on_outlined: '0xf1c8',
  mic_none: '0xe3e4',
  mic_none_sharp: '0xeadc',
  mic_none_rounded: '0xf8bb',
  mic_none_outlined: '0xf1c9',
  mic_off: '0xe3e5',
  mic_off_sharp: '0xeadd',
  mic_off_rounded: '0xf8bc',
  mic_off_outlined: '0xf1ca',
  microwave: '0xe3e6',
  microwave_sharp: '0xeadf',
  microwave_rounded: '0xf8be',
  microwave_outlined: '0xf1cc',
  military_tech: '0xe3e7',
  military_tech_sharp: '0xeae0',
  military_tech_rounded: '0xf8bf',
  military_tech_outlined: '0xf1cd',
  minimize: '0xe3e8',
  minimize_sharp: '0xeae1',
  minimize_rounded: '0xf8c0',
  minimize_outlined: '0xf1ce',
  minor_crash: '0xf07ad',
  minor_crash_sharp: '0xf0755',
  minor_crash_rounded: '0xf0805',
  minor_crash_outlined: '0xf06fd',
  miscellaneous_services: '0xe3e9',
  miscellaneous_services_sharp: '0xeae2',
  miscellaneous_services_rounded: '0xf8c1',
  miscellaneous_services_outlined: '0xf1cf',
  missed_video_call: '0xe3ea',
  missed_video_call_sharp: '0xeae3',
  missed_video_call_rounded: '0xf8c2',
  missed_video_call_outlined: '0xf1d0',
  mms: '0xe3eb',
  mms_sharp: '0xeae4',
  mms_rounded: '0xf8c3',
  mms_outlined: '0xf1d1',
  mobile_friendly: '0xe3ec',
  mobile_friendly_sharp: '0xeae5',
  mobile_friendly_rounded: '0xf8c4',
  mobile_friendly_outlined: '0xf1d2',
  mobile_off: '0xe3ed',
  mobile_off_sharp: '0xeae6',
  mobile_off_rounded: '0xf8c5',
  mobile_off_outlined: '0xf1d3',
  mobile_screen_share: '0xe3ee',
  mobile_screen_share_sharp: '0xeae7',
  mobile_screen_share_rounded: '0xf8c6',
  mobile_screen_share_outlined: '0xf1d4',
  mobiledata_off: '0xe3ef',
  mobiledata_off_sharp: '0xeae8',
  mobiledata_off_rounded: '0xf8c7',
  mobiledata_off_outlined: '0xf1d5',
  mode: '0xe3f0',
  mode_sharp: '0xeaed',
  mode_rounded: '0xf8cc',
  mode_outlined: '0xf1da',
  mode_comment: '0xe3f1',
  mode_comment_sharp: '0xeae9',
  mode_comment_rounded: '0xf8c8',
  mode_comment_outlined: '0xf1d6',
  mode_edit: '0xe3f2',
  mode_edit_sharp: '0xeaeb',
  mode_edit_rounded: '0xf8ca',
  mode_edit_outlined: '0xf1d8',
  mode_edit_outline: '0xe3f3',
  mode_edit_outline_sharp: '0xeaea',
  mode_edit_outline_rounded: '0xf8c9',
  mode_edit_outline_outlined: '0xf1d7',
  mode_fan_off: '0xf07ae',
  mode_fan_off_sharp: '0xf0756',
  mode_fan_off_rounded: '0xf0806',
  mode_fan_off_outlined: '0xf06fe',
  mode_night: '0xe3f4',
  mode_night_sharp: '0xeaec',
  mode_night_rounded: '0xf8cb',
  mode_night_outlined: '0xf1d9',
  mode_of_travel: '0xf053c',
  mode_of_travel_sharp: '0xf0446',
  mode_of_travel_rounded: '0xf0353',
  mode_of_travel_outlined: '0xf0634',
  mode_standby: '0xe3f5',
  mode_standby_sharp: '0xeaee',
  mode_standby_rounded: '0xf8cd',
  mode_standby_outlined: '0xf1db',
  model_training: '0xe3f6',
  model_training_sharp: '0xeaef',
  model_training_rounded: '0xf8ce',
  model_training_outlined: '0xf1dc',
  monetization_on: '0xe3f7',
  monetization_on_sharp: '0xeaf0',
  monetization_on_rounded: '0xf8cf',
  monetization_on_outlined: '0xf1dd',
  money: '0xe3f8',
  money_sharp: '0xeaf3',
  money_rounded: '0xf8d2',
  money_outlined: '0xf1e0',
  money_off: '0xe3f9',
  money_off_sharp: '0xeaf2',
  money_off_rounded: '0xf8d1',
  money_off_outlined: '0xf1df',
  money_off_csred: '0xe3fa',
  money_off_csred_sharp: '0xeaf1',
  money_off_csred_rounded: '0xf8d0',
  money_off_csred_outlined: '0xf1de',
  monitor: '0xe3fb',
  monitor_sharp: '0xeaf4',
  monitor_rounded: '0xf8d3',
  monitor_outlined: '0xf1e1',
  monitor_heart: '0xf053d',
  monitor_heart_sharp: '0xf0447',
  monitor_heart_rounded: '0xf0354',
  monitor_heart_outlined: '0xf0635',
  monitor_weight: '0xe3fc',
  monitor_weight_sharp: '0xeaf5',
  monitor_weight_rounded: '0xf8d4',
  monitor_weight_outlined: '0xf1e2',
  monochrome_photos: '0xe3fd',
  monochrome_photos_sharp: '0xeaf6',
  monochrome_photos_rounded: '0xf8d5',
  monochrome_photos_outlined: '0xf1e3',
  mood: '0xe3fe',
  mood_sharp: '0xeaf8',
  mood_rounded: '0xf8d7',
  mood_outlined: '0xf1e5',
  mood_bad: '0xe3ff',
  mood_bad_sharp: '0xeaf7',
  mood_bad_rounded: '0xf8d6',
  mood_bad_outlined: '0xf1e4',
  moped: '0xe400',
  moped_sharp: '0xeaf9',
  moped_rounded: '0xf8d8',
  moped_outlined: '0xf1e6',
  more: '0xe401',
  more_sharp: '0xeafb',
  more_rounded: '0xf8da',
  more_outlined: '0xf1e8',
  more_horiz: '0xe402',
  more_horiz_sharp: '0xeafa',
  more_horiz_rounded: '0xf8d9',
  more_horiz_outlined: '0xf1e7',
  more_time: '0xe403',
  more_time_sharp: '0xeafc',
  more_time_rounded: '0xf8db',
  more_time_outlined: '0xf1e9',
  more_vert: '0xe404',
  more_vert_sharp: '0xeafd',
  more_vert_rounded: '0xf8dc',
  more_vert_outlined: '0xf1ea',
  mosque: '0xf053e',
  mosque_sharp: '0xf0448',
  mosque_rounded: '0xf0355',
  mosque_outlined: '0xf0636',
  motion_photos_auto: '0xe405',
  motion_photos_auto_sharp: '0xeafe',
  motion_photos_auto_rounded: '0xf8dd',
  motion_photos_auto_outlined: '0xf1eb',
  motion_photos_off: '0xe406',
  motion_photos_off_sharp: '0xeaff',
  motion_photos_off_rounded: '0xf8de',
  motion_photos_off_outlined: '0xf1ec',
  motion_photos_on: '0xe407',
  motion_photos_on_sharp: '0xeb00',
  motion_photos_on_rounded: '0xf8df',
  motion_photos_on_outlined: '0xf1ed',
  motion_photos_pause: '0xe408',
  motion_photos_pause_sharp: '0xeb01',
  motion_photos_pause_rounded: '0xf8e0',
  motion_photos_pause_outlined: '0xf1ee',
  motion_photos_paused: '0xe409',
  motion_photos_paused_sharp: '0xeb02',
  motion_photos_paused_rounded: '0xf8e1',
  motion_photos_paused_outlined: '0xf1ef',
  motorcycle: '0xe40a',
  motorcycle_sharp: '0xeb03',
  motorcycle_rounded: '0xf8e2',
  motorcycle_outlined: '0xf1f0',
  mouse: '0xe40b',
  mouse_sharp: '0xeb04',
  mouse_rounded: '0xf8e3',
  mouse_outlined: '0xf1f1',
  move_down: '0xf053f',
  move_down_sharp: '0xf0449',
  move_down_rounded: '0xf0356',
  move_down_outlined: '0xf0637',
  move_to_inbox: '0xe40c',
  move_to_inbox_sharp: '0xeb05',
  move_to_inbox_rounded: '0xf8e4',
  move_to_inbox_outlined: '0xf1f2',
  move_up: '0xf0540',
  move_up_sharp: '0xf044a',
  move_up_rounded: '0xf0357',
  move_up_outlined: '0xf0638',
  movie: '0xe40d',
  movie_sharp: '0xeb08',
  movie_rounded: '0xf8e7',
  movie_outlined: '0xf1f5',
  movie_creation: '0xe40e',
  movie_creation_sharp: '0xeb06',
  movie_creation_rounded: '0xf8e5',
  movie_creation_outlined: '0xf1f3',
  movie_edit: '0xf08b9',
  movie_filter: '0xe40f',
  movie_filter_sharp: '0xeb07',
  movie_filter_rounded: '0xf8e6',
  movie_filter_outlined: '0xf1f4',
  moving: '0xe410',
  moving_sharp: '0xeb09',
  moving_rounded: '0xf8e8',
  moving_outlined: '0xf1f6',
  mp: '0xe411',
  mp_sharp: '0xeb0a',
  mp_rounded: '0xf8e9',
  mp_outlined: '0xf1f7',
  multiline_chart: '0xe412',
  multiline_chart_sharp: '0xeb0b',
  multiline_chart_rounded: '0xf8ea',
  multiline_chart_outlined: '0xf1f8',
  multiple_stop: '0xe413',
  multiple_stop_sharp: '0xeb0c',
  multiple_stop_rounded: '0xf8eb',
  multiple_stop_outlined: '0xf1f9',
  multitrack_audio: '0xe2e3',
  multitrack_audio_sharp: '0xe9de',
  multitrack_audio_rounded: '0xf7bd',
  multitrack_audio_outlined: '0xf0d0',
  museum: '0xe414',
  museum_sharp: '0xeb0d',
  museum_rounded: '0xf8ec',
  museum_outlined: '0xf1fa',
  music_note: '0xe415',
  music_note_sharp: '0xeb0e',
  music_note_rounded: '0xf8ed',
  music_note_outlined: '0xf1fb',
  music_off: '0xe416',
  music_off_sharp: '0xeb0f',
  music_off_rounded: '0xf8ee',
  music_off_outlined: '0xf1fc',
  music_video: '0xe417',
  music_video_sharp: '0xeb10',
  music_video_rounded: '0xf8ef',
  music_video_outlined: '0xf1fd',
  my_library_add: '0xe375',
  my_library_add_sharp: '0xea70',
  my_library_add_rounded: '0xf84f',
  my_library_add_outlined: '0xf15f',
  my_library_books: '0xe377',
  my_library_books_sharp: '0xea71',
  my_library_books_rounded: '0xf850',
  my_library_books_outlined: '0xf160',
  my_library_music: '0xe378',
  my_library_music_sharp: '0xea72',
  my_library_music_rounded: '0xf851',
  my_library_music_outlined: '0xf161',
  my_location: '0xe418',
  my_location_sharp: '0xeb11',
  my_location_rounded: '0xf8f0',
  my_location_outlined: '0xf1fe',
  nat: '0xe419',
  nat_sharp: '0xeb12',
  nat_rounded: '0xf8f1',
  nat_outlined: '0xf1ff',
  nature: '0xe41a',
  nature_sharp: '0xeb14',
  nature_rounded: '0xf8f3',
  nature_outlined: '0xf200',
  nature_people: '0xe41b',
  nature_people_sharp: '0xeb13',
  nature_people_rounded: '0xf8f2',
  nature_people_outlined: '0xf201',
  navigate_before: '0xe41c',
  navigate_before_sharp: '0xeb15',
  navigate_before_rounded: '0xf8f4',
  navigate_before_outlined: '0xf202',
  navigate_next: '0xe41d',
  navigate_next_sharp: '0xeb16',
  navigate_next_rounded: '0xf8f5',
  navigate_next_outlined: '0xf203',
  navigation: '0xe41e',
  navigation_sharp: '0xeb17',
  navigation_rounded: '0xf8f6',
  navigation_outlined: '0xf204',
  near_me: '0xe41f',
  near_me_sharp: '0xeb19',
  near_me_rounded: '0xf8f8',
  near_me_outlined: '0xf206',
  near_me_disabled: '0xe420',
  near_me_disabled_sharp: '0xeb18',
  near_me_disabled_rounded: '0xf8f7',
  near_me_disabled_outlined: '0xf205',
  nearby_error: '0xe421',
  nearby_error_sharp: '0xeb1a',
  nearby_error_rounded: '0xf8f9',
  nearby_error_outlined: '0xf207',
  nearby_off: '0xe422',
  nearby_off_sharp: '0xeb1b',
  nearby_off_rounded: '0xf8fa',
  nearby_off_outlined: '0xf208',
  nest_cam_wired_stand: '0xf07af',
  nest_cam_wired_stand_sharp: '0xf0757',
  nest_cam_wired_stand_rounded: '0xf0807',
  nest_cam_wired_stand_outlined: '0xf06ff',
  network_cell: '0xe423',
  network_cell_sharp: '0xeb1c',
  network_cell_rounded: '0xf8fb',
  network_cell_outlined: '0xf209',
  network_check: '0xe424',
  network_check_sharp: '0xeb1d',
  network_check_rounded: '0xf8fc',
  network_check_outlined: '0xf20a',
  network_locked: '0xe425',
  network_locked_sharp: '0xeb1e',
  network_locked_rounded: '0xf8fd',
  network_locked_outlined: '0xf20b',
  network_ping: '0xf06bf',
  network_ping_sharp: '0xf06b2',
  network_ping_rounded: '0xf06cc',
  network_ping_outlined: '0xf06a5',
  network_wifi: '0xe426',
  network_wifi_sharp: '0xeb1f',
  network_wifi_rounded: '0xf8fe',
  network_wifi_outlined: '0xf20c',
  network_wifi_1_bar: '0xf07b0',
  network_wifi_1_bar_sharp: '0xf0758',
  network_wifi_1_bar_rounded: '0xf0808',
  network_wifi_1_bar_outlined: '0xf0700',
  network_wifi_2_bar: '0xf07b1',
  network_wifi_2_bar_sharp: '0xf0759',
  network_wifi_2_bar_rounded: '0xf0809',
  network_wifi_2_bar_outlined: '0xf0701',
  network_wifi_3_bar: '0xf07b2',
  network_wifi_3_bar_sharp: '0xf075a',
  network_wifi_3_bar_rounded: '0xf080a',
  network_wifi_3_bar_outlined: '0xf0702',
  new_label: '0xe427',
  new_label_sharp: '0xeb20',
  new_label_rounded: '0xf8ff',
  new_label_outlined: '0xf20d',
  new_releases: '0xe428',
  new_releases_sharp: '0xeb21',
  new_releases_rounded: '0xf0000',
  new_releases_outlined: '0xf20e',
  newspaper: '0xf0541',
  newspaper_sharp: '0xf044b',
  newspaper_rounded: '0xf0358',
  newspaper_outlined: '0xf0639',
  next_plan: '0xe429',
  next_plan_sharp: '0xeb22',
  next_plan_rounded: '0xf0001',
  next_plan_outlined: '0xf20f',
  next_week: '0xe42a',
  next_week_sharp: '0xeb23',
  next_week_rounded: '0xf0002',
  next_week_outlined: '0xf210',
  nfc: '0xe42b',
  nfc_sharp: '0xeb24',
  nfc_rounded: '0xf0003',
  nfc_outlined: '0xf211',
  night_shelter: '0xe42c',
  night_shelter_sharp: '0xeb25',
  night_shelter_rounded: '0xf0004',
  night_shelter_outlined: '0xf212',
  nightlife: '0xe42d',
  nightlife_sharp: '0xeb26',
  nightlife_rounded: '0xf0005',
  nightlife_outlined: '0xf213',
  nightlight: '0xe42e',
  nightlight_sharp: '0xeb28',
  nightlight_rounded: '0xf0007',
  nightlight_outlined: '0xf214',
  nightlight_round: '0xe42f',
  nightlight_round_sharp: '0xeb27',
  nightlight_round_rounded: '0xf0006',
  nightlight_round_outlined: '0xf215',
  nights_stay: '0xe430',
  nights_stay_sharp: '0xeb29',
  nights_stay_rounded: '0xf0008',
  nights_stay_outlined: '0xf216',
  no_accounts: '0xe431',
  no_accounts_sharp: '0xeb2a',
  no_accounts_rounded: '0xf0009',
  no_accounts_outlined: '0xf217',
  no_adult_content: '0xf07b3',
  no_adult_content_sharp: '0xf075b',
  no_adult_content_rounded: '0xf080b',
  no_adult_content_outlined: '0xf0703',
  no_backpack: '0xe432',
  no_backpack_sharp: '0xeb2b',
  no_backpack_rounded: '0xf000a',
  no_backpack_outlined: '0xf218',
  no_cell: '0xe433',
  no_cell_sharp: '0xeb2c',
  no_cell_rounded: '0xf000b',
  no_cell_outlined: '0xf219',
  no_crash: '0xf07b4',
  no_crash_sharp: '0xf075c',
  no_crash_rounded: '0xf080c',
  no_crash_outlined: '0xf0704',
  no_drinks: '0xe434',
  no_drinks_sharp: '0xeb2d',
  no_drinks_rounded: '0xf000c',
  no_drinks_outlined: '0xf21a',
  no_encryption: '0xe435',
  no_encryption_sharp: '0xeb2f',
  no_encryption_rounded: '0xf000e',
  no_encryption_outlined: '0xf21c',
  no_encryption_gmailerrorred: '0xe436',
  no_encryption_gmailerrorred_sharp: '0xeb2e',
  no_encryption_gmailerrorred_rounded: '0xf000d',
  no_encryption_gmailerrorred_outlined: '0xf21b',
  no_flash: '0xe437',
  no_flash_sharp: '0xeb30',
  no_flash_rounded: '0xf000f',
  no_flash_outlined: '0xf21d',
  no_food: '0xe438',
  no_food_sharp: '0xeb31',
  no_food_rounded: '0xf0010',
  no_food_outlined: '0xf21e',
  no_luggage: '0xe439',
  no_luggage_sharp: '0xeb32',
  no_luggage_rounded: '0xf0011',
  no_luggage_outlined: '0xf21f',
  no_meals: '0xe43a',
  no_meals_sharp: '0xeb33',
  no_meals_rounded: '0xf0012',
  no_meals_outlined: '0xf220',
  no_meals_ouline: '0xe43b',
  no_meeting_room: '0xe43c',
  no_meeting_room_sharp: '0xeb34',
  no_meeting_room_rounded: '0xf0013',
  no_meeting_room_outlined: '0xf221',
  no_photography: '0xe43d',
  no_photography_sharp: '0xeb35',
  no_photography_rounded: '0xf0014',
  no_photography_outlined: '0xf222',
  no_sim: '0xe43e',
  no_sim_sharp: '0xeb36',
  no_sim_rounded: '0xf0015',
  no_sim_outlined: '0xf223',
  no_stroller: '0xe43f',
  no_stroller_sharp: '0xeb37',
  no_stroller_rounded: '0xf0016',
  no_stroller_outlined: '0xf224',
  no_transfer: '0xe440',
  no_transfer_sharp: '0xeb38',
  no_transfer_rounded: '0xf0017',
  no_transfer_outlined: '0xf225',
  noise_aware: '0xf07b5',
  noise_aware_sharp: '0xf075d',
  noise_aware_rounded: '0xf080d',
  noise_aware_outlined: '0xf0705',
  noise_control_off: '0xf07b6',
  noise_control_off_sharp: '0xf075e',
  noise_control_off_rounded: '0xf080e',
  noise_control_off_outlined: '0xf0706',
  nordic_walking: '0xe441',
  nordic_walking_sharp: '0xeb39',
  nordic_walking_rounded: '0xf0018',
  nordic_walking_outlined: '0xf226',
  north: '0xe442',
  north_sharp: '0xeb3b',
  north_rounded: '0xf001a',
  north_outlined: '0xf228',
  north_east: '0xe443',
  north_east_sharp: '0xeb3a',
  north_east_rounded: '0xf0019',
  north_east_outlined: '0xf227',
  north_west: '0xe444',
  north_west_sharp: '0xeb3c',
  north_west_rounded: '0xf001b',
  north_west_outlined: '0xf229',
  not_accessible: '0xe445',
  not_accessible_sharp: '0xeb3d',
  not_accessible_rounded: '0xf001c',
  not_accessible_outlined: '0xf22a',
  not_interested: '0xe446',
  not_interested_sharp: '0xeb3e',
  not_interested_rounded: '0xf001d',
  not_interested_outlined: '0xf22b',
  not_listed_location: '0xe447',
  not_listed_location_sharp: '0xeb3f',
  not_listed_location_rounded: '0xf001e',
  not_listed_location_outlined: '0xf22c',
  not_started: '0xe448',
  not_started_sharp: '0xeb40',
  not_started_rounded: '0xf001f',
  not_started_outlined: '0xf22d',
  note: '0xe449',
  note_sharp: '0xeb43',
  note_rounded: '0xf0022',
  note_outlined: '0xf230',
  note_add: '0xe44a',
  note_add_sharp: '0xeb41',
  note_add_rounded: '0xf0020',
  note_add_outlined: '0xf22e',
  note_alt: '0xe44b',
  note_alt_sharp: '0xeb42',
  note_alt_rounded: '0xf0021',
  note_alt_outlined: '0xf22f',
  notes: '0xe44c',
  notes_sharp: '0xeb44',
  notes_rounded: '0xf0023',
  notes_outlined: '0xf231',
  notification_add: '0xe44d',
  notification_add_sharp: '0xeb45',
  notification_add_rounded: '0xf0024',
  notification_add_outlined: '0xf232',
  notification_important: '0xe44e',
  notification_important_sharp: '0xeb46',
  notification_important_rounded: '0xf0025',
  notification_important_outlined: '0xf233',
  notifications: '0xe44f',
  notifications_sharp: '0xeb4b',
  notifications_rounded: '0xf002a',
  notifications_outlined: '0xf237',
  notifications_active: '0xe450',
  notifications_active_sharp: '0xeb47',
  notifications_active_rounded: '0xf0026',
  notifications_active_outlined: '0xf234',
  notifications_none: '0xe451',
  notifications_none_sharp: '0xeb48',
  notifications_none_rounded: '0xf0027',
  notifications_none_outlined: '0xf235',
  notifications_off: '0xe452',
  notifications_off_sharp: '0xeb49',
  notifications_off_rounded: '0xf0028',
  notifications_off_outlined: '0xf236',
  notifications_on: '0xe450',
  notifications_on_sharp: '0xeb47',
  notifications_on_rounded: '0xf0026',
  notifications_on_outlined: '0xf234',
  notifications_paused: '0xe453',
  notifications_paused_sharp: '0xeb4a',
  notifications_paused_rounded: '0xf0029',
  notifications_paused_outlined: '0xf238',
  now_wallpaper: '0xe6ca',
  now_wallpaper_sharp: '0xedc0',
  now_wallpaper_rounded: '0xf029f',
  now_wallpaper_outlined: '0xf4ad',
  now_widgets: '0xe6e6',
  now_widgets_sharp: '0xedda',
  now_widgets_rounded: '0xf02b9',
  now_widgets_outlined: '0xf4c7',
  numbers: '0xf0542',
  numbers_sharp: '0xf044c',
  numbers_rounded: '0xf0359',
  numbers_outlined: '0xf063a',
  offline_bolt: '0xe454',
  offline_bolt_sharp: '0xeb4c',
  offline_bolt_rounded: '0xf002b',
  offline_bolt_outlined: '0xf239',
  offline_pin: '0xe455',
  offline_pin_sharp: '0xeb4d',
  offline_pin_rounded: '0xf002c',
  offline_pin_outlined: '0xf23a',
  offline_share: '0xe456',
  offline_share_sharp: '0xeb4e',
  offline_share_rounded: '0xf002d',
  offline_share_outlined: '0xf23b',
  oil_barrel: '0xf07b7',
  oil_barrel_sharp: '0xf075f',
  oil_barrel_rounded: '0xf080f',
  oil_barrel_outlined: '0xf0707',
  on_device_training: '0xf07b8',
  on_device_training_sharp: '0xf0760',
  on_device_training_rounded: '0xf0810',
  on_device_training_outlined: '0xf0708',
  ondemand_video: '0xe457',
  ondemand_video_sharp: '0xeb4f',
  ondemand_video_rounded: '0xf002e',
  ondemand_video_outlined: '0xf23c',
  online_prediction: '0xe458',
  online_prediction_sharp: '0xeb50',
  online_prediction_rounded: '0xf002f',
  online_prediction_outlined: '0xf23d',
  opacity: '0xe459',
  opacity_sharp: '0xeb51',
  opacity_rounded: '0xf0030',
  opacity_outlined: '0xf23e',
  open_in_browser: '0xe45a',
  open_in_browser_sharp: '0xeb52',
  open_in_browser_rounded: '0xf0031',
  open_in_browser_outlined: '0xf23f',
  open_in_full: '0xe45b',
  open_in_full_sharp: '0xeb53',
  open_in_full_rounded: '0xf0032',
  open_in_full_outlined: '0xf240',
  open_in_new: '0xe45c',
  open_in_new_sharp: '0xeb55',
  open_in_new_rounded: '0xf0034',
  open_in_new_outlined: '0xf242',
  open_in_new_off: '0xe45d',
  open_in_new_off_sharp: '0xeb54',
  open_in_new_off_rounded: '0xf0033',
  open_in_new_off_outlined: '0xf241',
  open_with: '0xe45e',
  open_with_sharp: '0xeb56',
  open_with_rounded: '0xf0035',
  open_with_outlined: '0xf243',
  other_houses: '0xe45f',
  other_houses_sharp: '0xeb57',
  other_houses_rounded: '0xf0036',
  other_houses_outlined: '0xf244',
  outbond: '0xe460',
  outbond_sharp: '0xeb58',
  outbond_rounded: '0xf0037',
  outbond_outlined: '0xf245',
  outbound: '0xe461',
  outbound_sharp: '0xeb59',
  outbound_rounded: '0xf0038',
  outbound_outlined: '0xf246',
  outbox: '0xe462',
  outbox_sharp: '0xeb5a',
  outbox_rounded: '0xf0039',
  outbox_outlined: '0xf247',
  outdoor_grill: '0xe463',
  outdoor_grill_sharp: '0xeb5b',
  outdoor_grill_rounded: '0xf003a',
  outdoor_grill_outlined: '0xf248',
  outgoing_mail: '0xe464',
  outlet: '0xe465',
  outlet_sharp: '0xeb5c',
  outlet_rounded: '0xf003b',
  outlet_outlined: '0xf249',
  outlined_flag: '0xe466',
  outlined_flag_sharp: '0xeb5d',
  outlined_flag_rounded: '0xf003c',
  outlined_flag_outlined: '0xf24a',
  output: '0xf0543',
  output_sharp: '0xf044d',
  output_rounded: '0xf035a',
  output_outlined: '0xf063b',
  padding: '0xe467',
  padding_sharp: '0xeb5e',
  padding_rounded: '0xf003d',
  padding_outlined: '0xf24b',
  pages: '0xe468',
  pages_sharp: '0xeb5f',
  pages_rounded: '0xf003e',
  pages_outlined: '0xf24c',
  pageview: '0xe469',
  pageview_sharp: '0xeb60',
  pageview_rounded: '0xf003f',
  pageview_outlined: '0xf24d',
  paid: '0xe46a',
  paid_sharp: '0xeb61',
  paid_rounded: '0xf0040',
  paid_outlined: '0xf24e',
  palette: '0xe46b',
  palette_sharp: '0xeb62',
  palette_rounded: '0xf0041',
  palette_outlined: '0xf24f',
  pallet: '0xf086f',
  pan_tool: '0xe46c',
  pan_tool_sharp: '0xeb63',
  pan_tool_rounded: '0xf0042',
  pan_tool_outlined: '0xf250',
  pan_tool_alt: '0xf0544',
  pan_tool_alt_sharp: '0xf044e',
  pan_tool_alt_rounded: '0xf035b',
  pan_tool_alt_outlined: '0xf063c',
  panorama: '0xe46d',
  panorama_sharp: '0xeb69',
  panorama_rounded: '0xf0048',
  panorama_outlined: '0xf254',
  panorama_fish_eye: '0xe46e',
  panorama_fish_eye_sharp: '0xeb64',
  panorama_fish_eye_rounded: '0xf0043',
  panorama_fish_eye_outlined: '0xf251',
  panorama_fisheye: '0xe46e',
  panorama_fisheye_sharp: '0xeb64',
  panorama_fisheye_rounded: '0xf0043',
  panorama_fisheye_outlined: '0xf251',
  panorama_horizontal: '0xe46f',
  panorama_horizontal_sharp: '0xeb66',
  panorama_horizontal_rounded: '0xf0044',
  panorama_horizontal_outlined: '0xf252',
  panorama_horizontal_select: '0xe470',
  panorama_horizontal_select_sharp: '0xeb65',
  panorama_horizontal_select_rounded: '0xf0045',
  panorama_horizontal_select_outlined: '0xf253',
  panorama_photosphere: '0xe471',
  panorama_photosphere_sharp: '0xeb68',
  panorama_photosphere_rounded: '0xf0046',
  panorama_photosphere_outlined: '0xf255',
  panorama_photosphere_select: '0xe472',
  panorama_photosphere_select_sharp: '0xeb67',
  panorama_photosphere_select_rounded: '0xf0047',
  panorama_photosphere_select_outlined: '0xf256',
  panorama_vertical: '0xe473',
  panorama_vertical_sharp: '0xeb6b',
  panorama_vertical_rounded: '0xf0049',
  panorama_vertical_outlined: '0xf257',
  panorama_vertical_select: '0xe474',
  panorama_vertical_select_sharp: '0xeb6a',
  panorama_vertical_select_rounded: '0xf004a',
  panorama_vertical_select_outlined: '0xf258',
  panorama_wide_angle: '0xe475',
  panorama_wide_angle_sharp: '0xeb6d',
  panorama_wide_angle_rounded: '0xf004b',
  panorama_wide_angle_outlined: '0xf259',
  panorama_wide_angle_select: '0xe476',
  panorama_wide_angle_select_sharp: '0xeb6c',
  panorama_wide_angle_select_rounded: '0xf004c',
  panorama_wide_angle_select_outlined: '0xf25a',
  paragliding: '0xe477',
  paragliding_sharp: '0xeb6e',
  paragliding_rounded: '0xf004d',
  paragliding_outlined: '0xf25b',
  park: '0xe478',
  park_sharp: '0xeb6f',
  park_rounded: '0xf004e',
  park_outlined: '0xf25c',
  party_mode: '0xe479',
  party_mode_sharp: '0xeb70',
  party_mode_rounded: '0xf004f',
  party_mode_outlined: '0xf25d',
  password: '0xe47a',
  password_sharp: '0xeb71',
  password_rounded: '0xf0050',
  password_outlined: '0xf25e',
  paste: '0xe192',
  paste_sharp: '0xe890',
  paste_rounded: '0xf66f',
  paste_outlined: '0xef82',
  pattern: '0xe47b',
  pattern_sharp: '0xeb72',
  pattern_rounded: '0xf0051',
  pattern_outlined: '0xf25f',
  pause: '0xe47c',
  pause_sharp: '0xeb77',
  pause_rounded: '0xf0056',
  pause_outlined: '0xf263',
  pause_circle: '0xe47d',
  pause_circle_sharp: '0xeb75',
  pause_circle_rounded: '0xf0054',
  pause_circle_outlined: '0xf262',
  pause_circle_filled: '0xe47e',
  pause_circle_filled_sharp: '0xeb73',
  pause_circle_filled_rounded: '0xf0052',
  pause_circle_filled_outlined: '0xf260',
  pause_circle_outline: '0xe47f',
  pause_circle_outline_sharp: '0xeb74',
  pause_circle_outline_rounded: '0xf0053',
  pause_circle_outline_outlined: '0xf261',
  pause_presentation: '0xe480',
  pause_presentation_sharp: '0xeb76',
  pause_presentation_rounded: '0xf0055',
  pause_presentation_outlined: '0xf264',
  payment: '0xe481',
  payment_sharp: '0xeb78',
  payment_rounded: '0xf0057',
  payment_outlined: '0xf265',
  payments: '0xe482',
  payments_sharp: '0xeb79',
  payments_rounded: '0xf0058',
  payments_outlined: '0xf266',
  paypal: '0xf0545',
  paypal_sharp: '0xf044f',
  paypal_rounded: '0xf035c',
  paypal_outlined: '0xf063d',
  pedal_bike: '0xe483',
  pedal_bike_sharp: '0xeb7a',
  pedal_bike_rounded: '0xf0059',
  pedal_bike_outlined: '0xf267',
  pending: '0xe484',
  pending_sharp: '0xeb7c',
  pending_rounded: '0xf005b',
  pending_outlined: '0xf269',
  pending_actions: '0xe485',
  pending_actions_sharp: '0xeb7b',
  pending_actions_rounded: '0xf005a',
  pending_actions_outlined: '0xf268',
  pentagon: '0xf0546',
  pentagon_sharp: '0xf0450',
  pentagon_rounded: '0xf035d',
  pentagon_outlined: '0xf063e',
  people: '0xe486',
  people_sharp: '0xeb7f',
  people_rounded: '0xf005e',
  people_outlined: '0xf26c',
  people_alt: '0xe487',
  people_alt_sharp: '0xeb7d',
  people_alt_rounded: '0xf005c',
  people_alt_outlined: '0xf26a',
  people_outline: '0xe488',
  people_outline_sharp: '0xeb7e',
  people_outline_rounded: '0xf005d',
  people_outline_outlined: '0xf26b',
  percent: '0xf0547',
  percent_sharp: '0xf0451',
  percent_rounded: '0xf035e',
  percent_outlined: '0xf063f',
  perm_camera_mic: '0xe489',
  perm_camera_mic_sharp: '0xeb80',
  perm_camera_mic_rounded: '0xf005f',
  perm_camera_mic_outlined: '0xf26d',
  perm_contact_cal: '0xe48a',
  perm_contact_cal_sharp: '0xeb81',
  perm_contact_cal_rounded: '0xf0060',
  perm_contact_cal_outlined: '0xf26e',
  perm_contact_calendar: '0xe48a',
  perm_contact_calendar_sharp: '0xeb81',
  perm_contact_calendar_rounded: '0xf0060',
  perm_contact_calendar_outlined: '0xf26e',
  perm_data_setting: '0xe48b',
  perm_data_setting_sharp: '0xeb82',
  perm_data_setting_rounded: '0xf0061',
  perm_data_setting_outlined: '0xf26f',
  perm_device_info: '0xe48c',
  perm_device_info_sharp: '0xeb83',
  perm_device_info_rounded: '0xf0062',
  perm_device_info_outlined: '0xf270',
  perm_device_information: '0xe48c',
  perm_device_information_sharp: '0xeb83',
  perm_device_information_rounded: '0xf0062',
  perm_device_information_outlined: '0xf270',
  perm_identity: '0xe48d',
  perm_identity_sharp: '0xeb84',
  perm_identity_rounded: '0xf0063',
  perm_identity_outlined: '0xf271',
  perm_media: '0xe48e',
  perm_media_sharp: '0xeb85',
  perm_media_rounded: '0xf0064',
  perm_media_outlined: '0xf272',
  perm_phone_msg: '0xe48f',
  perm_phone_msg_sharp: '0xeb86',
  perm_phone_msg_rounded: '0xf0065',
  perm_phone_msg_outlined: '0xf273',
  perm_scan_wifi: '0xe490',
  perm_scan_wifi_sharp: '0xeb87',
  perm_scan_wifi_rounded: '0xf0066',
  perm_scan_wifi_outlined: '0xf274',
  person: '0xe491',
  person_sharp: '0xeb93',
  person_rounded: '0xf0071',
  person_outlined: '0xf27b',
  person_2: '0xf0870',
  person_2_sharp: '0xf0847',
  person_2_rounded: '0xf0890',
  person_2_outlined: '0xf08ae',
  person_3: '0xf0871',
  person_3_sharp: '0xf0848',
  person_3_rounded: '0xf0891',
  person_3_outlined: '0xf08af',
  person_4: '0xf0872',
  person_4_sharp: '0xf0849',
  person_4_rounded: '0xf0892',
  person_4_outlined: '0xf08b0',
  person_add: '0xe492',
  person_add_sharp: '0xeb8b',
  person_add_rounded: '0xf006a',
  person_add_outlined: '0xf278',
  person_add_alt: '0xe493',
  person_add_alt_sharp: '0xeb89',
  person_add_alt_rounded: '0xf0068',
  person_add_alt_outlined: '0xf276',
  person_add_alt_1: '0xe494',
  person_add_alt_1_sharp: '0xeb88',
  person_add_alt_1_rounded: '0xf0067',
  person_add_alt_1_outlined: '0xf275',
  person_add_disabled: '0xe495',
  person_add_disabled_sharp: '0xeb8a',
  person_add_disabled_rounded: '0xf0069',
  person_add_disabled_outlined: '0xf277',
  person_off: '0xe496',
  person_off_sharp: '0xeb8c',
  person_off_rounded: '0xf006b',
  person_off_outlined: '0xf279',
  person_outline: '0xe497',
  person_outline_sharp: '0xeb8d',
  person_outline_rounded: '0xf006c',
  person_outline_outlined: '0xf27a',
  person_pin: '0xe498',
  person_pin_sharp: '0xeb8f',
  person_pin_rounded: '0xf006e',
  person_pin_outlined: '0xf27d',
  person_pin_circle: '0xe499',
  person_pin_circle_sharp: '0xeb8e',
  person_pin_circle_rounded: '0xf006d',
  person_pin_circle_outlined: '0xf27c',
  person_remove: '0xe49a',
  person_remove_sharp: '0xeb91',
  person_remove_rounded: '0xf0070',
  person_remove_outlined: '0xf27f',
  person_remove_alt_1: '0xe49b',
  person_remove_alt_1_sharp: '0xeb90',
  person_remove_alt_1_rounded: '0xf006f',
  person_remove_alt_1_outlined: '0xf27e',
  person_search: '0xe49c',
  person_search_sharp: '0xeb92',
  person_search_rounded: '0xf0072',
  person_search_outlined: '0xf280',
  personal_injury: '0xe49d',
  personal_injury_sharp: '0xeb94',
  personal_injury_rounded: '0xf0073',
  personal_injury_outlined: '0xf281',
  personal_video: '0xe49e',
  personal_video_sharp: '0xeb95',
  personal_video_rounded: '0xf0074',
  personal_video_outlined: '0xf282',
  pest_control: '0xe49f',
  pest_control_sharp: '0xeb97',
  pest_control_rounded: '0xf0076',
  pest_control_outlined: '0xf283',
  pest_control_rodent: '0xe4a0',
  pest_control_rodent_sharp: '0xeb96',
  pest_control_rodent_rounded: '0xf0075',
  pest_control_rodent_outlined: '0xf284',
  pets: '0xe4a1',
  pets_sharp: '0xeb98',
  pets_rounded: '0xf0077',
  pets_outlined: '0xf285',
  phishing: '0xf0548',
  phishing_sharp: '0xf0452',
  phishing_rounded: '0xf035f',
  phishing_outlined: '0xf0640',
  phone: '0xe4a2',
  phone_sharp: '0xeba4',
  phone_rounded: '0xf0083',
  phone_outlined: '0xf290',
  phone_android: '0xe4a3',
  phone_android_sharp: '0xeb99',
  phone_android_rounded: '0xf0078',
  phone_android_outlined: '0xf286',
  phone_bluetooth_speaker: '0xe4a4',
  phone_bluetooth_speaker_sharp: '0xeb9a',
  phone_bluetooth_speaker_rounded: '0xf0079',
  phone_bluetooth_speaker_outlined: '0xf287',
  phone_callback: '0xe4a5',
  phone_callback_sharp: '0xeb9b',
  phone_callback_rounded: '0xf007a',
  phone_callback_outlined: '0xf288',
  phone_disabled: '0xe4a6',
  phone_disabled_sharp: '0xeb9c',
  phone_disabled_rounded: '0xf007b',
  phone_disabled_outlined: '0xf289',
  phone_enabled: '0xe4a7',
  phone_enabled_sharp: '0xeb9d',
  phone_enabled_rounded: '0xf007c',
  phone_enabled_outlined: '0xf28a',
  phone_forwarded: '0xe4a8',
  phone_forwarded_sharp: '0xeb9e',
  phone_forwarded_rounded: '0xf007d',
  phone_forwarded_outlined: '0xf28b',
  phone_in_talk: '0xe4a9',
  phone_in_talk_sharp: '0xeb9f',
  phone_in_talk_rounded: '0xf007e',
  phone_in_talk_outlined: '0xf28c',
  phone_iphone: '0xe4aa',
  phone_iphone_sharp: '0xeba0',
  phone_iphone_rounded: '0xf007f',
  phone_iphone_outlined: '0xf28d',
  phone_locked: '0xe4ab',
  phone_locked_sharp: '0xeba1',
  phone_locked_rounded: '0xf0080',
  phone_locked_outlined: '0xf28e',
  phone_missed: '0xe4ac',
  phone_missed_sharp: '0xeba2',
  phone_missed_rounded: '0xf0081',
  phone_missed_outlined: '0xf28f',
  phone_paused: '0xe4ad',
  phone_paused_sharp: '0xeba3',
  phone_paused_rounded: '0xf0082',
  phone_paused_outlined: '0xf291',
  phonelink: '0xe4ae',
  phonelink_sharp: '0xebaa',
  phonelink_rounded: '0xf0088',
  phonelink_outlined: '0xf295',
  phonelink_erase: '0xe4af',
  phonelink_erase_sharp: '0xeba5',
  phonelink_erase_rounded: '0xf0084',
  phonelink_erase_outlined: '0xf292',
  phonelink_lock: '0xe4b0',
  phonelink_lock_sharp: '0xeba6',
  phonelink_lock_rounded: '0xf0085',
  phonelink_lock_outlined: '0xf293',
  phonelink_off: '0xe4b1',
  phonelink_off_sharp: '0xeba7',
  phonelink_off_rounded: '0xf0086',
  phonelink_off_outlined: '0xf294',
  phonelink_ring: '0xe4b2',
  phonelink_ring_sharp: '0xeba8',
  phonelink_ring_rounded: '0xf0087',
  phonelink_ring_outlined: '0xf296',
  phonelink_setup: '0xe4b3',
  phonelink_setup_sharp: '0xeba9',
  phonelink_setup_rounded: '0xf0089',
  phonelink_setup_outlined: '0xf297',
  photo: '0xe4b4',
  photo_sharp: '0xebb1',
  photo_rounded: '0xf0090',
  photo_outlined: '0xf29e',
  photo_album: '0xe4b5',
  photo_album_sharp: '0xebab',
  photo_album_rounded: '0xf008a',
  photo_album_outlined: '0xf298',
  photo_camera: '0xe4b6',
  photo_camera_sharp: '0xebae',
  photo_camera_rounded: '0xf008d',
  photo_camera_outlined: '0xf29b',
  photo_camera_back: '0xe4b7',
  photo_camera_back_sharp: '0xebac',
  photo_camera_back_rounded: '0xf008b',
  photo_camera_back_outlined: '0xf299',
  photo_camera_front: '0xe4b8',
  photo_camera_front_sharp: '0xebad',
  photo_camera_front_rounded: '0xf008c',
  photo_camera_front_outlined: '0xf29a',
  photo_filter: '0xe4b9',
  photo_filter_sharp: '0xebaf',
  photo_filter_rounded: '0xf008e',
  photo_filter_outlined: '0xf29c',
  photo_library: '0xe4ba',
  photo_library_sharp: '0xebb0',
  photo_library_rounded: '0xf008f',
  photo_library_outlined: '0xf29d',
  photo_size_select_actual: '0xe4bb',
  photo_size_select_actual_sharp: '0xebb2',
  photo_size_select_actual_rounded: '0xf0091',
  photo_size_select_actual_outlined: '0xf29f',
  photo_size_select_large: '0xe4bc',
  photo_size_select_large_sharp: '0xebb3',
  photo_size_select_large_rounded: '0xf0092',
  photo_size_select_large_outlined: '0xf2a0',
  photo_size_select_small: '0xe4bd',
  photo_size_select_small_sharp: '0xebb4',
  photo_size_select_small_rounded: '0xf0093',
  photo_size_select_small_outlined: '0xf2a1',
  php: '0xf0549',
  php_sharp: '0xf0453',
  php_rounded: '0xf0360',
  php_outlined: '0xf0641',
  piano: '0xe4be',
  piano_sharp: '0xebb6',
  piano_rounded: '0xf0095',
  piano_outlined: '0xf2a3',
  piano_off: '0xe4bf',
  piano_off_sharp: '0xebb5',
  piano_off_rounded: '0xf0094',
  piano_off_outlined: '0xf2a2',
  picture_as_pdf: '0xe4c0',
  picture_as_pdf_sharp: '0xebb7',
  picture_as_pdf_rounded: '0xf0096',
  picture_as_pdf_outlined: '0xf2a4',
  picture_in_picture: '0xe4c1',
  picture_in_picture_sharp: '0xebb9',
  picture_in_picture_rounded: '0xf0098',
  picture_in_picture_outlined: '0xf2a6',
  picture_in_picture_alt: '0xe4c2',
  picture_in_picture_alt_sharp: '0xebb8',
  picture_in_picture_alt_rounded: '0xf0097',
  picture_in_picture_alt_outlined: '0xf2a5',
  pie_chart: '0xe4c3',
  pie_chart_sharp: '0xebbb',
  pie_chart_rounded: '0xf009a',
  pie_chart_outline: '0xe4c5',
  pie_chart_outline_sharp: '0xebba',
  pie_chart_outline_rounded: '0xf0099',
  pie_chart_outline_outlined: '0xf2a7',
  pin: '0xe4c6',
  pin_sharp: '0xebbd',
  pin_rounded: '0xf009c',
  pin_outlined: '0xf2aa',
  pin_drop: '0xe4c7',
  pin_drop_sharp: '0xebbc',
  pin_drop_rounded: '0xf009b',
  pin_drop_outlined: '0xf2a9',
  pin_end: '0xf054b',
  pin_end_sharp: '0xf0454',
  pin_end_rounded: '0xf0361',
  pin_end_outlined: '0xf0642',
  pin_invoke: '0xf054c',
  pin_invoke_sharp: '0xf0455',
  pin_invoke_rounded: '0xf0362',
  pin_invoke_outlined: '0xf0643',
  pinch: '0xf054d',
  pinch_sharp: '0xf0456',
  pinch_rounded: '0xf0363',
  pinch_outlined: '0xf0644',
  pivot_table_chart: '0xe4c8',
  pivot_table_chart_sharp: '0xebbe',
  pivot_table_chart_rounded: '0xf009d',
  pivot_table_chart_outlined: '0xf2ab',
  pix: '0xf054e',
  pix_sharp: '0xf0457',
  pix_rounded: '0xf0364',
  pix_outlined: '0xf0645',
  place: '0xe4c9',
  place_sharp: '0xebbf',
  place_rounded: '0xf009e',
  place_outlined: '0xf2ac',
  plagiarism: '0xe4ca',
  plagiarism_sharp: '0xebc0',
  plagiarism_rounded: '0xf009f',
  plagiarism_outlined: '0xf2ad',
  play_arrow: '0xe4cb',
  play_arrow_sharp: '0xebc1',
  play_arrow_rounded: '0xf00a0',
  play_arrow_outlined: '0xf2ae',
  play_circle: '0xe4cc',
  play_circle_sharp: '0xebc4',
  play_circle_rounded: '0xf00a3',
  play_circle_outlined: '0xf2b1',
  play_circle_fill: '0xe4cd',
  play_circle_fill_sharp: '0xebc2',
  play_circle_fill_rounded: '0xf00a1',
  play_circle_fill_outlined: '0xf2af',
  play_circle_filled: '0xe4cd',
  play_circle_filled_sharp: '0xebc2',
  play_circle_filled_rounded: '0xf00a1',
  play_circle_filled_outlined: '0xf2af',
  play_circle_outline: '0xe4ce',
  play_circle_outline_sharp: '0xebc3',
  play_circle_outline_rounded: '0xf00a2',
  play_circle_outline_outlined: '0xf2b0',
  play_disabled: '0xe4cf',
  play_disabled_sharp: '0xebc5',
  play_disabled_rounded: '0xf00a4',
  play_disabled_outlined: '0xf2b2',
  play_for_work: '0xe4d0',
  play_for_work_sharp: '0xebc6',
  play_for_work_rounded: '0xf00a5',
  play_for_work_outlined: '0xf2b3',
  play_lesson: '0xe4d1',
  play_lesson_sharp: '0xebc7',
  play_lesson_rounded: '0xf00a6',
  play_lesson_outlined: '0xf2b4',
  playlist_add: '0xe4d2',
  playlist_add_sharp: '0xebc9',
  playlist_add_rounded: '0xf00a8',
  playlist_add_outlined: '0xf2b6',
  playlist_add_check: '0xe4d3',
  playlist_add_check_sharp: '0xebc8',
  playlist_add_check_rounded: '0xf00a7',
  playlist_add_check_outlined: '0xf2b5',
  playlist_add_check_circle: '0xf054f',
  playlist_add_check_circle_sharp: '0xf0458',
  playlist_add_check_circle_rounded: '0xf0365',
  playlist_add_check_circle_outlined: '0xf0646',
  playlist_add_circle: '0xf0550',
  playlist_add_circle_sharp: '0xf0459',
  playlist_add_circle_rounded: '0xf0366',
  playlist_add_circle_outlined: '0xf0647',
  playlist_play: '0xe4d4',
  playlist_play_sharp: '0xebca',
  playlist_play_rounded: '0xf00a9',
  playlist_play_outlined: '0xf2b7',
  playlist_remove: '0xf0551',
  playlist_remove_sharp: '0xf045a',
  playlist_remove_rounded: '0xf0367',
  playlist_remove_outlined: '0xf0648',
  plumbing: '0xe4d5',
  plumbing_sharp: '0xebcb',
  plumbing_rounded: '0xf00aa',
  plumbing_outlined: '0xf2b8',
  plus_one: '0xe4d6',
  plus_one_sharp: '0xebcc',
  plus_one_rounded: '0xf00ab',
  plus_one_outlined: '0xf2b9',
  podcasts: '0xe4d7',
  podcasts_sharp: '0xebcd',
  podcasts_rounded: '0xf00ac',
  podcasts_outlined: '0xf2ba',
  point_of_sale: '0xe4d8',
  point_of_sale_sharp: '0xebce',
  point_of_sale_rounded: '0xf00ad',
  point_of_sale_outlined: '0xf2bb',
  policy: '0xe4d9',
  policy_sharp: '0xebcf',
  policy_rounded: '0xf00ae',
  policy_outlined: '0xf2bc',
  poll: '0xe4da',
  poll_sharp: '0xebd0',
  poll_rounded: '0xf00af',
  poll_outlined: '0xf2bd',
  polyline: '0xf0552',
  polyline_sharp: '0xf045b',
  polyline_rounded: '0xf0368',
  polyline_outlined: '0xf0649',
  polymer: '0xe4db',
  polymer_sharp: '0xebd1',
  polymer_rounded: '0xf00b0',
  polymer_outlined: '0xf2be',
  pool: '0xe4dc',
  pool_sharp: '0xebd2',
  pool_rounded: '0xf00b1',
  pool_outlined: '0xf2bf',
  portable_wifi_off: '0xe4dd',
  portable_wifi_off_sharp: '0xebd3',
  portable_wifi_off_rounded: '0xf00b2',
  portable_wifi_off_outlined: '0xf2c0',
  portrait: '0xe4de',
  portrait_sharp: '0xebd4',
  portrait_rounded: '0xf00b3',
  portrait_outlined: '0xf2c1',
  post_add: '0xe4df',
  post_add_sharp: '0xebd5',
  post_add_rounded: '0xf00b4',
  post_add_outlined: '0xf2c2',
  power: '0xe4e0',
  power_sharp: '0xebd9',
  power_rounded: '0xf00b7',
  power_outlined: '0xf2c5',
  power_input: '0xe4e1',
  power_input_sharp: '0xebd6',
  power_input_rounded: '0xf00b5',
  power_input_outlined: '0xf2c3',
  power_off: '0xe4e2',
  power_off_sharp: '0xebd7',
  power_off_rounded: '0xf00b6',
  power_off_outlined: '0xf2c4',
  power_settings_new: '0xe4e3',
  power_settings_new_sharp: '0xebd8',
  power_settings_new_rounded: '0xf00b8',
  power_settings_new_outlined: '0xf2c6',
  precision_manufacturing: '0xe4e4',
  precision_manufacturing_sharp: '0xebda',
  precision_manufacturing_rounded: '0xf00b9',
  precision_manufacturing_outlined: '0xf2c7',
  pregnant_woman: '0xe4e5',
  pregnant_woman_sharp: '0xebdb',
  pregnant_woman_rounded: '0xf00ba',
  pregnant_woman_outlined: '0xf2c8',
  present_to_all: '0xe4e6',
  present_to_all_sharp: '0xebdc',
  present_to_all_rounded: '0xf00bb',
  present_to_all_outlined: '0xf2c9',
  preview: '0xe4e7',
  preview_sharp: '0xebdd',
  preview_rounded: '0xf00bc',
  preview_outlined: '0xf2ca',
  price_change: '0xe4e8',
  price_change_sharp: '0xebde',
  price_change_rounded: '0xf00bd',
  price_change_outlined: '0xf2cb',
  price_check: '0xe4e9',
  price_check_sharp: '0xebdf',
  price_check_rounded: '0xf00be',
  price_check_outlined: '0xf2cc',
  print: '0xe4ea',
  print_sharp: '0xebe1',
  print_rounded: '0xf00c0',
  print_outlined: '0xf2ce',
  print_disabled: '0xe4eb',
  print_disabled_sharp: '0xebe0',
  print_disabled_rounded: '0xf00bf',
  print_disabled_outlined: '0xf2cd',
  priority_high: '0xe4ec',
  priority_high_sharp: '0xebe2',
  priority_high_rounded: '0xf00c1',
  priority_high_outlined: '0xf2cf',
  privacy_tip: '0xe4ed',
  privacy_tip_sharp: '0xebe3',
  privacy_tip_rounded: '0xf00c2',
  privacy_tip_outlined: '0xf2d0',
  private_connectivity: '0xf0553',
  private_connectivity_sharp: '0xf045c',
  private_connectivity_rounded: '0xf0369',
  private_connectivity_outlined: '0xf064a',
  production_quantity_limits: '0xe4ee',
  production_quantity_limits_sharp: '0xebe4',
  production_quantity_limits_rounded: '0xf00c3',
  production_quantity_limits_outlined: '0xf2d1',
  propane: '0xf07b9',
  propane_sharp: '0xf0761',
  propane_rounded: '0xf0811',
  propane_outlined: '0xf0709',
  propane_tank: '0xf07ba',
  propane_tank_sharp: '0xf0762',
  propane_tank_rounded: '0xf0812',
  propane_tank_outlined: '0xf070a',
  psychology: '0xe4ef',
  psychology_sharp: '0xebe5',
  psychology_rounded: '0xf00c4',
  psychology_outlined: '0xf2d2',
  psychology_alt: '0xf0873',
  psychology_alt_sharp: '0xf084a',
  psychology_alt_rounded: '0xf0893',
  psychology_alt_outlined: '0xf08b1',
  public: '0xe4f0',
  public_sharp: '0xebe7',
  public_rounded: '0xf00c6',
  public_outlined: '0xf2d4',
  public_off: '0xe4f1',
  public_off_sharp: '0xebe6',
  public_off_rounded: '0xf00c5',
  public_off_outlined: '0xf2d3',
  publish: '0xe4f2',
  publish_sharp: '0xebe8',
  publish_rounded: '0xf00c7',
  publish_outlined: '0xf2d5',
  published_with_changes: '0xe4f3',
  published_with_changes_sharp: '0xebe9',
  published_with_changes_rounded: '0xf00c8',
  published_with_changes_outlined: '0xf2d6',
  punch_clock: '0xf0554',
  punch_clock_sharp: '0xf045d',
  punch_clock_rounded: '0xf036a',
  punch_clock_outlined: '0xf064b',
  push_pin: '0xe4f4',
  push_pin_sharp: '0xebea',
  push_pin_rounded: '0xf00c9',
  push_pin_outlined: '0xf2d7',
  qr_code: '0xe4f5',
  qr_code_sharp: '0xebed',
  qr_code_rounded: '0xf00cb',
  qr_code_outlined: '0xf2d9',
  qr_code_2: '0xe4f6',
  qr_code_2_sharp: '0xebeb',
  qr_code_2_rounded: '0xf00ca',
  qr_code_2_outlined: '0xf2d8',
  qr_code_scanner: '0xe4f7',
  qr_code_scanner_sharp: '0xebec',
  qr_code_scanner_rounded: '0xf00cc',
  qr_code_scanner_outlined: '0xf2da',
  query_builder: '0xe4f8',
  query_builder_sharp: '0xebee',
  query_builder_rounded: '0xf00cd',
  query_builder_outlined: '0xf2db',
  query_stats: '0xe4f9',
  query_stats_sharp: '0xebef',
  query_stats_rounded: '0xf00ce',
  query_stats_outlined: '0xf2dc',
  question_answer: '0xe4fa',
  question_answer_sharp: '0xebf0',
  question_answer_rounded: '0xf00cf',
  question_answer_outlined: '0xf2dd',
  question_mark: '0xf0555',
  question_mark_sharp: '0xf045e',
  question_mark_rounded: '0xf036b',
  question_mark_outlined: '0xf064c',
  queue: '0xe4fb',
  queue_sharp: '0xebf3',
  queue_rounded: '0xf00d2',
  queue_outlined: '0xf2df',
  queue_music: '0xe4fc',
  queue_music_sharp: '0xebf1',
  queue_music_rounded: '0xf00d0',
  queue_music_outlined: '0xf2de',
  queue_play_next: '0xe4fd',
  queue_play_next_sharp: '0xebf2',
  queue_play_next_rounded: '0xf00d1',
  queue_play_next_outlined: '0xf2e0',
  quick_contacts_dialer: '0xe18c',
  quick_contacts_dialer_sharp: '0xe889',
  quick_contacts_dialer_rounded: '0xf668',
  quick_contacts_dialer_outlined: '0xef7b',
  quick_contacts_mail: '0xe18a',
  quick_contacts_mail_sharp: '0xe887',
  quick_contacts_mail_rounded: '0xf666',
  quick_contacts_mail_outlined: '0xef79',
  quickreply: '0xe4fe',
  quickreply_sharp: '0xebf4',
  quickreply_rounded: '0xf00d3',
  quickreply_outlined: '0xf2e1',
  quiz: '0xe4ff',
  quiz_sharp: '0xebf5',
  quiz_rounded: '0xf00d4',
  quiz_outlined: '0xf2e2',
  quora: '0xf0556',
  quora_sharp: '0xf045f',
  quora_rounded: '0xf036c',
  quora_outlined: '0xf064d',
  r_mobiledata: '0xe500',
  r_mobiledata_sharp: '0xebf6',
  r_mobiledata_rounded: '0xf00d5',
  r_mobiledata_outlined: '0xf2e3',
  radar: '0xe501',
  radar_sharp: '0xebf7',
  radar_rounded: '0xf00d6',
  radar_outlined: '0xf2e4',
  radio: '0xe502',
  radio_sharp: '0xebfa',
  radio_rounded: '0xf00d9',
  radio_outlined: '0xf2e7',
  radio_button_checked: '0xe503',
  radio_button_checked_sharp: '0xebf8',
  radio_button_checked_rounded: '0xf00d7',
  radio_button_checked_outlined: '0xf2e5',
  radio_button_off: '0xe504',
  radio_button_off_sharp: '0xebf9',
  radio_button_off_rounded: '0xf00d8',
  radio_button_off_outlined: '0xf2e6',
  radio_button_on: '0xe503',
  radio_button_on_sharp: '0xebf8',
  radio_button_on_rounded: '0xf00d7',
  radio_button_on_outlined: '0xf2e5',
  radio_button_unchecked: '0xe504',
  radio_button_unchecked_sharp: '0xebf9',
  radio_button_unchecked_rounded: '0xf00d8',
  radio_button_unchecked_outlined: '0xf2e6',
  railway_alert: '0xe505',
  railway_alert_sharp: '0xebfb',
  railway_alert_rounded: '0xf00da',
  railway_alert_outlined: '0xf2e8',
  ramen_dining: '0xe506',
  ramen_dining_sharp: '0xebfc',
  ramen_dining_rounded: '0xf00db',
  ramen_dining_outlined: '0xf2e9',
  ramp_left: '0xf0557',
  ramp_left_sharp: '0xf0460',
  ramp_left_rounded: '0xf036d',
  ramp_left_outlined: '0xf064e',
  ramp_right: '0xf0558',
  ramp_right_sharp: '0xf0461',
  ramp_right_rounded: '0xf036e',
  ramp_right_outlined: '0xf064f',
  rate_review: '0xe507',
  rate_review_sharp: '0xebfd',
  rate_review_rounded: '0xf00dc',
  rate_review_outlined: '0xf2ea',
  raw_off: '0xe508',
  raw_off_sharp: '0xebfe',
  raw_off_rounded: '0xf00dd',
  raw_off_outlined: '0xf2eb',
  raw_on: '0xe509',
  raw_on_sharp: '0xebff',
  raw_on_rounded: '0xf00de',
  raw_on_outlined: '0xf2ec',
  read_more: '0xe50a',
  read_more_sharp: '0xec00',
  read_more_rounded: '0xf00df',
  read_more_outlined: '0xf2ed',
  real_estate_agent: '0xe50b',
  real_estate_agent_sharp: '0xec01',
  real_estate_agent_rounded: '0xf00e0',
  real_estate_agent_outlined: '0xf2ee',
  rebase_edit: '0xf0874',
  receipt: '0xe50c',
  receipt_sharp: '0xec03',
  receipt_rounded: '0xf00e2',
  receipt_outlined: '0xf2f0',
  receipt_long: '0xe50d',
  receipt_long_sharp: '0xec02',
  receipt_long_rounded: '0xf00e1',
  receipt_long_outlined: '0xf2ef',
  recent_actors: '0xe50e',
  recent_actors_sharp: '0xec04',
  recent_actors_rounded: '0xf00e3',
  recent_actors_outlined: '0xf2f1',
  recommend: '0xe50f',
  recommend_sharp: '0xec05',
  recommend_rounded: '0xf00e4',
  recommend_outlined: '0xf2f2',
  record_voice_over: '0xe510',
  record_voice_over_sharp: '0xec06',
  record_voice_over_rounded: '0xf00e5',
  record_voice_over_outlined: '0xf2f3',
  rectangle: '0xf0559',
  rectangle_sharp: '0xf0462',
  rectangle_rounded: '0xf036f',
  rectangle_outlined: '0xf0650',
  recycling: '0xf055a',
  recycling_sharp: '0xf0463',
  recycling_rounded: '0xf0370',
  recycling_outlined: '0xf0651',
  reddit: '0xf055b',
  reddit_sharp: '0xf0464',
  reddit_rounded: '0xf0371',
  reddit_outlined: '0xf0652',
  redeem: '0xe511',
  redeem_sharp: '0xec07',
  redeem_rounded: '0xf00e6',
  redeem_outlined: '0xf2f4',
  redo: '0xe512',
  redo_sharp: '0xec08',
  redo_rounded: '0xf00e7',
  redo_outlined: '0xf2f5',
  reduce_capacity: '0xe513',
  reduce_capacity_sharp: '0xec09',
  reduce_capacity_rounded: '0xf00e8',
  reduce_capacity_outlined: '0xf2f6',
  refresh: '0xe514',
  refresh_sharp: '0xec0a',
  refresh_rounded: '0xf00e9',
  refresh_outlined: '0xf2f7',
  remember_me: '0xe515',
  remember_me_sharp: '0xec0b',
  remember_me_rounded: '0xf00ea',
  remember_me_outlined: '0xf2f8',
  remove: '0xe516',
  remove_sharp: '0xec12',
  remove_rounded: '0xf00f1',
  remove_outlined: '0xf2fe',
  remove_circle: '0xe517',
  remove_circle_sharp: '0xec0d',
  remove_circle_rounded: '0xf00ec',
  remove_circle_outlined: '0xf2fa',
  remove_circle_outline: '0xe518',
  remove_circle_outline_sharp: '0xec0c',
  remove_circle_outline_rounded: '0xf00eb',
  remove_circle_outline_outlined: '0xf2f9',
  remove_done: '0xe519',
  remove_done_sharp: '0xec0e',
  remove_done_rounded: '0xf00ed',
  remove_done_outlined: '0xf2fb',
  remove_from_queue: '0xe51a',
  remove_from_queue_sharp: '0xec0f',
  remove_from_queue_rounded: '0xf00ee',
  remove_from_queue_outlined: '0xf2fc',
  remove_moderator: '0xe51b',
  remove_moderator_sharp: '0xec10',
  remove_moderator_rounded: '0xf00ef',
  remove_moderator_outlined: '0xf2fd',
  remove_red_eye: '0xe51c',
  remove_red_eye_sharp: '0xec11',
  remove_red_eye_rounded: '0xf00f0',
  remove_red_eye_outlined: '0xf2ff',
  remove_road: '0xf07bb',
  remove_road_sharp: '0xf0763',
  remove_road_rounded: '0xf0813',
  remove_road_outlined: '0xf070b',
  remove_shopping_cart: '0xe51d',
  remove_shopping_cart_sharp: '0xec13',
  remove_shopping_cart_rounded: '0xf00f2',
  remove_shopping_cart_outlined: '0xf300',
  reorder: '0xe51e',
  reorder_sharp: '0xec14',
  reorder_rounded: '0xf00f3',
  reorder_outlined: '0xf301',
  repartition: '0xf0875',
  repartition_sharp: '0xf084b',
  repartition_rounded: '0xf0894',
  repartition_outlined: '0xf08b2',
  repeat: '0xe51f',
  repeat_sharp: '0xec18',
  repeat_rounded: '0xf00f7',
  repeat_outlined: '0xf305',
  repeat_on: '0xe520',
  repeat_on_sharp: '0xec15',
  repeat_on_rounded: '0xf00f4',
  repeat_on_outlined: '0xf302',
  repeat_one: '0xe521',
  repeat_one_sharp: '0xec17',
  repeat_one_rounded: '0xf00f6',
  repeat_one_outlined: '0xf304',
  repeat_one_on: '0xe522',
  repeat_one_on_sharp: '0xec16',
  repeat_one_on_rounded: '0xf00f5',
  repeat_one_on_outlined: '0xf303',
  replay: '0xe523',
  replay_sharp: '0xec1d',
  replay_rounded: '0xf00fc',
  replay_outlined: '0xf30a',
  replay_10: '0xe524',
  replay_10_sharp: '0xec19',
  replay_10_rounded: '0xf00f8',
  replay_10_outlined: '0xf306',
  replay_30: '0xe525',
  replay_30_sharp: '0xec1a',
  replay_30_rounded: '0xf00f9',
  replay_30_outlined: '0xf307',
  replay_5: '0xe526',
  replay_5_sharp: '0xec1b',
  replay_5_rounded: '0xf00fa',
  replay_5_outlined: '0xf308',
  replay_circle_filled: '0xe527',
  replay_circle_filled_sharp: '0xec1c',
  replay_circle_filled_rounded: '0xf00fb',
  replay_circle_filled_outlined: '0xf309',
  reply: '0xe528',
  reply_sharp: '0xec1f',
  reply_rounded: '0xf00fe',
  reply_outlined: '0xf30c',
  reply_all: '0xe529',
  reply_all_sharp: '0xec1e',
  reply_all_rounded: '0xf00fd',
  reply_all_outlined: '0xf30b',
  report: '0xe52a',
  report_sharp: '0xec23',
  report_rounded: '0xf0102',
  report_outlined: '0xf30f',
  report_gmailerrorred: '0xe52b',
  report_gmailerrorred_sharp: '0xec20',
  report_gmailerrorred_rounded: '0xf00ff',
  report_gmailerrorred_outlined: '0xf30d',
  report_off: '0xe52c',
  report_off_sharp: '0xec21',
  report_off_rounded: '0xf0100',
  report_off_outlined: '0xf30e',
  report_problem: '0xe52d',
  report_problem_sharp: '0xec22',
  report_problem_rounded: '0xf0101',
  report_problem_outlined: '0xf310',
  request_page: '0xe52e',
  request_page_sharp: '0xec24',
  request_page_rounded: '0xf0103',
  request_page_outlined: '0xf311',
  request_quote: '0xe52f',
  request_quote_sharp: '0xec25',
  request_quote_rounded: '0xf0104',
  request_quote_outlined: '0xf312',
  reset_tv: '0xe530',
  reset_tv_sharp: '0xec26',
  reset_tv_rounded: '0xf0105',
  reset_tv_outlined: '0xf313',
  restart_alt: '0xe531',
  restart_alt_sharp: '0xec27',
  restart_alt_rounded: '0xf0106',
  restart_alt_outlined: '0xf314',
  restaurant: '0xe532',
  restaurant_sharp: '0xec29',
  restaurant_rounded: '0xf0108',
  restaurant_outlined: '0xf316',
  restaurant_menu: '0xe533',
  restaurant_menu_sharp: '0xec28',
  restaurant_menu_rounded: '0xf0107',
  restaurant_menu_outlined: '0xf315',
  restore: '0xe534',
  restore_sharp: '0xec2c',
  restore_rounded: '0xf010b',
  restore_outlined: '0xf318',
  restore_from_trash: '0xe535',
  restore_from_trash_sharp: '0xec2a',
  restore_from_trash_rounded: '0xf0109',
  restore_from_trash_outlined: '0xf317',
  restore_page: '0xe536',
  restore_page_sharp: '0xec2b',
  restore_page_rounded: '0xf010a',
  restore_page_outlined: '0xf319',
  reviews: '0xe537',
  reviews_sharp: '0xec2d',
  reviews_rounded: '0xf010c',
  reviews_outlined: '0xf31a',
  rice_bowl: '0xe538',
  rice_bowl_sharp: '0xec2e',
  rice_bowl_rounded: '0xf010d',
  rice_bowl_outlined: '0xf31b',
  ring_volume: '0xe539',
  ring_volume_sharp: '0xec2f',
  ring_volume_rounded: '0xf010e',
  ring_volume_outlined: '0xf31c',
  rocket: '0xf055c',
  rocket_sharp: '0xf0466',
  rocket_rounded: '0xf0373',
  rocket_outlined: '0xf0654',
  rocket_launch: '0xf055d',
  rocket_launch_sharp: '0xf0465',
  rocket_launch_rounded: '0xf0372',
  rocket_launch_outlined: '0xf0653',
  roller_shades: '0xf07bc',
  roller_shades_sharp: '0xf0765',
  roller_shades_rounded: '0xf0815',
  roller_shades_outlined: '0xf070d',
  roller_shades_closed: '0xf07bd',
  roller_shades_closed_sharp: '0xf0764',
  roller_shades_closed_rounded: '0xf0814',
  roller_shades_closed_outlined: '0xf070c',
  roller_skating: '0xf06c0',
  roller_skating_sharp: '0xf06b3',
  roller_skating_rounded: '0xf06cd',
  roller_skating_outlined: '0xf06a6',
  roofing: '0xe53a',
  roofing_sharp: '0xec30',
  roofing_rounded: '0xf010f',
  roofing_outlined: '0xf31d',
  room: '0xe53b',
  room_sharp: '0xec33',
  room_rounded: '0xf0111',
  room_outlined: '0xf31e',
  room_preferences: '0xe53c',
  room_preferences_sharp: '0xec31',
  room_preferences_rounded: '0xf0110',
  room_preferences_outlined: '0xf31f',
  room_service: '0xe53d',
  room_service_sharp: '0xec32',
  room_service_rounded: '0xf0112',
  room_service_outlined: '0xf320',
  rotate_90_degrees_ccw: '0xe53e',
  rotate_90_degrees_ccw_sharp: '0xec34',
  rotate_90_degrees_ccw_rounded: '0xf0113',
  rotate_90_degrees_ccw_outlined: '0xf321',
  rotate_90_degrees_cw: '0xf055e',
  rotate_90_degrees_cw_sharp: '0xf0467',
  rotate_90_degrees_cw_rounded: '0xf0374',
  rotate_90_degrees_cw_outlined: '0xf0655',
  rotate_left: '0xe53f',
  rotate_left_sharp: '0xec35',
  rotate_left_rounded: '0xf0114',
  rotate_left_outlined: '0xf322',
  rotate_right: '0xe540',
  rotate_right_sharp: '0xec36',
  rotate_right_rounded: '0xf0115',
  rotate_right_outlined: '0xf323',
  roundabout_left: '0xf055f',
  roundabout_left_sharp: '0xf0468',
  roundabout_left_rounded: '0xf0375',
  roundabout_left_outlined: '0xf0656',
  roundabout_right: '0xf0560',
  roundabout_right_sharp: '0xf0469',
  roundabout_right_rounded: '0xf0376',
  roundabout_right_outlined: '0xf0657',
  rounded_corner: '0xe541',
  rounded_corner_sharp: '0xec37',
  rounded_corner_rounded: '0xf0116',
  rounded_corner_outlined: '0xf324',
  route: '0xf0561',
  route_sharp: '0xf046a',
  route_rounded: '0xf0377',
  route_outlined: '0xf0658',
  router: '0xe542',
  router_sharp: '0xec38',
  router_rounded: '0xf0117',
  router_outlined: '0xf325',
  rowing: '0xe543',
  rowing_sharp: '0xec39',
  rowing_rounded: '0xf0118',
  rowing_outlined: '0xf326',
  rss_feed: '0xe544',
  rss_feed_sharp: '0xec3a',
  rss_feed_rounded: '0xf0119',
  rss_feed_outlined: '0xf327',
  rsvp: '0xe545',
  rsvp_sharp: '0xec3b',
  rsvp_rounded: '0xf011a',
  rsvp_outlined: '0xf328',
  rtt: '0xe546',
  rtt_sharp: '0xec3c',
  rtt_rounded: '0xf011b',
  rtt_outlined: '0xf329',
  rule: '0xe547',
  rule_sharp: '0xec3e',
  rule_rounded: '0xf011d',
  rule_outlined: '0xf32b',
  rule_folder: '0xe548',
  rule_folder_sharp: '0xec3d',
  rule_folder_rounded: '0xf011c',
  rule_folder_outlined: '0xf32a',
  run_circle: '0xe549',
  run_circle_sharp: '0xec3f',
  run_circle_rounded: '0xf011e',
  run_circle_outlined: '0xf32c',
  running_with_errors: '0xe54a',
  running_with_errors_sharp: '0xec40',
  running_with_errors_rounded: '0xf011f',
  running_with_errors_outlined: '0xf32d',
  rv_hookup: '0xe54b',
  rv_hookup_sharp: '0xec41',
  rv_hookup_rounded: '0xf0120',
  rv_hookup_outlined: '0xf32e',
  safety_check: '0xf07be',
  safety_check_sharp: '0xf0766',
  safety_check_rounded: '0xf0816',
  safety_check_outlined: '0xf070e',
  safety_divider: '0xe54c',
  safety_divider_sharp: '0xec42',
  safety_divider_rounded: '0xf0121',
  safety_divider_outlined: '0xf32f',
  sailing: '0xe54d',
  sailing_sharp: '0xec43',
  sailing_rounded: '0xf0122',
  sailing_outlined: '0xf330',
  sanitizer: '0xe54e',
  sanitizer_sharp: '0xec44',
  sanitizer_rounded: '0xf0123',
  sanitizer_outlined: '0xf331',
  satellite: '0xe54f',
  satellite_sharp: '0xec45',
  satellite_rounded: '0xf0124',
  satellite_outlined: '0xf332',
  satellite_alt: '0xf0562',
  satellite_alt_sharp: '0xf046b',
  satellite_alt_rounded: '0xf0378',
  satellite_alt_outlined: '0xf0659',
  save: '0xe550',
  save_sharp: '0xec47',
  save_rounded: '0xf0126',
  save_outlined: '0xf334',
  save_alt: '0xe551',
  save_alt_sharp: '0xec46',
  save_alt_rounded: '0xf0125',
  save_alt_outlined: '0xf333',
  save_as: '0xf0563',
  save_as_sharp: '0xf046c',
  save_as_rounded: '0xf0379',
  save_as_outlined: '0xf065a',
  saved_search: '0xe552',
  saved_search_sharp: '0xec48',
  saved_search_rounded: '0xf0127',
  saved_search_outlined: '0xf335',
  savings: '0xe553',
  savings_sharp: '0xec49',
  savings_rounded: '0xf0128',
  savings_outlined: '0xf336',
  scale: '0xf0564',
  scale_sharp: '0xf046d',
  scale_rounded: '0xf037a',
  scale_outlined: '0xf065b',
  scanner: '0xe554',
  scanner_sharp: '0xec4a',
  scanner_rounded: '0xf0129',
  scanner_outlined: '0xf337',
  scatter_plot: '0xe555',
  scatter_plot_sharp: '0xec4b',
  scatter_plot_rounded: '0xf012a',
  scatter_plot_outlined: '0xf338',
  schedule: '0xe556',
  schedule_sharp: '0xec4d',
  schedule_rounded: '0xf012b',
  schedule_outlined: '0xf339',
  schedule_send: '0xe557',
  schedule_send_sharp: '0xec4c',
  schedule_send_rounded: '0xf012c',
  schedule_send_outlined: '0xf33a',
  schema: '0xe558',
  schema_sharp: '0xec4e',
  schema_rounded: '0xf012d',
  schema_outlined: '0xf33b',
  school: '0xe559',
  school_sharp: '0xec4f',
  school_rounded: '0xf012e',
  school_outlined: '0xf33c',
  science: '0xe55a',
  science_sharp: '0xec50',
  science_rounded: '0xf012f',
  science_outlined: '0xf33d',
  score: '0xe55b',
  score_sharp: '0xec51',
  score_rounded: '0xf0130',
  score_outlined: '0xf33e',
  scoreboard: '0xf06c1',
  scoreboard_sharp: '0xf06b4',
  scoreboard_rounded: '0xf06ce',
  scoreboard_outlined: '0xf06a7',
  screen_lock_landscape: '0xe55c',
  screen_lock_landscape_sharp: '0xec52',
  screen_lock_landscape_rounded: '0xf0131',
  screen_lock_landscape_outlined: '0xf33f',
  screen_lock_portrait: '0xe55d',
  screen_lock_portrait_sharp: '0xec53',
  screen_lock_portrait_rounded: '0xf0132',
  screen_lock_portrait_outlined: '0xf340',
  screen_lock_rotation: '0xe55e',
  screen_lock_rotation_sharp: '0xec54',
  screen_lock_rotation_rounded: '0xf0133',
  screen_lock_rotation_outlined: '0xf341',
  screen_rotation: '0xe55f',
  screen_rotation_sharp: '0xec55',
  screen_rotation_rounded: '0xf0134',
  screen_rotation_outlined: '0xf342',
  screen_rotation_alt: '0xf07bf',
  screen_rotation_alt_sharp: '0xf0767',
  screen_rotation_alt_rounded: '0xf0817',
  screen_rotation_alt_outlined: '0xf070f',
  screen_search_desktop: '0xe560',
  screen_search_desktop_sharp: '0xec56',
  screen_search_desktop_rounded: '0xf0135',
  screen_search_desktop_outlined: '0xf343',
  screen_share: '0xe561',
  screen_share_sharp: '0xec57',
  screen_share_rounded: '0xf0136',
  screen_share_outlined: '0xf344',
  screenshot: '0xe562',
  screenshot_sharp: '0xec58',
  screenshot_rounded: '0xf0137',
  screenshot_outlined: '0xf345',
  screenshot_monitor: '0xf07c0',
  screenshot_monitor_sharp: '0xf0768',
  screenshot_monitor_rounded: '0xf0818',
  screenshot_monitor_outlined: '0xf0710',
  scuba_diving: '0xf06c2',
  scuba_diving_sharp: '0xf06b5',
  scuba_diving_rounded: '0xf06cf',
  scuba_diving_outlined: '0xf06a8',
  sd: '0xe563',
  sd_sharp: '0xec5b',
  sd_rounded: '0xf013a',
  sd_outlined: '0xf348',
  sd_card: '0xe564',
  sd_card_sharp: '0xec5a',
  sd_card_rounded: '0xf0139',
  sd_card_outlined: '0xf347',
  sd_card_alert: '0xe565',
  sd_card_alert_sharp: '0xec59',
  sd_card_alert_rounded: '0xf0138',
  sd_card_alert_outlined: '0xf346',
  sd_storage: '0xe566',
  sd_storage_sharp: '0xec5c',
  sd_storage_rounded: '0xf013b',
  sd_storage_outlined: '0xf349',
  search: '0xe567',
  search_sharp: '0xec5e',
  search_rounded: '0xf013d',
  search_outlined: '0xf34b',
  search_off: '0xe568',
  search_off_sharp: '0xec5d',
  search_off_rounded: '0xf013c',
  search_off_outlined: '0xf34a',
  security: '0xe569',
  security_sharp: '0xec5f',
  security_rounded: '0xf013e',
  security_outlined: '0xf34c',
  security_update: '0xe56a',
  security_update_sharp: '0xec61',
  security_update_rounded: '0xf0140',
  security_update_outlined: '0xf34e',
  security_update_good: '0xe56b',
  security_update_good_sharp: '0xec60',
  security_update_good_rounded: '0xf013f',
  security_update_good_outlined: '0xf34d',
  security_update_warning: '0xe56c',
  security_update_warning_sharp: '0xec62',
  security_update_warning_rounded: '0xf0141',
  security_update_warning_outlined: '0xf34f',
  segment: '0xe56d',
  segment_sharp: '0xec63',
  segment_rounded: '0xf0142',
  segment_outlined: '0xf350',
  select_all: '0xe56e',
  select_all_sharp: '0xec64',
  select_all_rounded: '0xf0143',
  select_all_outlined: '0xf351',
  self_improvement: '0xe56f',
  self_improvement_sharp: '0xec65',
  self_improvement_rounded: '0xf0144',
  self_improvement_outlined: '0xf352',
  sell: '0xe570',
  sell_sharp: '0xec66',
  sell_rounded: '0xf0145',
  sell_outlined: '0xf353',
  send: '0xe571',
  send_sharp: '0xec68',
  send_rounded: '0xf0147',
  send_outlined: '0xf355',
  send_and_archive: '0xe572',
  send_and_archive_sharp: '0xec67',
  send_and_archive_rounded: '0xf0146',
  send_and_archive_outlined: '0xf354',
  send_time_extension: '0xf0565',
  send_time_extension_sharp: '0xf046e',
  send_time_extension_rounded: '0xf037b',
  send_time_extension_outlined: '0xf065c',
  send_to_mobile: '0xe573',
  send_to_mobile_sharp: '0xec69',
  send_to_mobile_rounded: '0xf0148',
  send_to_mobile_outlined: '0xf356',
  sensor_door: '0xe574',
  sensor_door_sharp: '0xec6a',
  sensor_door_rounded: '0xf0149',
  sensor_door_outlined: '0xf357',
  sensor_occupied: '0xf07c1',
  sensor_occupied_sharp: '0xf0769',
  sensor_occupied_rounded: '0xf0819',
  sensor_occupied_outlined: '0xf0711',
  sensor_window: '0xe575',
  sensor_window_sharp: '0xec6b',
  sensor_window_rounded: '0xf014a',
  sensor_window_outlined: '0xf358',
  sensors: '0xe576',
  sensors_sharp: '0xec6d',
  sensors_rounded: '0xf014c',
  sensors_outlined: '0xf35a',
  sensors_off: '0xe577',
  sensors_off_sharp: '0xec6c',
  sensors_off_rounded: '0xf014b',
  sensors_off_outlined: '0xf359',
  sentiment_dissatisfied: '0xe578',
  sentiment_dissatisfied_sharp: '0xec6e',
  sentiment_dissatisfied_rounded: '0xf014d',
  sentiment_dissatisfied_outlined: '0xf35b',
  sentiment_neutral: '0xe579',
  sentiment_neutral_sharp: '0xec6f',
  sentiment_neutral_rounded: '0xf014e',
  sentiment_neutral_outlined: '0xf35c',
  sentiment_satisfied: '0xe57a',
  sentiment_satisfied_sharp: '0xec71',
  sentiment_satisfied_rounded: '0xf0150',
  sentiment_satisfied_outlined: '0xf35e',
  sentiment_satisfied_alt: '0xe57b',
  sentiment_satisfied_alt_sharp: '0xec70',
  sentiment_satisfied_alt_rounded: '0xf014f',
  sentiment_satisfied_alt_outlined: '0xf35d',
  sentiment_very_dissatisfied: '0xe57c',
  sentiment_very_dissatisfied_sharp: '0xec72',
  sentiment_very_dissatisfied_rounded: '0xf0151',
  sentiment_very_dissatisfied_outlined: '0xf35f',
  sentiment_very_satisfied: '0xe57d',
  sentiment_very_satisfied_sharp: '0xec73',
  sentiment_very_satisfied_rounded: '0xf0152',
  sentiment_very_satisfied_outlined: '0xf360',
  set_meal: '0xe57e',
  set_meal_sharp: '0xec74',
  set_meal_rounded: '0xf0153',
  set_meal_outlined: '0xf361',
  settings: '0xe57f',
  settings_sharp: '0xec85',
  settings_rounded: '0xf0164',
  settings_outlined: '0xf36e',
  settings_accessibility: '0xe580',
  settings_accessibility_sharp: '0xec75',
  settings_accessibility_rounded: '0xf0154',
  settings_accessibility_outlined: '0xf362',
  settings_applications: '0xe581',
  settings_applications_sharp: '0xec76',
  settings_applications_rounded: '0xf0155',
  settings_applications_outlined: '0xf363',
  settings_backup_restore: '0xe582',
  settings_backup_restore_sharp: '0xec77',
  settings_backup_restore_rounded: '0xf0156',
  settings_backup_restore_outlined: '0xf364',
  settings_bluetooth: '0xe583',
  settings_bluetooth_sharp: '0xec78',
  settings_bluetooth_rounded: '0xf0157',
  settings_bluetooth_outlined: '0xf365',
  settings_brightness: '0xe584',
  settings_brightness_sharp: '0xec79',
  settings_brightness_rounded: '0xf0158',
  settings_brightness_outlined: '0xf366',
  settings_cell: '0xe585',
  settings_cell_sharp: '0xec7a',
  settings_cell_rounded: '0xf0159',
  settings_cell_outlined: '0xf367',
  settings_display: '0xe584',
  settings_display_sharp: '0xec79',
  settings_display_rounded: '0xf0158',
  settings_display_outlined: '0xf366',
  settings_ethernet: '0xe586',
  settings_ethernet_sharp: '0xec7b',
  settings_ethernet_rounded: '0xf015a',
  settings_ethernet_outlined: '0xf368',
  settings_input_antenna: '0xe587',
  settings_input_antenna_sharp: '0xec7c',
  settings_input_antenna_rounded: '0xf015b',
  settings_input_antenna_outlined: '0xf369',
  settings_input_component: '0xe588',
  settings_input_component_sharp: '0xec7d',
  settings_input_component_rounded: '0xf015c',
  settings_input_component_outlined: '0xf36a',
  settings_input_composite: '0xe589',
  settings_input_composite_sharp: '0xec7e',
  settings_input_composite_rounded: '0xf015d',
  settings_input_composite_outlined: '0xf36b',
  settings_input_hdmi: '0xe58a',
  settings_input_hdmi_sharp: '0xec7f',
  settings_input_hdmi_rounded: '0xf015e',
  settings_input_hdmi_outlined: '0xf36c',
  settings_input_svideo: '0xe58b',
  settings_input_svideo_sharp: '0xec80',
  settings_input_svideo_rounded: '0xf015f',
  settings_input_svideo_outlined: '0xf36d',
  settings_overscan: '0xe58c',
  settings_overscan_sharp: '0xec81',
  settings_overscan_rounded: '0xf0160',
  settings_overscan_outlined: '0xf36f',
  settings_phone: '0xe58d',
  settings_phone_sharp: '0xec82',
  settings_phone_rounded: '0xf0161',
  settings_phone_outlined: '0xf370',
  settings_power: '0xe58e',
  settings_power_sharp: '0xec83',
  settings_power_rounded: '0xf0162',
  settings_power_outlined: '0xf371',
  settings_remote: '0xe58f',
  settings_remote_sharp: '0xec84',
  settings_remote_rounded: '0xf0163',
  settings_remote_outlined: '0xf372',
  settings_suggest: '0xe590',
  settings_suggest_sharp: '0xec86',
  settings_suggest_rounded: '0xf0165',
  settings_suggest_outlined: '0xf373',
  settings_system_daydream: '0xe591',
  settings_system_daydream_sharp: '0xec87',
  settings_system_daydream_rounded: '0xf0166',
  settings_system_daydream_outlined: '0xf374',
  settings_voice: '0xe592',
  settings_voice_sharp: '0xec88',
  settings_voice_rounded: '0xf0167',
  settings_voice_outlined: '0xf375',
  severe_cold: '0xf07c2',
  severe_cold_sharp: '0xf076a',
  severe_cold_rounded: '0xf081a',
  severe_cold_outlined: '0xf0712',
  shape_line: '0xf0876',
  shape_line_sharp: '0xf084c',
  shape_line_rounded: '0xf0895',
  shape_line_outlined: '0xf08b3',
  share: '0xe593',
  share_sharp: '0xec8b',
  share_rounded: '0xf016a',
  share_outlined: '0xf378',
  share_arrival_time: '0xe594',
  share_arrival_time_sharp: '0xec89',
  share_arrival_time_rounded: '0xf0168',
  share_arrival_time_outlined: '0xf376',
  share_location: '0xe595',
  share_location_sharp: '0xec8a',
  share_location_rounded: '0xf0169',
  share_location_outlined: '0xf377',
  shelves: '0xf0877',
  shield: '0xe596',
  shield_sharp: '0xec8c',
  shield_rounded: '0xf016b',
  shield_outlined: '0xf379',
  shield_moon: '0xf0566',
  shield_moon_sharp: '0xf046f',
  shield_moon_rounded: '0xf037c',
  shield_moon_outlined: '0xf065d',
  shop: '0xe597',
  shop_sharp: '0xec8e',
  shop_rounded: '0xf016d',
  shop_outlined: '0xf37b',
  shop_2: '0xe598',
  shop_2_sharp: '0xec8d',
  shop_2_rounded: '0xf016c',
  shop_2_outlined: '0xf37a',
  shop_two: '0xe599',
  shop_two_sharp: '0xec8f',
  shop_two_rounded: '0xf016e',
  shop_two_outlined: '0xf37c',
  shopify: '0xf0567',
  shopify_sharp: '0xf0470',
  shopify_rounded: '0xf037d',
  shopify_outlined: '0xf065e',
  shopping_bag: '0xe59a',
  shopping_bag_sharp: '0xec90',
  shopping_bag_rounded: '0xf016f',
  shopping_bag_outlined: '0xf37d',
  shopping_basket: '0xe59b',
  shopping_basket_sharp: '0xec91',
  shopping_basket_rounded: '0xf0170',
  shopping_basket_outlined: '0xf37e',
  shopping_cart: '0xe59c',
  shopping_cart_sharp: '0xec92',
  shopping_cart_rounded: '0xf0171',
  shopping_cart_outlined: '0xf37f',
  shopping_cart_checkout: '0xf0568',
  shopping_cart_checkout_sharp: '0xf0471',
  shopping_cart_checkout_rounded: '0xf037e',
  shopping_cart_checkout_outlined: '0xf065f',
  short_text: '0xe59d',
  short_text_sharp: '0xec93',
  short_text_rounded: '0xf0172',
  short_text_outlined: '0xf380',
  shortcut: '0xe59e',
  shortcut_sharp: '0xec94',
  shortcut_rounded: '0xf0173',
  shortcut_outlined: '0xf381',
  show_chart: '0xe59f',
  show_chart_sharp: '0xec95',
  show_chart_rounded: '0xf0174',
  show_chart_outlined: '0xf382',
  shower: '0xe5a0',
  shower_sharp: '0xec96',
  shower_rounded: '0xf0175',
  shower_outlined: '0xf383',
  shuffle: '0xe5a1',
  shuffle_sharp: '0xec98',
  shuffle_rounded: '0xf0177',
  shuffle_outlined: '0xf385',
  shuffle_on: '0xe5a2',
  shuffle_on_sharp: '0xec97',
  shuffle_on_rounded: '0xf0176',
  shuffle_on_outlined: '0xf384',
  shutter_speed: '0xe5a3',
  shutter_speed_sharp: '0xec99',
  shutter_speed_rounded: '0xf0178',
  shutter_speed_outlined: '0xf386',
  sick: '0xe5a4',
  sick_sharp: '0xec9a',
  sick_rounded: '0xf0179',
  sick_outlined: '0xf387',
  sign_language: '0xf07c3',
  sign_language_sharp: '0xf076b',
  sign_language_rounded: '0xf081b',
  sign_language_outlined: '0xf0713',
  signal_cellular_0_bar: '0xe5a5',
  signal_cellular_0_bar_sharp: '0xec9b',
  signal_cellular_0_bar_rounded: '0xf017a',
  signal_cellular_0_bar_outlined: '0xf388',
  signal_cellular_4_bar: '0xe5a6',
  signal_cellular_4_bar_sharp: '0xec9c',
  signal_cellular_4_bar_rounded: '0xf017b',
  signal_cellular_4_bar_outlined: '0xf389',
  signal_cellular_alt: '0xe5a7',
  signal_cellular_alt_sharp: '0xec9d',
  signal_cellular_alt_rounded: '0xf017c',
  signal_cellular_alt_outlined: '0xf38a',
  signal_cellular_alt_1_bar: '0xf07c4',
  signal_cellular_alt_1_bar_sharp: '0xf076c',
  signal_cellular_alt_1_bar_rounded: '0xf081c',
  signal_cellular_alt_1_bar_outlined: '0xf0714',
  signal_cellular_alt_2_bar: '0xf07c5',
  signal_cellular_alt_2_bar_sharp: '0xf076d',
  signal_cellular_alt_2_bar_rounded: '0xf081d',
  signal_cellular_alt_2_bar_outlined: '0xf0715',
  signal_cellular_connected_no_internet_0_bar: '0xe5a8',
  signal_cellular_connected_no_internet_0_bar_sharp: '0xec9e',
  signal_cellular_connected_no_internet_0_bar_rounded: '0xf017d',
  signal_cellular_connected_no_internet_0_bar_outlined: '0xf38b',
  signal_cellular_connected_no_internet_4_bar: '0xe5a9',
  signal_cellular_connected_no_internet_4_bar_sharp: '0xec9f',
  signal_cellular_connected_no_internet_4_bar_rounded: '0xf017e',
  signal_cellular_connected_no_internet_4_bar_outlined: '0xf38c',
  signal_cellular_no_sim: '0xe5aa',
  signal_cellular_no_sim_sharp: '0xeca0',
  signal_cellular_no_sim_rounded: '0xf017f',
  signal_cellular_no_sim_outlined: '0xf38d',
  signal_cellular_nodata: '0xe5ab',
  signal_cellular_nodata_sharp: '0xeca1',
  signal_cellular_nodata_rounded: '0xf0180',
  signal_cellular_nodata_outlined: '0xf38e',
  signal_cellular_null: '0xe5ac',
  signal_cellular_null_sharp: '0xeca2',
  signal_cellular_null_rounded: '0xf0181',
  signal_cellular_null_outlined: '0xf38f',
  signal_cellular_off: '0xe5ad',
  signal_cellular_off_sharp: '0xeca3',
  signal_cellular_off_rounded: '0xf0182',
  signal_cellular_off_outlined: '0xf390',
  signal_wifi_0_bar: '0xe5ae',
  signal_wifi_0_bar_sharp: '0xeca4',
  signal_wifi_0_bar_rounded: '0xf0183',
  signal_wifi_0_bar_outlined: '0xf391',
  signal_wifi_4_bar: '0xe5af',
  signal_wifi_4_bar_sharp: '0xeca6',
  signal_wifi_4_bar_rounded: '0xf0185',
  signal_wifi_4_bar_outlined: '0xf393',
  signal_wifi_4_bar_lock: '0xe5b0',
  signal_wifi_4_bar_lock_sharp: '0xeca5',
  signal_wifi_4_bar_lock_rounded: '0xf0184',
  signal_wifi_4_bar_lock_outlined: '0xf392',
  signal_wifi_bad: '0xe5b1',
  signal_wifi_bad_sharp: '0xeca7',
  signal_wifi_bad_rounded: '0xf0186',
  signal_wifi_bad_outlined: '0xf394',
  signal_wifi_connected_no_internet_4: '0xe5b2',
  signal_wifi_connected_no_internet_4_sharp: '0xeca8',
  signal_wifi_connected_no_internet_4_rounded: '0xf0187',
  signal_wifi_connected_no_internet_4_outlined: '0xf395',
  signal_wifi_off: '0xe5b3',
  signal_wifi_off_sharp: '0xeca9',
  signal_wifi_off_rounded: '0xf0188',
  signal_wifi_off_outlined: '0xf396',
  signal_wifi_statusbar_4_bar: '0xe5b4',
  signal_wifi_statusbar_4_bar_sharp: '0xecaa',
  signal_wifi_statusbar_4_bar_rounded: '0xf0189',
  signal_wifi_statusbar_4_bar_outlined: '0xf397',
  signal_wifi_statusbar_connected_no_internet_4: '0xe5b5',
  signal_wifi_statusbar_connected_no_internet_4_sharp: '0xecab',
  signal_wifi_statusbar_connected_no_internet_4_rounded: '0xf018a',
  signal_wifi_statusbar_connected_no_internet_4_outlined: '0xf398',
  signal_wifi_statusbar_null: '0xe5b6',
  signal_wifi_statusbar_null_sharp: '0xecac',
  signal_wifi_statusbar_null_rounded: '0xf018b',
  signal_wifi_statusbar_null_outlined: '0xf399',
  signpost: '0xf0569',
  signpost_sharp: '0xf0472',
  signpost_rounded: '0xf037f',
  signpost_outlined: '0xf0660',
  sim_card: '0xe5b7',
  sim_card_sharp: '0xecaf',
  sim_card_rounded: '0xf018e',
  sim_card_outlined: '0xf39c',
  sim_card_alert: '0xe5b8',
  sim_card_alert_sharp: '0xecad',
  sim_card_alert_rounded: '0xf018c',
  sim_card_alert_outlined: '0xf39a',
  sim_card_download: '0xe5b9',
  sim_card_download_sharp: '0xecae',
  sim_card_download_rounded: '0xf018d',
  sim_card_download_outlined: '0xf39b',
  single_bed: '0xe5ba',
  single_bed_sharp: '0xecb0',
  single_bed_rounded: '0xf018f',
  single_bed_outlined: '0xf39d',
  sip: '0xe5bb',
  sip_sharp: '0xecb1',
  sip_rounded: '0xf0190',
  sip_outlined: '0xf39e',
  skateboarding: '0xe5bc',
  skateboarding_sharp: '0xecb2',
  skateboarding_rounded: '0xf0191',
  skateboarding_outlined: '0xf39f',
  skip_next: '0xe5bd',
  skip_next_sharp: '0xecb3',
  skip_next_rounded: '0xf0192',
  skip_next_outlined: '0xf3a0',
  skip_previous: '0xe5be',
  skip_previous_sharp: '0xecb4',
  skip_previous_rounded: '0xf0193',
  skip_previous_outlined: '0xf3a1',
  sledding: '0xe5bf',
  sledding_sharp: '0xecb5',
  sledding_rounded: '0xf0194',
  sledding_outlined: '0xf3a2',
  slideshow: '0xe5c0',
  slideshow_sharp: '0xecb6',
  slideshow_rounded: '0xf0195',
  slideshow_outlined: '0xf3a3',
  slow_motion_video: '0xe5c1',
  slow_motion_video_sharp: '0xecb7',
  slow_motion_video_rounded: '0xf0196',
  slow_motion_video_outlined: '0xf3a4',
  smart_button: '0xe5c2',
  smart_button_sharp: '0xecb8',
  smart_button_rounded: '0xf0197',
  smart_button_outlined: '0xf3a5',
  smart_display: '0xe5c3',
  smart_display_sharp: '0xecb9',
  smart_display_rounded: '0xf0198',
  smart_display_outlined: '0xf3a6',
  smart_screen: '0xe5c4',
  smart_screen_sharp: '0xecba',
  smart_screen_rounded: '0xf0199',
  smart_screen_outlined: '0xf3a7',
  smart_toy: '0xe5c5',
  smart_toy_sharp: '0xecbb',
  smart_toy_rounded: '0xf019a',
  smart_toy_outlined: '0xf3a8',
  smartphone: '0xe5c6',
  smartphone_sharp: '0xecbc',
  smartphone_rounded: '0xf019b',
  smartphone_outlined: '0xf3a9',
  smoke_free: '0xe5c7',
  smoke_free_sharp: '0xecbd',
  smoke_free_rounded: '0xf019c',
  smoke_free_outlined: '0xf3aa',
  smoking_rooms: '0xe5c8',
  smoking_rooms_sharp: '0xecbe',
  smoking_rooms_rounded: '0xf019d',
  smoking_rooms_outlined: '0xf3ab',
  sms: '0xe5c9',
  sms_sharp: '0xecc0',
  sms_rounded: '0xf019f',
  sms_outlined: '0xf3ad',
  sms_failed: '0xe5ca',
  sms_failed_sharp: '0xecbf',
  sms_failed_rounded: '0xf019e',
  sms_failed_outlined: '0xf3ac',
  snapchat: '0xf056a',
  snapchat_sharp: '0xf0473',
  snapchat_rounded: '0xf0380',
  snapchat_outlined: '0xf0661',
  snippet_folder: '0xe5cb',
  snippet_folder_sharp: '0xecc1',
  snippet_folder_rounded: '0xf01a0',
  snippet_folder_outlined: '0xf3ae',
  snooze: '0xe5cc',
  snooze_sharp: '0xecc2',
  snooze_rounded: '0xf01a1',
  snooze_outlined: '0xf3af',
  snowboarding: '0xe5cd',
  snowboarding_sharp: '0xecc3',
  snowboarding_rounded: '0xf01a2',
  snowboarding_outlined: '0xf3b0',
  snowing: '0xf056b',
  snowmobile: '0xe5ce',
  snowmobile_sharp: '0xecc4',
  snowmobile_rounded: '0xf01a3',
  snowmobile_outlined: '0xf3b1',
  snowshoeing: '0xe5cf',
  snowshoeing_sharp: '0xecc5',
  snowshoeing_rounded: '0xf01a4',
  snowshoeing_outlined: '0xf3b2',
  soap: '0xe5d0',
  soap_sharp: '0xecc6',
  soap_rounded: '0xf01a5',
  soap_outlined: '0xf3b3',
  social_distance: '0xe5d1',
  social_distance_sharp: '0xecc7',
  social_distance_rounded: '0xf01a6',
  social_distance_outlined: '0xf3b4',
  solar_power: '0xf07c6',
  solar_power_sharp: '0xf076e',
  solar_power_rounded: '0xf081e',
  solar_power_outlined: '0xf0716',
  sort: '0xe5d2',
  sort_sharp: '0xecc9',
  sort_rounded: '0xf01a8',
  sort_outlined: '0xf3b6',
  sort_by_alpha: '0xe5d3',
  sort_by_alpha_sharp: '0xecc8',
  sort_by_alpha_rounded: '0xf01a7',
  sort_by_alpha_outlined: '0xf3b5',
  sos: '0xf07c7',
  sos_sharp: '0xf076f',
  sos_rounded: '0xf081f',
  sos_outlined: '0xf0717',
  soup_kitchen: '0xf056c',
  soup_kitchen_sharp: '0xf0474',
  soup_kitchen_rounded: '0xf0381',
  soup_kitchen_outlined: '0xf0662',
  source: '0xe5d4',
  source_sharp: '0xecca',
  source_rounded: '0xf01a9',
  source_outlined: '0xf3b7',
  south: '0xe5d5',
  south_sharp: '0xeccc',
  south_rounded: '0xf01ab',
  south_outlined: '0xf3b9',
  south_america: '0xf056d',
  south_america_sharp: '0xf0475',
  south_america_rounded: '0xf0382',
  south_america_outlined: '0xf0663',
  south_east: '0xe5d6',
  south_east_sharp: '0xeccb',
  south_east_rounded: '0xf01aa',
  south_east_outlined: '0xf3b8',
  south_west: '0xe5d7',
  south_west_sharp: '0xeccd',
  south_west_rounded: '0xf01ac',
  south_west_outlined: '0xf3ba',
  spa: '0xe5d8',
  spa_sharp: '0xecce',
  spa_rounded: '0xf01ad',
  spa_outlined: '0xf3bb',
  space_bar: '0xe5d9',
  space_bar_sharp: '0xeccf',
  space_bar_rounded: '0xf01ae',
  space_bar_outlined: '0xf3bc',
  space_dashboard: '0xe5da',
  space_dashboard_sharp: '0xecd0',
  space_dashboard_rounded: '0xf01af',
  space_dashboard_outlined: '0xf3bd',
  spatial_audio: '0xf07c8',
  spatial_audio_sharp: '0xf0771',
  spatial_audio_rounded: '0xf0821',
  spatial_audio_outlined: '0xf0719',
  spatial_audio_off: '0xf07c9',
  spatial_audio_off_sharp: '0xf0770',
  spatial_audio_off_rounded: '0xf0820',
  spatial_audio_off_outlined: '0xf0718',
  spatial_tracking: '0xf07ca',
  spatial_tracking_sharp: '0xf0772',
  spatial_tracking_rounded: '0xf0822',
  spatial_tracking_outlined: '0xf071a',
  speaker: '0xe5db',
  speaker_sharp: '0xecd5',
  speaker_rounded: '0xf01b4',
  speaker_outlined: '0xf3c1',
  speaker_group: '0xe5dc',
  speaker_group_sharp: '0xecd1',
  speaker_group_rounded: '0xf01b0',
  speaker_group_outlined: '0xf3be',
  speaker_notes: '0xe5dd',
  speaker_notes_sharp: '0xecd3',
  speaker_notes_rounded: '0xf01b2',
  speaker_notes_outlined: '0xf3c0',
  speaker_notes_off: '0xe5de',
  speaker_notes_off_sharp: '0xecd2',
  speaker_notes_off_rounded: '0xf01b1',
  speaker_notes_off_outlined: '0xf3bf',
  speaker_phone: '0xe5df',
  speaker_phone_sharp: '0xecd4',
  speaker_phone_rounded: '0xf01b3',
  speaker_phone_outlined: '0xf3c2',
  speed: '0xe5e0',
  speed_sharp: '0xecd6',
  speed_rounded: '0xf01b5',
  speed_outlined: '0xf3c3',
  spellcheck: '0xe5e1',
  spellcheck_sharp: '0xecd7',
  spellcheck_rounded: '0xf01b6',
  spellcheck_outlined: '0xf3c4',
  splitscreen: '0xe5e2',
  splitscreen_sharp: '0xecd8',
  splitscreen_rounded: '0xf01b7',
  splitscreen_outlined: '0xf3c5',
  spoke: '0xf056e',
  spoke_sharp: '0xf0476',
  spoke_rounded: '0xf0383',
  spoke_outlined: '0xf0664',
  sports: '0xe5e3',
  sports_sharp: '0xece7',
  sports_rounded: '0xf01c4',
  sports_outlined: '0xf3d2',
  sports_bar: '0xe5e4',
  sports_bar_sharp: '0xecd9',
  sports_bar_rounded: '0xf01b8',
  sports_bar_outlined: '0xf3c6',
  sports_baseball: '0xe5e5',
  sports_baseball_sharp: '0xecda',
  sports_baseball_rounded: '0xf01b9',
  sports_baseball_outlined: '0xf3c7',
  sports_basketball: '0xe5e6',
  sports_basketball_sharp: '0xecdb',
  sports_basketball_rounded: '0xf01ba',
  sports_basketball_outlined: '0xf3c8',
  sports_cricket: '0xe5e7',
  sports_cricket_sharp: '0xecdc',
  sports_cricket_rounded: '0xf01bb',
  sports_cricket_outlined: '0xf3c9',
  sports_esports: '0xe5e8',
  sports_esports_sharp: '0xecdd',
  sports_esports_rounded: '0xf01bc',
  sports_esports_outlined: '0xf3ca',
  sports_football: '0xe5e9',
  sports_football_sharp: '0xecde',
  sports_football_rounded: '0xf01bd',
  sports_football_outlined: '0xf3cb',
  sports_golf: '0xe5ea',
  sports_golf_sharp: '0xecdf',
  sports_golf_rounded: '0xf01be',
  sports_golf_outlined: '0xf3cc',
  sports_gymnastics: '0xf06c3',
  sports_gymnastics_sharp: '0xf06b6',
  sports_gymnastics_rounded: '0xf06d0',
  sports_gymnastics_outlined: '0xf06a9',
  sports_handball: '0xe5eb',
  sports_handball_sharp: '0xece0',
  sports_handball_rounded: '0xf01bf',
  sports_handball_outlined: '0xf3cd',
  sports_hockey: '0xe5ec',
  sports_hockey_sharp: '0xece1',
  sports_hockey_rounded: '0xf01c0',
  sports_hockey_outlined: '0xf3ce',
  sports_kabaddi: '0xe5ed',
  sports_kabaddi_sharp: '0xece2',
  sports_kabaddi_rounded: '0xf01c1',
  sports_kabaddi_outlined: '0xf3cf',
  sports_martial_arts: '0xf056f',
  sports_martial_arts_sharp: '0xf0477',
  sports_martial_arts_rounded: '0xf0384',
  sports_martial_arts_outlined: '0xf0665',
  sports_mma: '0xe5ee',
  sports_mma_sharp: '0xece3',
  sports_mma_rounded: '0xf01c2',
  sports_mma_outlined: '0xf3d0',
  sports_motorsports: '0xe5ef',
  sports_motorsports_sharp: '0xece4',
  sports_motorsports_rounded: '0xf01c3',
  sports_motorsports_outlined: '0xf3d1',
  sports_rugby: '0xe5f0',
  sports_rugby_sharp: '0xece5',
  sports_rugby_rounded: '0xf01c5',
  sports_rugby_outlined: '0xf3d3',
  sports_score: '0xe5f1',
  sports_score_sharp: '0xece6',
  sports_score_rounded: '0xf01c6',
  sports_score_outlined: '0xf3d4',
  sports_soccer: '0xe5f2',
  sports_soccer_sharp: '0xece8',
  sports_soccer_rounded: '0xf01c7',
  sports_soccer_outlined: '0xf3d5',
  sports_tennis: '0xe5f3',
  sports_tennis_sharp: '0xece9',
  sports_tennis_rounded: '0xf01c8',
  sports_tennis_outlined: '0xf3d6',
  sports_volleyball: '0xe5f4',
  sports_volleyball_sharp: '0xecea',
  sports_volleyball_rounded: '0xf01c9',
  sports_volleyball_outlined: '0xf3d7',
  square: '0xf0570',
  square_sharp: '0xf0478',
  square_rounded: '0xf0385',
  square_outlined: '0xf0666',
  square_foot: '0xe5f5',
  square_foot_sharp: '0xeceb',
  square_foot_rounded: '0xf01ca',
  square_foot_outlined: '0xf3d8',
  ssid_chart: '0xf0571',
  ssid_chart_sharp: '0xf0479',
  ssid_chart_rounded: '0xf0386',
  ssid_chart_outlined: '0xf0667',
  stacked_bar_chart: '0xe5f6',
  stacked_bar_chart_sharp: '0xecec',
  stacked_bar_chart_rounded: '0xf01cb',
  stacked_bar_chart_outlined: '0xf3d9',
  stacked_line_chart: '0xe5f7',
  stacked_line_chart_sharp: '0xeced',
  stacked_line_chart_rounded: '0xf01cc',
  stacked_line_chart_outlined: '0xf3da',
  stadium: '0xf0572',
  stadium_sharp: '0xf047a',
  stadium_rounded: '0xf0387',
  stadium_outlined: '0xf0668',
  stairs: '0xe5f8',
  stairs_sharp: '0xecee',
  stairs_rounded: '0xf01cd',
  stairs_outlined: '0xf3db',
  star: '0xe5f9',
  star_sharp: '0xecf5',
  star_rounded: '0xf01d4',
  star_outlined: '0xf3e0',
  star_border: '0xe5fa',
  star_border_sharp: '0xecf0',
  star_border_rounded: '0xf01cf',
  star_border_outlined: '0xf3dc',
  star_border_purple500: '0xe5fb',
  star_border_purple500_sharp: '0xecef',
  star_border_purple500_rounded: '0xf01ce',
  star_border_purple500_outlined: '0xf3dd',
  star_half: '0xe5fc',
  star_half_sharp: '0xecf1',
  star_half_rounded: '0xf01d0',
  star_half_outlined: '0xf3de',
  star_outline: '0xe5fd',
  star_outline_sharp: '0xecf2',
  star_outline_rounded: '0xf01d1',
  star_outline_outlined: '0xf3df',
  star_purple500: '0xe5fe',
  star_purple500_sharp: '0xecf3',
  star_purple500_rounded: '0xf01d2',
  star_purple500_outlined: '0xf3e1',
  star_rate: '0xe5ff',
  star_rate_sharp: '0xecf4',
  star_rate_rounded: '0xf01d3',
  star_rate_outlined: '0xf3e2',
  stars: '0xe600',
  stars_sharp: '0xecf6',
  stars_rounded: '0xf01d5',
  stars_outlined: '0xf3e3',
  start: '0xf0573',
  start_sharp: '0xf047b',
  start_rounded: '0xf0388',
  start_outlined: '0xf0669',
  stay_current_landscape: '0xe601',
  stay_current_landscape_sharp: '0xecf7',
  stay_current_landscape_rounded: '0xf01d6',
  stay_current_landscape_outlined: '0xf3e4',
  stay_current_portrait: '0xe602',
  stay_current_portrait_sharp: '0xecf8',
  stay_current_portrait_rounded: '0xf01d7',
  stay_current_portrait_outlined: '0xf3e5',
  stay_primary_landscape: '0xe603',
  stay_primary_landscape_sharp: '0xecf9',
  stay_primary_landscape_rounded: '0xf01d8',
  stay_primary_landscape_outlined: '0xf3e6',
  stay_primary_portrait: '0xe604',
  stay_primary_portrait_sharp: '0xecfa',
  stay_primary_portrait_rounded: '0xf01d9',
  stay_primary_portrait_outlined: '0xf3e7',
  sticky_note_2: '0xe605',
  sticky_note_2_sharp: '0xecfb',
  sticky_note_2_rounded: '0xf01da',
  sticky_note_2_outlined: '0xf3e8',
  stop: '0xe606',
  stop_sharp: '0xecfe',
  stop_rounded: '0xf01dc',
  stop_outlined: '0xf3ea',
  stop_circle: '0xe607',
  stop_circle_sharp: '0xecfc',
  stop_circle_rounded: '0xf01db',
  stop_circle_outlined: '0xf3e9',
  stop_screen_share: '0xe608',
  stop_screen_share_sharp: '0xecfd',
  stop_screen_share_rounded: '0xf01dd',
  stop_screen_share_outlined: '0xf3eb',
  storage: '0xe609',
  storage_sharp: '0xecff',
  storage_rounded: '0xf01de',
  storage_outlined: '0xf3ec',
  store: '0xe60a',
  store_sharp: '0xed01',
  store_rounded: '0xf01e0',
  store_outlined: '0xf3ee',
  store_mall_directory: '0xe60b',
  store_mall_directory_sharp: '0xed00',
  store_mall_directory_rounded: '0xf01df',
  store_mall_directory_outlined: '0xf3ed',
  storefront: '0xe60c',
  storefront_sharp: '0xed02',
  storefront_rounded: '0xf01e1',
  storefront_outlined: '0xf3ef',
  storm: '0xe60d',
  storm_sharp: '0xed03',
  storm_rounded: '0xf01e2',
  storm_outlined: '0xf3f0',
  straight: '0xf0574',
  straight_sharp: '0xf047c',
  straight_rounded: '0xf0389',
  straight_outlined: '0xf066a',
  straighten: '0xe60e',
  straighten_sharp: '0xed04',
  straighten_rounded: '0xf01e3',
  straighten_outlined: '0xf3f1',
  stream: '0xe60f',
  stream_sharp: '0xed05',
  stream_rounded: '0xf01e4',
  stream_outlined: '0xf3f2',
  streetview: '0xe610',
  streetview_sharp: '0xed06',
  streetview_rounded: '0xf01e5',
  streetview_outlined: '0xf3f3',
  strikethrough_s: '0xe611',
  strikethrough_s_sharp: '0xed07',
  strikethrough_s_rounded: '0xf01e6',
  strikethrough_s_outlined: '0xf3f4',
  stroller: '0xe612',
  stroller_sharp: '0xed08',
  stroller_rounded: '0xf01e7',
  stroller_outlined: '0xf3f5',
  style: '0xe613',
  style_sharp: '0xed09',
  style_rounded: '0xf01e8',
  style_outlined: '0xf3f6',
  subdirectory_arrow_left: '0xe614',
  subdirectory_arrow_left_sharp: '0xed0a',
  subdirectory_arrow_left_rounded: '0xf01e9',
  subdirectory_arrow_left_outlined: '0xf3f7',
  subdirectory_arrow_right: '0xe615',
  subdirectory_arrow_right_sharp: '0xed0b',
  subdirectory_arrow_right_rounded: '0xf01ea',
  subdirectory_arrow_right_outlined: '0xf3f8',
  subject: '0xe616',
  subject_sharp: '0xed0c',
  subject_rounded: '0xf01eb',
  subject_outlined: '0xf3f9',
  subscript: '0xe617',
  subscript_sharp: '0xed0d',
  subscript_rounded: '0xf01ec',
  subscript_outlined: '0xf3fa',
  subscriptions: '0xe618',
  subscriptions_sharp: '0xed0e',
  subscriptions_rounded: '0xf01ed',
  subscriptions_outlined: '0xf3fb',
  subtitles: '0xe619',
  subtitles_sharp: '0xed10',
  subtitles_rounded: '0xf01ef',
  subtitles_outlined: '0xf3fd',
  subtitles_off: '0xe61a',
  subtitles_off_sharp: '0xed0f',
  subtitles_off_rounded: '0xf01ee',
  subtitles_off_outlined: '0xf3fc',
  subway: '0xe61b',
  subway_sharp: '0xed11',
  subway_rounded: '0xf01f0',
  subway_outlined: '0xf3fe',
  summarize: '0xe61c',
  summarize_sharp: '0xed12',
  summarize_rounded: '0xf01f1',
  summarize_outlined: '0xf3ff',
  sunny: '0xf0575',
  sunny_snowing: '0xf0576',
  superscript: '0xe61d',
  superscript_sharp: '0xed13',
  superscript_rounded: '0xf01f2',
  superscript_outlined: '0xf400',
  supervised_user_circle: '0xe61e',
  supervised_user_circle_sharp: '0xed14',
  supervised_user_circle_rounded: '0xf01f3',
  supervised_user_circle_outlined: '0xf401',
  supervisor_account: '0xe61f',
  supervisor_account_sharp: '0xed15',
  supervisor_account_rounded: '0xf01f4',
  supervisor_account_outlined: '0xf402',
  support: '0xe620',
  support_sharp: '0xed17',
  support_rounded: '0xf01f6',
  support_outlined: '0xf404',
  support_agent: '0xe621',
  support_agent_sharp: '0xed16',
  support_agent_rounded: '0xf01f5',
  support_agent_outlined: '0xf403',
  surfing: '0xe622',
  surfing_sharp: '0xed18',
  surfing_rounded: '0xf01f7',
  surfing_outlined: '0xf405',
  surround_sound: '0xe623',
  surround_sound_sharp: '0xed19',
  surround_sound_rounded: '0xf01f8',
  surround_sound_outlined: '0xf406',
  swap_calls: '0xe624',
  swap_calls_sharp: '0xed1a',
  swap_calls_rounded: '0xf01f9',
  swap_calls_outlined: '0xf407',
  swap_horiz: '0xe625',
  swap_horiz_sharp: '0xed1b',
  swap_horiz_rounded: '0xf01fa',
  swap_horiz_outlined: '0xf408',
  swap_horizontal_circle: '0xe626',
  swap_horizontal_circle_sharp: '0xed1c',
  swap_horizontal_circle_rounded: '0xf01fb',
  swap_horizontal_circle_outlined: '0xf409',
  swap_vert: '0xe627',
  swap_vert_sharp: '0xed1d',
  swap_vert_rounded: '0xf01fc',
  swap_vert_outlined: '0xf40a',
  swap_vert_circle: '0xe628',
  swap_vert_circle_sharp: '0xed1e',
  swap_vert_circle_rounded: '0xf01fd',
  swap_vert_circle_outlined: '0xf40b',
  swap_vertical_circle: '0xe628',
  swap_vertical_circle_sharp: '0xed1e',
  swap_vertical_circle_rounded: '0xf01fd',
  swap_vertical_circle_outlined: '0xf40b',
  swipe: '0xe629',
  swipe_sharp: '0xed1f',
  swipe_rounded: '0xf01fe',
  swipe_outlined: '0xf40c',
  swipe_down: '0xf0578',
  swipe_down_sharp: '0xf047e',
  swipe_down_rounded: '0xf038b',
  swipe_down_outlined: '0xf066c',
  swipe_down_alt: '0xf0577',
  swipe_down_alt_sharp: '0xf047d',
  swipe_down_alt_rounded: '0xf038a',
  swipe_down_alt_outlined: '0xf066b',
  swipe_left: '0xf057a',
  swipe_left_sharp: '0xf0480',
  swipe_left_rounded: '0xf038d',
  swipe_left_outlined: '0xf066e',
  swipe_left_alt: '0xf0579',
  swipe_left_alt_sharp: '0xf047f',
  swipe_left_alt_rounded: '0xf038c',
  swipe_left_alt_outlined: '0xf066d',
  swipe_right: '0xf057c',
  swipe_right_sharp: '0xf0482',
  swipe_right_rounded: '0xf038f',
  swipe_right_outlined: '0xf0670',
  swipe_right_alt: '0xf057b',
  swipe_right_alt_sharp: '0xf0481',
  swipe_right_alt_rounded: '0xf038e',
  swipe_right_alt_outlined: '0xf066f',
  swipe_up: '0xf057e',
  swipe_up_sharp: '0xf0484',
  swipe_up_rounded: '0xf0391',
  swipe_up_outlined: '0xf0672',
  swipe_up_alt: '0xf057d',
  swipe_up_alt_sharp: '0xf0483',
  swipe_up_alt_rounded: '0xf0390',
  swipe_up_alt_outlined: '0xf0671',
  swipe_vertical: '0xf057f',
  swipe_vertical_sharp: '0xf0485',
  swipe_vertical_rounded: '0xf0392',
  swipe_vertical_outlined: '0xf0673',
  switch_access_shortcut: '0xf0581',
  switch_access_shortcut_sharp: '0xf0487',
  switch_access_shortcut_rounded: '0xf0394',
  switch_access_shortcut_outlined: '0xf0675',
  switch_access_shortcut_add: '0xf0580',
  switch_access_shortcut_add_sharp: '0xf0486',
  switch_access_shortcut_add_rounded: '0xf0393',
  switch_access_shortcut_add_outlined: '0xf0674',
  switch_account: '0xe62a',
  switch_account_sharp: '0xed20',
  switch_account_rounded: '0xf01ff',
  switch_account_outlined: '0xf40d',
  switch_camera: '0xe62b',
  switch_camera_sharp: '0xed21',
  switch_camera_rounded: '0xf0200',
  switch_camera_outlined: '0xf40e',
  switch_left: '0xe62c',
  switch_left_sharp: '0xed22',
  switch_left_rounded: '0xf0201',
  switch_left_outlined: '0xf40f',
  switch_right: '0xe62d',
  switch_right_sharp: '0xed23',
  switch_right_rounded: '0xf0202',
  switch_right_outlined: '0xf410',
  switch_video: '0xe62e',
  switch_video_sharp: '0xed24',
  switch_video_rounded: '0xf0203',
  switch_video_outlined: '0xf411',
  synagogue: '0xf0582',
  synagogue_sharp: '0xf0488',
  synagogue_rounded: '0xf0395',
  synagogue_outlined: '0xf0676',
  sync: '0xe62f',
  sync_sharp: '0xed28',
  sync_rounded: '0xf0207',
  sync_outlined: '0xf414',
  sync_alt: '0xe630',
  sync_alt_sharp: '0xed25',
  sync_alt_rounded: '0xf0204',
  sync_alt_outlined: '0xf412',
  sync_disabled: '0xe631',
  sync_disabled_sharp: '0xed26',
  sync_disabled_rounded: '0xf0205',
  sync_disabled_outlined: '0xf413',
  sync_lock: '0xf0583',
  sync_lock_sharp: '0xf0489',
  sync_lock_rounded: '0xf0396',
  sync_lock_outlined: '0xf0677',
  sync_problem: '0xe632',
  sync_problem_sharp: '0xed27',
  sync_problem_rounded: '0xf0206',
  sync_problem_outlined: '0xf415',
  system_security_update: '0xe633',
  system_security_update_sharp: '0xed2a',
  system_security_update_rounded: '0xf0209',
  system_security_update_outlined: '0xf417',
  system_security_update_good: '0xe634',
  system_security_update_good_sharp: '0xed29',
  system_security_update_good_rounded: '0xf0208',
  system_security_update_good_outlined: '0xf416',
  system_security_update_warning: '0xe635',
  system_security_update_warning_sharp: '0xed2b',
  system_security_update_warning_rounded: '0xf020a',
  system_security_update_warning_outlined: '0xf418',
  system_update: '0xe636',
  system_update_sharp: '0xed2d',
  system_update_rounded: '0xf020c',
  system_update_outlined: '0xf41a',
  system_update_alt: '0xe637',
  system_update_alt_sharp: '0xed2c',
  system_update_alt_rounded: '0xf020b',
  system_update_alt_outlined: '0xf419',
  system_update_tv: '0xe637',
  system_update_tv_sharp: '0xed2c',
  system_update_tv_rounded: '0xf020b',
  system_update_tv_outlined: '0xf419',
  tab: '0xe638',
  tab_sharp: '0xed2e',
  tab_rounded: '0xf020d',
  tab_outlined: '0xf41b',
  tab_unselected: '0xe639',
  tab_unselected_sharp: '0xed2f',
  tab_unselected_rounded: '0xf020e',
  tab_unselected_outlined: '0xf41c',
  table_bar: '0xf0584',
  table_bar_sharp: '0xf048a',
  table_bar_rounded: '0xf0397',
  table_bar_outlined: '0xf0678',
  table_chart: '0xe63a',
  table_chart_sharp: '0xed30',
  table_chart_rounded: '0xf020f',
  table_chart_outlined: '0xf41d',
  table_restaurant: '0xf0585',
  table_restaurant_sharp: '0xf048b',
  table_restaurant_rounded: '0xf0398',
  table_restaurant_outlined: '0xf0679',
  table_rows: '0xe63b',
  table_rows_sharp: '0xed31',
  table_rows_rounded: '0xf0210',
  table_rows_outlined: '0xf41e',
  table_view: '0xe63c',
  table_view_sharp: '0xed32',
  table_view_rounded: '0xf0211',
  table_view_outlined: '0xf41f',
  tablet: '0xe63d',
  tablet_sharp: '0xed35',
  tablet_rounded: '0xf0214',
  tablet_outlined: '0xf422',
  tablet_android: '0xe63e',
  tablet_android_sharp: '0xed33',
  tablet_android_rounded: '0xf0212',
  tablet_android_outlined: '0xf420',
  tablet_mac: '0xe63f',
  tablet_mac_sharp: '0xed34',
  tablet_mac_rounded: '0xf0213',
  tablet_mac_outlined: '0xf421',
  tag: '0xe640',
  tag_sharp: '0xed37',
  tag_rounded: '0xf0216',
  tag_outlined: '0xf424',
  tag_faces: '0xe641',
  tag_faces_sharp: '0xed36',
  tag_faces_rounded: '0xf0215',
  tag_faces_outlined: '0xf423',
  takeout_dining: '0xe642',
  takeout_dining_sharp: '0xed38',
  takeout_dining_rounded: '0xf0217',
  takeout_dining_outlined: '0xf425',
  tap_and_play: '0xe643',
  tap_and_play_sharp: '0xed39',
  tap_and_play_rounded: '0xf0218',
  tap_and_play_outlined: '0xf426',
  tapas: '0xe644',
  tapas_sharp: '0xed3a',
  tapas_rounded: '0xf0219',
  tapas_outlined: '0xf427',
  task: '0xe645',
  task_sharp: '0xed3c',
  task_rounded: '0xf021b',
  task_outlined: '0xf429',
  task_alt: '0xe646',
  task_alt_sharp: '0xed3b',
  task_alt_rounded: '0xf021a',
  task_alt_outlined: '0xf428',
  taxi_alert: '0xe647',
  taxi_alert_sharp: '0xed3d',
  taxi_alert_rounded: '0xf021c',
  taxi_alert_outlined: '0xf42a',
  telegram: '0xf0586',
  telegram_sharp: '0xf048c',
  telegram_rounded: '0xf0399',
  telegram_outlined: '0xf067a',
  temple_buddhist: '0xf0587',
  temple_buddhist_sharp: '0xf048d',
  temple_buddhist_rounded: '0xf039a',
  temple_buddhist_outlined: '0xf067b',
  temple_hindu: '0xf0588',
  temple_hindu_sharp: '0xf048e',
  temple_hindu_rounded: '0xf039b',
  temple_hindu_outlined: '0xf067c',
  terminal: '0xf0589',
  terminal_sharp: '0xf048f',
  terminal_rounded: '0xf039c',
  terminal_outlined: '0xf067d',
  terrain: '0xe648',
  terrain_sharp: '0xed3e',
  terrain_rounded: '0xf021d',
  terrain_outlined: '0xf42b',
  text_decrease: '0xf058a',
  text_decrease_sharp: '0xf0490',
  text_decrease_rounded: '0xf039d',
  text_decrease_outlined: '0xf067e',
  text_fields: '0xe649',
  text_fields_sharp: '0xed3f',
  text_fields_rounded: '0xf021e',
  text_fields_outlined: '0xf42c',
  text_format: '0xe64a',
  text_format_sharp: '0xed40',
  text_format_rounded: '0xf021f',
  text_format_outlined: '0xf42d',
  text_increase: '0xf058b',
  text_increase_sharp: '0xf0491',
  text_increase_rounded: '0xf039e',
  text_increase_outlined: '0xf067f',
  text_rotate_up: '0xe64b',
  text_rotate_up_sharp: '0xed41',
  text_rotate_up_rounded: '0xf0220',
  text_rotate_up_outlined: '0xf42e',
  text_rotate_vertical: '0xe64c',
  text_rotate_vertical_sharp: '0xed42',
  text_rotate_vertical_rounded: '0xf0221',
  text_rotate_vertical_outlined: '0xf42f',
  text_rotation_angledown: '0xe64d',
  text_rotation_angledown_sharp: '0xed43',
  text_rotation_angledown_rounded: '0xf0222',
  text_rotation_angledown_outlined: '0xf430',
  text_rotation_angleup: '0xe64e',
  text_rotation_angleup_sharp: '0xed44',
  text_rotation_angleup_rounded: '0xf0223',
  text_rotation_angleup_outlined: '0xf431',
  text_rotation_down: '0xe64f',
  text_rotation_down_sharp: '0xed45',
  text_rotation_down_rounded: '0xf0224',
  text_rotation_down_outlined: '0xf432',
  text_rotation_none: '0xe650',
  text_rotation_none_sharp: '0xed46',
  text_rotation_none_rounded: '0xf0225',
  text_rotation_none_outlined: '0xf433',
  text_snippet: '0xe651',
  text_snippet_sharp: '0xed47',
  text_snippet_rounded: '0xf0226',
  text_snippet_outlined: '0xf434',
  textsms: '0xe652',
  textsms_sharp: '0xed48',
  textsms_rounded: '0xf0227',
  textsms_outlined: '0xf435',
  texture: '0xe653',
  texture_sharp: '0xed49',
  texture_rounded: '0xf0228',
  texture_outlined: '0xf436',
  theater_comedy: '0xe654',
  theater_comedy_sharp: '0xed4a',
  theater_comedy_rounded: '0xf0229',
  theater_comedy_outlined: '0xf437',
  theaters: '0xe655',
  theaters_sharp: '0xed4b',
  theaters_rounded: '0xf022a',
  theaters_outlined: '0xf438',
  thermostat: '0xe656',
  thermostat_sharp: '0xed4d',
  thermostat_rounded: '0xf022c',
  thermostat_outlined: '0xf43a',
  thermostat_auto: '0xe657',
  thermostat_auto_sharp: '0xed4c',
  thermostat_auto_rounded: '0xf022b',
  thermostat_auto_outlined: '0xf439',
  thumb_down: '0xe658',
  thumb_down_sharp: '0xed50',
  thumb_down_rounded: '0xf022f',
  thumb_down_outlined: '0xf43d',
  thumb_down_alt: '0xe659',
  thumb_down_alt_sharp: '0xed4e',
  thumb_down_alt_rounded: '0xf022d',
  thumb_down_alt_outlined: '0xf43b',
  thumb_down_off_alt: '0xe65a',
  thumb_down_off_alt_sharp: '0xed4f',
  thumb_down_off_alt_rounded: '0xf022e',
  thumb_down_off_alt_outlined: '0xf43c',
  thumb_up: '0xe65b',
  thumb_up_sharp: '0xed53',
  thumb_up_rounded: '0xf0232',
  thumb_up_outlined: '0xf440',
  thumb_up_alt: '0xe65c',
  thumb_up_alt_sharp: '0xed51',
  thumb_up_alt_rounded: '0xf0230',
  thumb_up_alt_outlined: '0xf43e',
  thumb_up_off_alt: '0xe65d',
  thumb_up_off_alt_sharp: '0xed52',
  thumb_up_off_alt_rounded: '0xf0231',
  thumb_up_off_alt_outlined: '0xf43f',
  thumbs_up_down: '0xe65e',
  thumbs_up_down_sharp: '0xed54',
  thumbs_up_down_rounded: '0xf0233',
  thumbs_up_down_outlined: '0xf441',
  thunderstorm: '0xf07cb',
  thunderstorm_sharp: '0xf0773',
  thunderstorm_rounded: '0xf0823',
  thunderstorm_outlined: '0xf071b',
  tiktok: '0xf058c',
  tiktok_sharp: '0xf0492',
  tiktok_rounded: '0xf039f',
  tiktok_outlined: '0xf0680',
  time_to_leave: '0xe65f',
  time_to_leave_sharp: '0xed55',
  time_to_leave_rounded: '0xf0234',
  time_to_leave_outlined: '0xf442',
  timelapse: '0xe660',
  timelapse_sharp: '0xed56',
  timelapse_rounded: '0xf0235',
  timelapse_outlined: '0xf443',
  timeline: '0xe661',
  timeline_sharp: '0xed57',
  timeline_rounded: '0xf0236',
  timeline_outlined: '0xf444',
  timer: '0xe662',
  timer_sharp: '0xed5d',
  timer_rounded: '0xf023c',
  timer_outlined: '0xf44a',
  timer_10: '0xe663',
  timer_10_sharp: '0xed59',
  timer_10_rounded: '0xf0237',
  timer_10_outlined: '0xf445',
  timer_10_select: '0xe664',
  timer_10_select_sharp: '0xed58',
  timer_10_select_rounded: '0xf0238',
  timer_10_select_outlined: '0xf446',
  timer_3: '0xe665',
  timer_3_sharp: '0xed5b',
  timer_3_rounded: '0xf0239',
  timer_3_outlined: '0xf447',
  timer_3_select: '0xe666',
  timer_3_select_sharp: '0xed5a',
  timer_3_select_rounded: '0xf023a',
  timer_3_select_outlined: '0xf448',
  timer_off: '0xe667',
  timer_off_sharp: '0xed5c',
  timer_off_rounded: '0xf023b',
  timer_off_outlined: '0xf449',
  tips_and_updates: '0xf058d',
  tips_and_updates_sharp: '0xf0493',
  tips_and_updates_rounded: '0xf03a0',
  tips_and_updates_outlined: '0xf0681',
  tire_repair: '0xf06c4',
  tire_repair_sharp: '0xf06b7',
  tire_repair_rounded: '0xf06d1',
  tire_repair_outlined: '0xf06aa',
  title: '0xe668',
  title_sharp: '0xed5e',
  title_rounded: '0xf023d',
  title_outlined: '0xf44b',
  toc: '0xe669',
  toc_sharp: '0xed5f',
  toc_rounded: '0xf023e',
  toc_outlined: '0xf44c',
  today: '0xe66a',
  today_sharp: '0xed60',
  today_rounded: '0xf023f',
  today_outlined: '0xf44d',
  toggle_off: '0xe66b',
  toggle_off_sharp: '0xed61',
  toggle_off_rounded: '0xf0240',
  toggle_off_outlined: '0xf44e',
  toggle_on: '0xe66c',
  toggle_on_sharp: '0xed62',
  toggle_on_rounded: '0xf0241',
  toggle_on_outlined: '0xf44f',
  token: '0xf058e',
  token_sharp: '0xf0494',
  token_rounded: '0xf03a1',
  token_outlined: '0xf0682',
  toll: '0xe66d',
  toll_sharp: '0xed63',
  toll_rounded: '0xf0242',
  toll_outlined: '0xf450',
  tonality: '0xe66e',
  tonality_sharp: '0xed64',
  tonality_rounded: '0xf0243',
  tonality_outlined: '0xf451',
  topic: '0xe66f',
  topic_sharp: '0xed65',
  topic_rounded: '0xf0244',
  topic_outlined: '0xf452',
  tornado: '0xf07cc',
  tornado_sharp: '0xf0774',
  tornado_rounded: '0xf0824',
  tornado_outlined: '0xf071c',
  touch_app: '0xe670',
  touch_app_sharp: '0xed66',
  touch_app_rounded: '0xf0245',
  touch_app_outlined: '0xf453',
  tour: '0xe671',
  tour_sharp: '0xed67',
  tour_rounded: '0xf0246',
  tour_outlined: '0xf454',
  toys: '0xe672',
  toys_sharp: '0xed68',
  toys_rounded: '0xf0247',
  toys_outlined: '0xf455',
  track_changes: '0xe673',
  track_changes_sharp: '0xed69',
  track_changes_rounded: '0xf0248',
  track_changes_outlined: '0xf456',
  traffic: '0xe674',
  traffic_sharp: '0xed6a',
  traffic_rounded: '0xf0249',
  traffic_outlined: '0xf457',
  train: '0xe675',
  train_sharp: '0xed6b',
  train_rounded: '0xf024a',
  train_outlined: '0xf458',
  tram: '0xe676',
  tram_sharp: '0xed6c',
  tram_rounded: '0xf024b',
  tram_outlined: '0xf459',
  transcribe: '0xf07cd',
  transcribe_sharp: '0xf0775',
  transcribe_rounded: '0xf0825',
  transcribe_outlined: '0xf071d',
  transfer_within_a_station: '0xe677',
  transfer_within_a_station_sharp: '0xed6d',
  transfer_within_a_station_rounded: '0xf024c',
  transfer_within_a_station_outlined: '0xf45a',
  transform: '0xe678',
  transform_sharp: '0xed6e',
  transform_rounded: '0xf024d',
  transform_outlined: '0xf45b',
  transgender: '0xe679',
  transgender_sharp: '0xed6f',
  transgender_rounded: '0xf024e',
  transgender_outlined: '0xf45c',
  transit_enterexit: '0xe67a',
  transit_enterexit_sharp: '0xed70',
  transit_enterexit_rounded: '0xf024f',
  transit_enterexit_outlined: '0xf45d',
  translate: '0xe67b',
  translate_sharp: '0xed71',
  translate_rounded: '0xf0250',
  translate_outlined: '0xf45e',
  travel_explore: '0xe67c',
  travel_explore_sharp: '0xed72',
  travel_explore_rounded: '0xf0251',
  travel_explore_outlined: '0xf45f',
  trending_down: '0xe67d',
  trending_down_sharp: '0xed73',
  trending_down_rounded: '0xf0252',
  trending_down_outlined: '0xf460',
  trending_flat: '0xe67e',
  trending_flat_sharp: '0xed74',
  trending_flat_rounded: '0xf0253',
  trending_flat_outlined: '0xf461',
  trending_neutral: '0xe67e',
  trending_neutral_sharp: '0xed74',
  trending_neutral_rounded: '0xf0253',
  trending_neutral_outlined: '0xf461',
  trending_up: '0xe67f',
  trending_up_sharp: '0xed75',
  trending_up_rounded: '0xf0254',
  trending_up_outlined: '0xf462',
  trip_origin: '0xe680',
  trip_origin_sharp: '0xed76',
  trip_origin_rounded: '0xf0255',
  trip_origin_outlined: '0xf463',
  trolley: '0xf0878',
  troubleshoot: '0xf07ce',
  troubleshoot_sharp: '0xf0776',
  troubleshoot_rounded: '0xf0826',
  troubleshoot_outlined: '0xf071e',
  try_sms_star: '0xe681',
  try_sms_star_sharp: '0xed77',
  try_sms_star_rounded: '0xf0256',
  try_sms_star_outlined: '0xf464',
  tsunami: '0xf07cf',
  tsunami_sharp: '0xf0777',
  tsunami_rounded: '0xf0827',
  tsunami_outlined: '0xf071f',
  tty: '0xe682',
  tty_sharp: '0xed78',
  tty_rounded: '0xf0257',
  tty_outlined: '0xf465',
  tune: '0xe683',
  tune_sharp: '0xed79',
  tune_rounded: '0xf0258',
  tune_outlined: '0xf466',
  tungsten: '0xe684',
  tungsten_sharp: '0xed7a',
  tungsten_rounded: '0xf0259',
  tungsten_outlined: '0xf467',
  turn_left: '0xf058f',
  turn_left_sharp: '0xf0495',
  turn_left_rounded: '0xf03a2',
  turn_left_outlined: '0xf0683',
  turn_right: '0xf0590',
  turn_right_sharp: '0xf0496',
  turn_right_rounded: '0xf03a3',
  turn_right_outlined: '0xf0684',
  turn_sharp_left: '0xf0591',
  turn_sharp_left_sharp: '0xf0497',
  turn_sharp_left_rounded: '0xf03a4',
  turn_sharp_left_outlined: '0xf0685',
  turn_sharp_right: '0xf0592',
  turn_sharp_right_sharp: '0xf0498',
  turn_sharp_right_rounded: '0xf03a5',
  turn_sharp_right_outlined: '0xf0686',
  turn_slight_left: '0xf0593',
  turn_slight_left_sharp: '0xf0499',
  turn_slight_left_rounded: '0xf03a6',
  turn_slight_left_outlined: '0xf0687',
  turn_slight_right: '0xf0594',
  turn_slight_right_sharp: '0xf049a',
  turn_slight_right_rounded: '0xf03a7',
  turn_slight_right_outlined: '0xf0688',
  turned_in: '0xe685',
  turned_in_sharp: '0xed7c',
  turned_in_rounded: '0xf025b',
  turned_in_outlined: '0xf469',
  turned_in_not: '0xe686',
  turned_in_not_sharp: '0xed7b',
  turned_in_not_rounded: '0xf025a',
  turned_in_not_outlined: '0xf468',
  tv: '0xe687',
  tv_sharp: '0xed7e',
  tv_rounded: '0xf025d',
  tv_outlined: '0xf46b',
  tv_off: '0xe688',
  tv_off_sharp: '0xed7d',
  tv_off_rounded: '0xf025c',
  tv_off_outlined: '0xf46a',
  two_wheeler: '0xe689',
  two_wheeler_sharp: '0xed7f',
  two_wheeler_rounded: '0xf025e',
  two_wheeler_outlined: '0xf46c',
  type_specimen: '0xf07d0',
  type_specimen_sharp: '0xf0778',
  type_specimen_rounded: '0xf0828',
  type_specimen_outlined: '0xf0720',
  u_turn_left: '0xf0595',
  u_turn_left_sharp: '0xf049b',
  u_turn_left_rounded: '0xf03a8',
  u_turn_left_outlined: '0xf0689',
  u_turn_right: '0xf0596',
  u_turn_right_sharp: '0xf049c',
  u_turn_right_rounded: '0xf03a9',
  u_turn_right_outlined: '0xf068a',
  umbrella: '0xe68a',
  umbrella_sharp: '0xed80',
  umbrella_rounded: '0xf025f',
  umbrella_outlined: '0xf46d',
  unarchive: '0xe68b',
  unarchive_sharp: '0xed81',
  unarchive_rounded: '0xf0260',
  unarchive_outlined: '0xf46e',
  undo: '0xe68c',
  undo_sharp: '0xed82',
  undo_rounded: '0xf0261',
  undo_outlined: '0xf46f',
  unfold_less: '0xe68d',
  unfold_less_sharp: '0xed83',
  unfold_less_rounded: '0xf0262',
  unfold_less_outlined: '0xf470',
  unfold_less_double: '0xf0879',
  unfold_less_double_sharp: '0xf084d',
  unfold_less_double_rounded: '0xf0896',
  unfold_less_double_outlined: '0xf08b4',
  unfold_more: '0xe68e',
  unfold_more_sharp: '0xed84',
  unfold_more_rounded: '0xf0263',
  unfold_more_outlined: '0xf471',
  unfold_more_double: '0xf087a',
  unfold_more_double_sharp: '0xf084e',
  unfold_more_double_rounded: '0xf0897',
  unfold_more_double_outlined: '0xf08b5',
  unpublished: '0xe68f',
  unpublished_sharp: '0xed85',
  unpublished_rounded: '0xf0264',
  unpublished_outlined: '0xf472',
  unsubscribe: '0xe690',
  unsubscribe_sharp: '0xed86',
  unsubscribe_rounded: '0xf0265',
  unsubscribe_outlined: '0xf473',
  upcoming: '0xe691',
  upcoming_sharp: '0xed87',
  upcoming_rounded: '0xf0266',
  upcoming_outlined: '0xf474',
  update: '0xe692',
  update_sharp: '0xed89',
  update_rounded: '0xf0268',
  update_outlined: '0xf476',
  update_disabled: '0xe693',
  update_disabled_sharp: '0xed88',
  update_disabled_rounded: '0xf0267',
  update_disabled_outlined: '0xf475',
  upgrade: '0xe694',
  upgrade_sharp: '0xed8a',
  upgrade_rounded: '0xf0269',
  upgrade_outlined: '0xf477',
  upload: '0xe695',
  upload_sharp: '0xed8c',
  upload_rounded: '0xf026b',
  upload_outlined: '0xf479',
  upload_file: '0xe696',
  upload_file_sharp: '0xed8b',
  upload_file_rounded: '0xf026a',
  upload_file_outlined: '0xf478',
  usb: '0xe697',
  usb_sharp: '0xed8e',
  usb_rounded: '0xf026d',
  usb_outlined: '0xf47b',
  usb_off: '0xe698',
  usb_off_sharp: '0xed8d',
  usb_off_rounded: '0xf026c',
  usb_off_outlined: '0xf47a',
  vaccines: '0xf0597',
  vaccines_sharp: '0xf049d',
  vaccines_rounded: '0xf03aa',
  vaccines_outlined: '0xf068b',
  vape_free: '0xf06c5',
  vape_free_sharp: '0xf06b8',
  vape_free_rounded: '0xf06d2',
  vape_free_outlined: '0xf06ab',
  vaping_rooms: '0xf06c6',
  vaping_rooms_sharp: '0xf06b9',
  vaping_rooms_rounded: '0xf06d3',
  vaping_rooms_outlined: '0xf06ac',
  verified: '0xe699',
  verified_sharp: '0xed8f',
  verified_rounded: '0xf026e',
  verified_outlined: '0xf47c',
  verified_user: '0xe69a',
  verified_user_sharp: '0xed90',
  verified_user_rounded: '0xf026f',
  verified_user_outlined: '0xf47d',
  vertical_align_bottom: '0xe69b',
  vertical_align_bottom_sharp: '0xed91',
  vertical_align_bottom_rounded: '0xf0270',
  vertical_align_bottom_outlined: '0xf47e',
  vertical_align_center: '0xe69c',
  vertical_align_center_sharp: '0xed92',
  vertical_align_center_rounded: '0xf0271',
  vertical_align_center_outlined: '0xf47f',
  vertical_align_top: '0xe69d',
  vertical_align_top_sharp: '0xed93',
  vertical_align_top_rounded: '0xf0272',
  vertical_align_top_outlined: '0xf480',
  vertical_distribute: '0xe69e',
  vertical_distribute_sharp: '0xed94',
  vertical_distribute_rounded: '0xf0273',
  vertical_distribute_outlined: '0xf481',
  vertical_shades: '0xf07d1',
  vertical_shades_sharp: '0xf077a',
  vertical_shades_rounded: '0xf082a',
  vertical_shades_outlined: '0xf0722',
  vertical_shades_closed: '0xf07d2',
  vertical_shades_closed_sharp: '0xf0779',
  vertical_shades_closed_rounded: '0xf0829',
  vertical_shades_closed_outlined: '0xf0721',
  vertical_split: '0xe69f',
  vertical_split_sharp: '0xed95',
  vertical_split_rounded: '0xf0274',
  vertical_split_outlined: '0xf482',
  vibration: '0xe6a0',
  vibration_sharp: '0xed96',
  vibration_rounded: '0xf0275',
  vibration_outlined: '0xf483',
  video_call: '0xe6a1',
  video_call_sharp: '0xed97',
  video_call_rounded: '0xf0276',
  video_call_outlined: '0xf484',
  video_camera_back: '0xe6a2',
  video_camera_back_sharp: '0xed98',
  video_camera_back_rounded: '0xf0277',
  video_camera_back_outlined: '0xf485',
  video_camera_front: '0xe6a3',
  video_camera_front_sharp: '0xed99',
  video_camera_front_rounded: '0xf0278',
  video_camera_front_outlined: '0xf486',
  video_chat: '0xf087b',
  video_chat_sharp: '0xf084f',
  video_chat_rounded: '0xf0898',
  video_chat_outlined: '0xf08b6',
  video_collection: '0xe6a5',
  video_collection_sharp: '0xed9b',
  video_collection_rounded: '0xf027a',
  video_collection_outlined: '0xf488',
  video_file: '0xf0598',
  video_file_sharp: '0xf049e',
  video_file_rounded: '0xf03ab',
  video_file_outlined: '0xf068c',
  video_label: '0xe6a4',
  video_label_sharp: '0xed9a',
  video_label_rounded: '0xf0279',
  video_label_outlined: '0xf487',
  video_library: '0xe6a5',
  video_library_sharp: '0xed9b',
  video_library_rounded: '0xf027a',
  video_library_outlined: '0xf488',
  video_settings: '0xe6a6',
  video_settings_sharp: '0xed9c',
  video_settings_rounded: '0xf027b',
  video_settings_outlined: '0xf489',
  video_stable: '0xe6a7',
  video_stable_sharp: '0xed9d',
  video_stable_rounded: '0xf027c',
  video_stable_outlined: '0xf48a',
  videocam: '0xe6a8',
  videocam_sharp: '0xed9f',
  videocam_rounded: '0xf027e',
  videocam_outlined: '0xf48c',
  videocam_off: '0xe6a9',
  videocam_off_sharp: '0xed9e',
  videocam_off_rounded: '0xf027d',
  videocam_off_outlined: '0xf48b',
  videogame_asset: '0xe6aa',
  videogame_asset_sharp: '0xeda1',
  videogame_asset_rounded: '0xf0280',
  videogame_asset_outlined: '0xf48e',
  videogame_asset_off: '0xe6ab',
  videogame_asset_off_sharp: '0xeda0',
  videogame_asset_off_rounded: '0xf027f',
  videogame_asset_off_outlined: '0xf48d',
  view_agenda: '0xe6ac',
  view_agenda_sharp: '0xeda2',
  view_agenda_rounded: '0xf0281',
  view_agenda_outlined: '0xf48f',
  view_array: '0xe6ad',
  view_array_sharp: '0xeda3',
  view_array_rounded: '0xf0282',
  view_array_outlined: '0xf490',
  view_carousel: '0xe6ae',
  view_carousel_sharp: '0xeda4',
  view_carousel_rounded: '0xf0283',
  view_carousel_outlined: '0xf491',
  view_column: '0xe6af',
  view_column_sharp: '0xeda5',
  view_column_rounded: '0xf0284',
  view_column_outlined: '0xf492',
  view_comfortable: '0xe6b0',
  view_comfortable_sharp: '0xeda6',
  view_comfortable_rounded: '0xf0285',
  view_comfortable_outlined: '0xf493',
  view_comfy: '0xe6b0',
  view_comfy_sharp: '0xeda6',
  view_comfy_rounded: '0xf0285',
  view_comfy_outlined: '0xf493',
  view_comfy_alt: '0xf0599',
  view_comfy_alt_sharp: '0xf049f',
  view_comfy_alt_rounded: '0xf03ac',
  view_comfy_alt_outlined: '0xf068d',
  view_compact: '0xe6b1',
  view_compact_sharp: '0xeda7',
  view_compact_rounded: '0xf0286',
  view_compact_outlined: '0xf494',
  view_compact_alt: '0xf059a',
  view_compact_alt_sharp: '0xf04a0',
  view_compact_alt_rounded: '0xf03ad',
  view_compact_alt_outlined: '0xf068e',
  view_cozy: '0xf059b',
  view_cozy_sharp: '0xf04a1',
  view_cozy_rounded: '0xf03ae',
  view_cozy_outlined: '0xf068f',
  view_day: '0xe6b2',
  view_day_sharp: '0xeda8',
  view_day_rounded: '0xf0287',
  view_day_outlined: '0xf495',
  view_headline: '0xe6b3',
  view_headline_sharp: '0xeda9',
  view_headline_rounded: '0xf0288',
  view_headline_outlined: '0xf496',
  view_in_ar: '0xe6b4',
  view_in_ar_sharp: '0xedaa',
  view_in_ar_rounded: '0xf0289',
  view_in_ar_outlined: '0xf497',
  view_kanban: '0xf059c',
  view_kanban_sharp: '0xf04a2',
  view_kanban_rounded: '0xf03af',
  view_kanban_outlined: '0xf0690',
  view_list: '0xe6b5',
  view_list_sharp: '0xedab',
  view_list_rounded: '0xf028a',
  view_list_outlined: '0xf498',
  view_module: '0xe6b6',
  view_module_sharp: '0xedac',
  view_module_rounded: '0xf028b',
  view_module_outlined: '0xf499',
  view_quilt: '0xe6b7',
  view_quilt_sharp: '0xedad',
  view_quilt_rounded: '0xf028c',
  view_quilt_outlined: '0xf49a',
  view_sidebar: '0xe6b8',
  view_sidebar_sharp: '0xedae',
  view_sidebar_rounded: '0xf028d',
  view_sidebar_outlined: '0xf49b',
  view_stream: '0xe6b9',
  view_stream_sharp: '0xedaf',
  view_stream_rounded: '0xf028e',
  view_stream_outlined: '0xf49c',
  view_timeline: '0xf059d',
  view_timeline_sharp: '0xf04a3',
  view_timeline_rounded: '0xf03b0',
  view_timeline_outlined: '0xf0691',
  view_week: '0xe6ba',
  view_week_sharp: '0xedb0',
  view_week_rounded: '0xf028f',
  view_week_outlined: '0xf49d',
  vignette: '0xe6bb',
  vignette_sharp: '0xedb1',
  vignette_rounded: '0xf0290',
  vignette_outlined: '0xf49e',
  villa: '0xe6bc',
  villa_sharp: '0xedb2',
  villa_rounded: '0xf0291',
  villa_outlined: '0xf49f',
  visibility: '0xe6bd',
  visibility_sharp: '0xedb4',
  visibility_rounded: '0xf0293',
  visibility_outlined: '0xf4a1',
  visibility_off: '0xe6be',
  visibility_off_sharp: '0xedb3',
  visibility_off_rounded: '0xf0292',
  visibility_off_outlined: '0xf4a0',
  voice_chat: '0xe6bf',
  voice_chat_sharp: '0xedb5',
  voice_chat_rounded: '0xf0294',
  voice_chat_outlined: '0xf4a2',
  voice_over_off: '0xe6c0',
  voice_over_off_sharp: '0xedb6',
  voice_over_off_rounded: '0xf0295',
  voice_over_off_outlined: '0xf4a3',
  voicemail: '0xe6c1',
  voicemail_sharp: '0xedb7',
  voicemail_rounded: '0xf0296',
  voicemail_outlined: '0xf4a4',
  volcano: '0xf07d3',
  volcano_sharp: '0xf077b',
  volcano_rounded: '0xf082b',
  volcano_outlined: '0xf0723',
  volume_down: '0xe6c2',
  volume_down_sharp: '0xedb8',
  volume_down_rounded: '0xf0297',
  volume_down_outlined: '0xf4a5',
  volume_down_alt: '0xf059e',
  volume_mute: '0xe6c3',
  volume_mute_sharp: '0xedb9',
  volume_mute_rounded: '0xf0298',
  volume_mute_outlined: '0xf4a6',
  volume_off: '0xe6c4',
  volume_off_sharp: '0xedba',
  volume_off_rounded: '0xf0299',
  volume_off_outlined: '0xf4a7',
  volume_up: '0xe6c5',
  volume_up_sharp: '0xedbb',
  volume_up_rounded: '0xf029a',
  volume_up_outlined: '0xf4a8',
  volunteer_activism: '0xe6c6',
  volunteer_activism_sharp: '0xedbc',
  volunteer_activism_rounded: '0xf029b',
  volunteer_activism_outlined: '0xf4a9',
  vpn_key: '0xe6c7',
  vpn_key_sharp: '0xedbd',
  vpn_key_rounded: '0xf029c',
  vpn_key_outlined: '0xf4aa',
  vpn_key_off: '0xf059f',
  vpn_key_off_sharp: '0xf04a4',
  vpn_key_off_rounded: '0xf03b1',
  vpn_key_off_outlined: '0xf0692',
  vpn_lock: '0xe6c8',
  vpn_lock_sharp: '0xedbe',
  vpn_lock_rounded: '0xf029d',
  vpn_lock_outlined: '0xf4ab',
  vrpano: '0xe6c9',
  vrpano_sharp: '0xedbf',
  vrpano_rounded: '0xf029e',
  vrpano_outlined: '0xf4ac',
  wallet: '0xf07d4',
  wallet_sharp: '0xf077c',
  wallet_rounded: '0xf082c',
  wallet_outlined: '0xf0724',
  wallet_giftcard: '0xe13e',
  wallet_giftcard_sharp: '0xe83b',
  wallet_giftcard_rounded: '0xf61a',
  wallet_giftcard_outlined: '0xef2d',
  wallet_membership: '0xe13f',
  wallet_membership_sharp: '0xe83c',
  wallet_membership_rounded: '0xf61b',
  wallet_membership_outlined: '0xef2e',
  wallet_travel: '0xe140',
  wallet_travel_sharp: '0xe83d',
  wallet_travel_rounded: '0xf61c',
  wallet_travel_outlined: '0xef2f',
  wallpaper: '0xe6ca',
  wallpaper_sharp: '0xedc0',
  wallpaper_rounded: '0xf029f',
  wallpaper_outlined: '0xf4ad',
  warehouse: '0xf05a0',
  warehouse_sharp: '0xf04a5',
  warehouse_rounded: '0xf03b2',
  warehouse_outlined: '0xf0693',
  warning: '0xe6cb',
  warning_sharp: '0xedc2',
  warning_rounded: '0xf02a1',
  warning_outlined: '0xf4af',
  warning_amber: '0xe6cc',
  warning_amber_sharp: '0xedc1',
  warning_amber_rounded: '0xf02a0',
  warning_amber_outlined: '0xf4ae',
  wash: '0xe6cd',
  wash_sharp: '0xedc3',
  wash_rounded: '0xf02a2',
  wash_outlined: '0xf4b0',
  watch: '0xe6ce',
  watch_sharp: '0xedc5',
  watch_rounded: '0xf02a4',
  watch_outlined: '0xf4b2',
  watch_later: '0xe6cf',
  watch_later_sharp: '0xedc4',
  watch_later_rounded: '0xf02a3',
  watch_later_outlined: '0xf4b1',
  watch_off: '0xf05a1',
  watch_off_sharp: '0xf04a6',
  watch_off_rounded: '0xf03b3',
  watch_off_outlined: '0xf0694',
  water: '0xe6d0',
  water_sharp: '0xedc7',
  water_rounded: '0xf02a6',
  water_outlined: '0xf4b4',
  water_damage: '0xe6d1',
  water_damage_sharp: '0xedc6',
  water_damage_rounded: '0xf02a5',
  water_damage_outlined: '0xf4b3',
  water_drop: '0xf05a2',
  water_drop_sharp: '0xf04a7',
  water_drop_rounded: '0xf03b4',
  water_drop_outlined: '0xf0695',
  waterfall_chart: '0xe6d2',
  waterfall_chart_sharp: '0xedc8',
  waterfall_chart_rounded: '0xf02a7',
  waterfall_chart_outlined: '0xf4b5',
  waves: '0xe6d3',
  waves_sharp: '0xedc9',
  waves_rounded: '0xf02a8',
  waves_outlined: '0xf4b6',
  waving_hand: '0xf05a3',
  waving_hand_sharp: '0xf04a8',
  waving_hand_rounded: '0xf03b5',
  waving_hand_outlined: '0xf0696',
  wb_auto: '0xe6d4',
  wb_auto_sharp: '0xedca',
  wb_auto_rounded: '0xf02a9',
  wb_auto_outlined: '0xf4b7',
  wb_cloudy: '0xe6d5',
  wb_cloudy_sharp: '0xedcb',
  wb_cloudy_rounded: '0xf02aa',
  wb_cloudy_outlined: '0xf4b8',
  wb_incandescent: '0xe6d6',
  wb_incandescent_sharp: '0xedcc',
  wb_incandescent_rounded: '0xf02ab',
  wb_incandescent_outlined: '0xf4b9',
  wb_iridescent: '0xe6d7',
  wb_iridescent_sharp: '0xedcd',
  wb_iridescent_rounded: '0xf02ac',
  wb_iridescent_outlined: '0xf4ba',
  wb_shade: '0xe6d8',
  wb_shade_sharp: '0xedce',
  wb_shade_rounded: '0xf02ad',
  wb_shade_outlined: '0xf4bb',
  wb_sunny: '0xe6d9',
  wb_sunny_sharp: '0xedcf',
  wb_sunny_rounded: '0xf02ae',
  wb_sunny_outlined: '0xf4bc',
  wb_twighlight: '0xe6da',
  wb_twilight: '0xe6db',
  wb_twilight_sharp: '0xedd0',
  wb_twilight_rounded: '0xf02af',
  wb_twilight_outlined: '0xf4bd',
  wc: '0xe6dc',
  wc_sharp: '0xedd1',
  wc_rounded: '0xf02b0',
  wc_outlined: '0xf4be',
  web: '0xe6dd',
  web_sharp: '0xedd4',
  web_rounded: '0xf02b3',
  web_outlined: '0xf4c1',
  web_asset: '0xe6de',
  web_asset_sharp: '0xedd3',
  web_asset_rounded: '0xf02b2',
  web_asset_outlined: '0xf4c0',
  web_asset_off: '0xe6df',
  web_asset_off_sharp: '0xedd2',
  web_asset_off_rounded: '0xf02b1',
  web_asset_off_outlined: '0xf4bf',
  web_stories: '0xe6e0',
  web_stories_sharp: '0xf0850',
  web_stories_rounded: '0xf0899',
  web_stories_outlined: '0xf08b7',
  webhook: '0xf05a4',
  webhook_sharp: '0xf04a9',
  webhook_rounded: '0xf03b6',
  webhook_outlined: '0xf0697',
  wechat: '0xf05a5',
  wechat_sharp: '0xf04aa',
  wechat_rounded: '0xf03b7',
  wechat_outlined: '0xf0698',
  weekend: '0xe6e1',
  weekend_sharp: '0xedd5',
  weekend_rounded: '0xf02b4',
  weekend_outlined: '0xf4c2',
  west: '0xe6e2',
  west_sharp: '0xedd6',
  west_rounded: '0xf02b5',
  west_outlined: '0xf4c3',
  whatshot: '0xe6e3',
  whatshot_sharp: '0xedd7',
  whatshot_rounded: '0xf02b6',
  whatshot_outlined: '0xf4c4',
  wheelchair_pickup: '0xe6e4',
  wheelchair_pickup_sharp: '0xedd8',
  wheelchair_pickup_rounded: '0xf02b7',
  wheelchair_pickup_outlined: '0xf4c5',
  where_to_vote: '0xe6e5',
  where_to_vote_sharp: '0xedd9',
  where_to_vote_rounded: '0xf02b8',
  where_to_vote_outlined: '0xf4c6',
  widgets: '0xe6e6',
  widgets_sharp: '0xedda',
  widgets_rounded: '0xf02b9',
  widgets_outlined: '0xf4c7',
  width_full: '0xf07d5',
  width_full_sharp: '0xf077d',
  width_full_rounded: '0xf082d',
  width_full_outlined: '0xf0725',
  width_normal: '0xf07d6',
  width_normal_sharp: '0xf077e',
  width_normal_rounded: '0xf082e',
  width_normal_outlined: '0xf0726',
  width_wide: '0xf07d7',
  width_wide_sharp: '0xf077f',
  width_wide_rounded: '0xf082f',
  width_wide_outlined: '0xf0727',
  wifi: '0xe6e7',
  wifi_sharp: '0xede0',
  wifi_rounded: '0xf02bf',
  wifi_outlined: '0xf4cc',
  wifi_1_bar: '0xf07d8',
  wifi_1_bar_sharp: '0xf0780',
  wifi_1_bar_rounded: '0xf0830',
  wifi_1_bar_outlined: '0xf0728',
  wifi_2_bar: '0xf07d9',
  wifi_2_bar_sharp: '0xf0781',
  wifi_2_bar_rounded: '0xf0831',
  wifi_2_bar_outlined: '0xf0729',
  wifi_calling: '0xe6e8',
  wifi_calling_sharp: '0xeddc',
  wifi_calling_rounded: '0xf02bb',
  wifi_calling_outlined: '0xf4c9',
  wifi_calling_3: '0xe6e9',
  wifi_calling_3_sharp: '0xeddb',
  wifi_calling_3_rounded: '0xf02ba',
  wifi_calling_3_outlined: '0xf4c8',
  wifi_channel: '0xf05a7',
  wifi_channel_sharp: '0xf04ac',
  wifi_channel_rounded: '0xf03b9',
  wifi_channel_outlined: '0xf069a',
  wifi_find: '0xf05a8',
  wifi_find_sharp: '0xf04ad',
  wifi_find_rounded: '0xf03ba',
  wifi_find_outlined: '0xf069b',
  wifi_lock: '0xe6ea',
  wifi_lock_sharp: '0xeddd',
  wifi_lock_rounded: '0xf02bc',
  wifi_lock_outlined: '0xf4ca',
  wifi_off: '0xe6eb',
  wifi_off_sharp: '0xedde',
  wifi_off_rounded: '0xf02bd',
  wifi_off_outlined: '0xf4cb',
  wifi_password: '0xf05a9',
  wifi_password_sharp: '0xf04ae',
  wifi_password_rounded: '0xf03bb',
  wifi_password_outlined: '0xf069c',
  wifi_protected_setup: '0xe6ec',
  wifi_protected_setup_sharp: '0xeddf',
  wifi_protected_setup_rounded: '0xf02be',
  wifi_protected_setup_outlined: '0xf4cd',
  wifi_tethering: '0xe6ed',
  wifi_tethering_sharp: '0xede3',
  wifi_tethering_rounded: '0xf02c2',
  wifi_tethering_outlined: '0xf4d0',
  wifi_tethering_error: '0xf05aa',
  wifi_tethering_error_sharp: '0xf04af',
  wifi_tethering_error_rounded: '0xf05aa',
  wifi_tethering_error_outlined: '0xf069d',
  wifi_tethering_error_rounded_sharp: '0xf04af',
  wifi_tethering_error_rounded_rounded: '0xf02c0',
  wifi_tethering_error_rounded_outlined: '0xf069d',
  wifi_tethering_off: '0xe6ef',
  wifi_tethering_off_sharp: '0xede2',
  wifi_tethering_off_rounded: '0xf02c1',
  wifi_tethering_off_outlined: '0xf4cf',
  wind_power: '0xf07da',
  wind_power_sharp: '0xf0782',
  wind_power_rounded: '0xf0832',
  wind_power_outlined: '0xf072a',
  window: '0xe6f0',
  window_sharp: '0xede4',
  window_rounded: '0xf02c3',
  window_outlined: '0xf4d1',
  wine_bar: '0xe6f1',
  wine_bar_sharp: '0xede5',
  wine_bar_rounded: '0xf02c4',
  wine_bar_outlined: '0xf4d2',
  woman: '0xf05ab',
  woman_sharp: '0xf04b0',
  woman_rounded: '0xf03bd',
  woman_outlined: '0xf069e',
  woman_2: '0xf087c',
  woman_2_sharp: '0xf0851',
  woman_2_rounded: '0xf089a',
  woman_2_outlined: '0xf08b8',
  woo_commerce: '0xf05ac',
  woo_commerce_sharp: '0xf04b1',
  woo_commerce_rounded: '0xf03be',
  woo_commerce_outlined: '0xf069f',
  wordpress: '0xf05ad',
  wordpress_sharp: '0xf04b2',
  wordpress_rounded: '0xf03bf',
  wordpress_outlined: '0xf06a0',
  work: '0xe6f2',
  work_sharp: '0xede8',
  work_rounded: '0xf02c7',
  work_outlined: '0xf4d5',
  work_history: '0xf07db',
  work_history_sharp: '0xf0783',
  work_history_rounded: '0xf0833',
  work_history_outlined: '0xf072b',
  work_off: '0xe6f3',
  work_off_sharp: '0xede6',
  work_off_rounded: '0xf02c5',
  work_off_outlined: '0xf4d3',
  work_outline: '0xe6f4',
  work_outline_sharp: '0xede7',
  work_outline_rounded: '0xf02c6',
  work_outline_outlined: '0xf4d4',
  workspace_premium: '0xf05ae',
  workspace_premium_sharp: '0xf04b3',
  workspace_premium_rounded: '0xf03c0',
  workspace_premium_outlined: '0xf06a1',
  workspaces: '0xe6f5',
  workspaces_sharp: '0xede9',
  workspaces_rounded: '0xf02c8',
  workspaces_outlined: '0xf4d6',
  workspaces_filled: '0xe6f6',
  workspaces_outline: '0xe6f7',
  wrap_text: '0xe6f8',
  wrap_text_sharp: '0xedea',
  wrap_text_rounded: '0xf02c9',
  wrap_text_outlined: '0xf4d7',
  wrong_location: '0xe6f9',
  wrong_location_sharp: '0xedeb',
  wrong_location_rounded: '0xf02ca',
  wrong_location_outlined: '0xf4d8',
  wysiwyg: '0xe6fa',
  wysiwyg_sharp: '0xedec',
  wysiwyg_rounded: '0xf02cb',
  wysiwyg_outlined: '0xf4d9',
  yard: '0xe6fb',
  yard_sharp: '0xeded',
  yard_rounded: '0xf02cc',
  yard_outlined: '0xf4da',
  youtube_searched_for: '0xe6fc',
  youtube_searched_for_sharp: '0xedee',
  youtube_searched_for_rounded: '0xf02cd',
  youtube_searched_for_outlined: '0xf4db',
  zoom_in: '0xe6fd',
  zoom_in_sharp: '0xedef',
  zoom_in_rounded: '0xf02ce',
  zoom_in_outlined: '0xf4dc',
  zoom_in_map: '0xf05af',
  zoom_in_map_sharp: '0xf04b4',
  zoom_in_map_rounded: '0xf03c1',
  zoom_in_map_outlined: '0xf06a2',
  zoom_out: '0xe6fe',
  zoom_out_sharp: '0xedf1',
  zoom_out_rounded: '0xf02d0',
  zoom_out_outlined: '0xf4de',
  zoom_out_map: '0xe6ff',
  zoom_out_map_sharp: '0xedf0',
  zoom_out_map_rounded: '0xf02cf',
  zoom_out_map_outlined: '0xf4dd',
};
