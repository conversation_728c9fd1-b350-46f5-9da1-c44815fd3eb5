<!--模版社区成功确认框 -->
<template>
  <DialogRoot v-model:open="open">
    <DialogPortal>
      <DialogOverlay
        class="bg-black4 data-[state=open]:animate-overlayShow fixed inset-0 z-[999]"
        @pointerdown.stop
      />
      <DialogContent
        class="dialog-custom-content data-[state=open]:animate-contentShow fixed top-[50%] left-[50%] w-[415px] translate-x-[-50%] translate-y-[-50%] rounded-[13px] bg-white p-4 shadow1 focus:outline-none z-[1000] flex flex-col"
        :class="className"
      >
        <DialogTitle
          class="text-[18px] leading-[28px] font-semibold mr-[46px] mb-1 whitespace-nowrap"
        >
          {{ t('submit_template_title') }}
        </DialogTitle>
        <div class="flex flex-col justify-between">
          <div class="text-[14px] leading-[20px]">
            <div
              v-if="isEdit"
              class="flex flex-col justify-between content-text text-[#475467] mb-4 min-h-[56px]"
            >
              <div v-html="getTranslateStr('edit_template_context_text')"></div>
            </div>
            <div v-else class="flex flex-col content-text text-[#475467] mb-4 min-h-[100px]">
              <div class="mb-6">{{ t('submit_template_context_one') }}</div>
              <div v-html="getTranslateStr('submit_template_context_two')"></div>
            </div>
          </div>
          <!-- <div class="flex"> -->
          <div
            class="cursor-pointer h-10 w-full bg-black3 rounded-lg text-sm text-white flex flex-row justify-center items-center mt-auto"
            data-pld="community-template-success-confirm"
            @click="handleConfirm(true)"
          >
            {{ t('Device_finddevice_failconnect_button') }}
          </div>
          <!-- </div> -->
        </div>
        <DialogClose
          v-if="autoClose"
          class="dialog-close text-grass11 absolute top-[10px] right-[10px] inline-flex w-[18px] h-[18px] appearance-none items-center justify-center rounded-full border-0 focus:outline-none"
          aria-label="Close"
        >
          <svg-icon name="close" class="w-[18px] h-[18px] border-0"></svg-icon>
        </DialogClose>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { storage } from '@/utils/storage';
import { getTranslateStr } from '@/utils/lang';

// import DialogTip from '@/components/dialog-tip';
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from 'radix-vue';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
});

let open = ref(false);
let autoClose = ref(true);
let tipDialog = ref(null);
let checkValue = ref(true);
const emit = defineEmits(['confirm', 'cancel']);

const toggleDialog = (flag) => {
  open.value = flag;
};
const handleConfirm = (isConfirm = true) => {
  open.value = false;
};

defineExpose({
  toggleDialog,
});

onMounted(() => {});
</script>

<style scoped lang="less"></style>
