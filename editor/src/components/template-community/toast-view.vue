<!-- 成功包含预览按钮的toast -->
<template>
  <!-- :duration="3000" swipeDirection="up" -->
  <ToastProvider :duration="2000">
    <ToastRoot
      v-model:open="open"
      class="ToastRoot flex items-center px-5 py-3 bg-white rounded-xl shadow-lg border border-gray-100"
    >
      <svg-icon name="success_icon" class="w-6 h-6 mr-2" />
      <span class="text-4 leading-6 text-[#1F1F1F] flex-1 font-bold">{{
        t('Edit_speaker_dialog_toast_save_success')
      }}</span>
      <ToastAction as-child alt-text="View details">
        <button
          v-if="!enableShareWidget"
          class="px-4 py-1 rounded-full bg-green-50 text-[#34C759] text-sm hover:bg-green-100 transition"
          data-pld="community-template-view"
          @click="viewTemplate"
        >
          {{ t('Filelist_selected_merge_success_view') }}
        </button>
      </ToastAction>
      <ToastClose as-child>
        <button
          class="ml-2 p-1 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600 transition"
          aria-label="Close"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </ToastClose>
    </ToastRoot>
    <ToastViewport
      class="[--viewport-padding:_25px] fixed top-6 left-1/2 -translate-x-1/2 flex flex-col p-[var(--viewport-padding)] gap-[10px] w-[420px] max-w-[90vw] m-0 list-none z-[**********] outline-none"
    />
  </ToastProvider>
</template>

<script setup>
import { ref } from 'vue';
import { ToastAction, ToastClose, ToastProvider, ToastRoot, ToastViewport } from 'radix-vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;

const open = ref(false);
const viewData = ref({});
const options = ref({});

const props = defineProps({
  enableShareWidget: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['viewTemplate']);

const showToast = (data, options = {}) => {
  open.value = true;
  viewData.value = data;
  options.value = options;
};

const viewTemplate = () => {
  emit('viewTemplate', viewData.value);
};

defineExpose({
  showToast,
});
</script>

<style scoped lang="less"></style>
