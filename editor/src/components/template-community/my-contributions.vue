<!-- 模版 -->
<template>
  <div class="my-contributions">
    <div class="flex items-center px-10">
      <TitleBlock class="" :data="t('my_contributions')" />
      <!-- my templates 按钮 -->
      <div
        class="flex items-center text-sm text-[#858C9B] leading-[18px] cursor-pointer"
        data-pld="community-mytemplates-submit-click"
        @click="handleSubmit('myTemplates')"
      >
        <div class="mr-[6px] whitespace-nowrap">{{ t('my_templates') }}</div>
        <svg-icon name="drill_in" class="w-2 h-3"></svg-icon>
      </div>
    </div>
    <MySwiper
      class="weekly-swiper"
      :data="mineTemplatesData"
      :options="options"
      :needSwiperWrapper="true"
    >
      <template #default="{ item, index }">
        <!-- min-w-[240px] -->
        <div
          class="my-swiper-item ml-[20px] h-[160px] relative max-w-[320px] rounded-[14px] mb-6 mt-4 bg-white"
          :class="{
            'mr-[20px]': index === mineTemplatesData.length - 1,
          }"
          @click="handleSwiperSlideClick(mineTemplatesPreviewData, index)"
        >
          <div
            v-if="index === 0"
            class="submit-card w-full h-full flex flex-col items-center justify-center bg-[#F2F4F7] rounded-[14px]"
          >
            <div
              class="w-[64px] h-[64px] bg-[#E4E7EC] rounded-full flex items-center justify-center"
              data-pld="community-template-submit-click"
              @click="handleSubmit('submit')"
            >
              <svg-icon name="plus" class="w-10 h-10"></svg-icon>
            </div>
            <div
              class="text-[#007AFF] font-semibold mt-2"
              data-pld="community-template-submit-click"
              @click="handleSubmit('submit')"
            >
              {{ t('submit_text') }}
            </div>
          </div>
          <CardBox
            :data="item"
            :index="index"
            :badgeStatus="item.badgeStatus"
            :badgeText="item.badgeText"
            v-else
          >
          </CardBox>
        </div>
      </template>
    </MySwiper>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import TitleBlock from './title-block.vue';
import CardBox from './card-box.vue';
import MySwiper from '@/components/swiper/index.vue';
import { getMineTemplates } from '@/apis/templateCommunity';
import { convertToHexColor, findIconCodeToName } from '@/utils/material-icons-collection.js';
import { formatDate } from '@/utils/time-format';

const props = defineProps({
  refresh: {
    type: Boolean,
    default: false,
  },
});

const validateAuthUser = inject('validateAuthUser');
const oSLanguage = inject('oSLanguage');
const options = ref({
  // autoplay: {
  //   delay: 3000,
  //   pauseOnMouseEnter: true,
  // },
  slidesPerView: 4.2,
  slidesPerGroup: 4,
});

const statusMapColor = {
  in_review: 'orange',
  new_in_review: 'orange',
  published: 'green',
  rejected: 'red',
  unavailable: 'red',
};

const statusMapLanguage = {
  in_review: 'in_review_text',
  new_in_review: 'new_in_review_text',
  published: 'published_text',
  rejected: 'rejected_text',
  unavailable: 'unavailable_text',
};

let mineTemplatesData = ref([{ id: 'submit' }]);
let mineTemplatesPreviewData = ref([]);
const emit = defineEmits(['showPreview']);
let newInReviews = []; // 用于记录符合条件的 template_id
const getMineTemplatesData = async () => {
  // console.log('getMineTemplatesData-gsy:');
  try {
    const res = await getMineTemplates({});
    mineTemplatesData.value = (res?.data || []).reduce((acc, item) => {
      const versions = item.versions || [];
      const flattenedVersions = versions.map((version) => {
        // Add logic for new_in_review status
        let status = version.status;
        if (item.is_exist_published && version.status === 'in_review') {
          status = 'new_in_review';
          // 记录 template_id
          newInReviews.push(version.template_id);
        }

        return {
          id: item.id,
          versionId: version.id,
          templateId: version.template_id,
          badgeStatus: statusMapColor[status] || 'green',
          badgeText: statusMapLanguage[status] || '',
          iconColor: convertToHexColor(version.icon_color),
          iconName: findIconCodeToName(version.icon),
          title: version.title,
          description: version.description || '',
          authorName: item.author_name,
          usageCount: item.usage_count,
          custom_type: 'community',
          isLocked: item.is_locked === 1 || false,
          status: status,
          custom_status: 'saveAsTemplate',
          category: version.category,
          authorEmail: version.author_email,
          content: version.content,
          createdAt: formatDate(version.created_at),
          comment: version.comment,
          noEdit: newInReviews.includes(version.template_id),
        };
      });
      return [...acc, ...flattenedVersions];
    }, []);
    mineTemplatesPreviewData.value = (mineTemplatesData.value || []).map((item) => {
      return {
        custom_type: 'community',
        custom_status: item.custom_status,
        orignal: item,
        translated: item,
      };
    });
    mineTemplatesData.value.unshift({ id: 'submit' });

    // console.log('mineTemplatesData-gsy:', mineTemplatesData.value);
  } catch (error) {
    console.log(error);
  }
};

const handleSwiperSlideClick = (data, index) => {
  // console.log('handleSwiperSlideClick:', data, index);
  if (index === 0) {
    return;
  } else {
    emit('showPreview', data, index - 1);
  }
};

const handleSubmit = (type) => {
  if (!validateAuthUser()) {
    return;
  }
  emit('handleSubmit', type);
};

onMounted(() => {
  getMineTemplatesData();
});

defineExpose({
  refresh: getMineTemplatesData,
});
</script>

<style lang="less">
.weekly-swiper {
  .swiper-button-next {
    right: -5px !important;
  }
  .swiper-button-prev {
    left: 0px !important;
  }
}
</style>
