<!-- try it dailog -->
<template>
  <!-- 消息确认框 -->
  <DialogRoot v-model:open="open">
    <DialogPortal>
      <DialogOverlay
        class="bg-black4 data-[state=open]:animate-overlayShow fixed inset-0 z-[99999]"
        @pointerdown.stop
      />
      <DialogContent
        class="dialog-custom-content data-[state=open]:animate-contentShow fixed top-[50%] left-[50%] max-h-[85vh] w-[400px] min-h-[270px] translate-x-[-50%] translate-y-[-50%] rounded-[13px] bg-white p-4 shadow1 focus:outline-none z-[999999]"
        :class="className"
      >
        <DialogTitle class="text-[18px] leading-[28px] font-semibold break-words mr-[46px]">
          {{ t('try_it_title') }}
        </DialogTitle>
        <div class="flex flex-col justify-between">
          <div class="text-[14px] leading-[20px]">
            <div class="content-text text-[#475467] mb-4 min-h-[96px]">
              {{ t('try_it_content') }}
            </div>
            <div class="checkbox flex items-center mb-[18px]">
              <input id="checkbox" type="checkbox" v-model="checkValue" class="mr-2" />
              <label for="checkbox" class="text-gray-700 select-none cursor-pointer">
                {{ t('Smart_clip_guide_never_show') }}
              </label>
            </div>
          </div>
          <div class="flex">
            <div
              class="cursor-pointer h-10 w-full bg-black3 rounded-lg text-sm text-white flex flex-row justify-center items-center"
              @click="handleConfirm(true)"
            >
              {{ t('Device_finddevice_failconnect_button') }}
            </div>
          </div>
        </div>
        <DialogClose
          v-if="autoClose"
          class="dialog-close text-grass11 absolute top-[10px] right-[10px] inline-flex w-[18px] h-[18px] appearance-none items-center justify-center rounded-full border-0 focus:outline-none"
          aria-label="Close"
        >
          <svg-icon name="close" class="w-[18px] h-[18px] border-0"></svg-icon>
        </DialogClose>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { storage } from '@/utils/storage';

// import DialogTip from '@/components/dialog-tip';
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from 'radix-vue';

const props = defineProps({
  userid: {
    type: String,
    required: true,
  },
});

let open = ref(false);
let autoClose = ref(true);
let tipDialog = ref(null);
let checkValue = ref(false);
const emit = defineEmits(['confirm']);

const toggleDialog = (flag) => {
  open.value = flag;
};
const handleConfirm = (isConfirm = true) => {
  open.value = false;
  if (checkValue.value) {
    storage.set('communityTryItNeverShow', checkValue.value, props.userid, 'NoExpiration');
  }
  emit('confirm', isConfirm);
};

defineExpose({
  toggleDialog,
});

onMounted(() => {});
</script>

<style scoped lang="less">
.checkbox input[type='checkbox'] {
  position: relative;
  width: 24px;
  height: 24px;
  border: 2px solid #cacfd8;
  border-radius: 9999px;
  background: #fff;
  appearance: none;
  outline: none;
  cursor: pointer;
}
.checkbox input[type='checkbox']:checked {
  // border-color: #1d1d1d;
  border: 2px solid #1d1d1d;
  background: #1d1d1d;
}
.checkbox input[type='checkbox']:checked::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 9999px;
  background: #1d1d1d;
}
.checkbox input[type='checkbox']:checked::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 18px;
  height: 18px;
  transform: translate(-50%, -50%);
  background: url("data:image/svg+xml;utf8,<svg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M3 6.5L5.5 9L9 4.5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/></svg>")
    no-repeat center/contain;
  z-index: 1;
}
</style>
