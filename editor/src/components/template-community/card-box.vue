<!-- 卡片 -->
<template>
  <div
    class="card-box h-full flex flex-col relative hover:shadow-[0_0_32px_0_rgba(0,0,0,0.10)] bg-white border border-[#E4E7EC] hover:border-transparent rounded-[14px]"
  >
    <Badges v-if="data?.isLocked" :isLocked="data?.isLocked" />
    <Badges v-else-if="data?.badgeText" :text="t(data?.badgeText)" :status="data?.badgeStatus" />

    <div class="flex flex-col">
      <div class="flex items-center mt-3 ml-3 mb-1">
        <span class="text-[24px] font-bold mr-1 text-[#D0D5DD] leading-8" v-if="showIndex">{{
          index + 1
        }}</span>
        <span
          v-if="data.custom_type === 'community'"
          class="material-symbols-rounded flex-shrink-0"
          :style="{
            color: data?.iconColor,
          }"
          >{{ data?.iconName }}</span
        >
        <img
          :src="data?.icon"
          class="w-8 h-8 mr-2 color-[#858C9B] text-8 leading-8"
          v-else-if="data?.icon"
        />
        <img
          src="/static/template_icon_blank.png"
          class="w-8 h-8 mr-2 color-[#858C9B] text-8 leading-8"
          v-else
        />
      </div>
      <div class="px-4 h-[62px] overflow-hidden">
        <SmartEllipsis
          v-if="data.custom_type === 'community'"
          :type="data.custom_type"
          :textOne="data?.title"
          :textTwo="data?.description"
        ></SmartEllipsis>
        <SmartEllipsis
          v-else
          :type="data.custom_type"
          :textOne="data?.name"
          :textThree="data?.keywords"
        ></SmartEllipsis>
      </div>
    </div>

    <div class="flex items-center px-4 pb-3 mt-auto text-4 text-[#858C9B] h-[18px]">
      <template v-if="data.custom_type === 'community'">
        <svg-icon name="dashboard" class="w-4 h-4 mr-[2px]"></svg-icon>
        <span class="mr-2">{{ data.usageCount }}</span>
        <span class="mr-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
        <span class="truncate whitespace-nowrap">{{ data.authorName }}</span>
      </template>
      <template v-else>
        <svg-icon name="plaud_icon" class="w-4 h-4 mr-1"></svg-icon>
        <span class="text-[12px]">PLAUD.AI</span>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import SmartEllipsis from './smart-ellipsis.vue';
import Badges from './badges.vue';

const props = defineProps({
  showIndex: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
});

onMounted(() => {});
</script>

<style scoped lang="less">
.card-box {
}
</style>
