<!-- 模版 -->
<template>
  <div class="top flex items-center justify-between mb-4 px-10">
    <div class="flex items-center">
      <svg-icon name="benefit" class="w-5 h-5 text-black"></svg-icon>
      <span class="font-bold text-black">{{ t('template_community_title') }}</span>
    </div>
    <button
      class="flex items-center bg-blue-100 rounded-full px-3 py-1 cursor-pointer"
      data-pld="community-template-submit-click"
      @click="handleSubmit"
    >
      <svg-icon name="plus" class="w-5 h-5 mr-[2px]"></svg-icon>
      <span class="text-blue-500 font-semibold">{{ t('submit_text') }}</span>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;

const props = defineProps({});
const emit = defineEmits(['handleSubmit']);

const validateAuthUser = inject('validateAuthUser');

const handleSubmit = () => {
  if (!validateAuthUser()) {
    return;
  }
  emit('handleSubmit');
};

onMounted(() => {});
</script>

<style scoped lang="less"></style>
