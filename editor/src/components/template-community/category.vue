<!-- 模版社区 -->
<template>
  <div class="category-wrapper text-black3 text-sm flex items-center space-x-2 relative">
    <div
      ref="gradientOverlay"
      class="w-[80px] h-8 absolute right-0 top-0 bg-[linear-gradient(270deg,_#FFF_0%,_rgba(255,255,255,0)_100%)] transition-opacity duration-300 pointer-events-none"
    ></div>
    <div
      class="flex flex-1 items-center overflow-x-auto pb-[10px] myscrollbar-hover"
      @scroll="handleScroll"
    >
      <div
        class="flex-shrink-0 flex items-center bg-gray-100 rounded-xl px-3 py-1 mr-3 cursor-pointer"
        v-for="(item, index) in categoryData"
        :key="item.category"
        data-pld="community-template-category"
        @click="handleCategoryClick(index)"
      >
        <img :src="item.icon" class="w-[16px] mr-[6px]" />
        <span class="text-gray-700">{{ item.category }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, onBeforeMount } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { getCategory } from '@/apis/templateCommunity';

const props = defineProps({
  categoryData: {
    type: Object,
    default: () => [],
  },
});
const oSLanguageId = inject('oSLanguageId');
const emit = defineEmits(['getCategoryData', 'goCategoryDetailByIndex']);
const handleCategoryClick = (index) => {
  emit('goCategoryDetailByIndex', index);
};

const gradientOverlay = ref(null);
// let categoryData = ref([]);

const handleScroll = (e) => {
  if (gradientOverlay.value) {
    const { scrollLeft, scrollWidth, clientWidth } = e.target;
    const isEnd = Math.abs(scrollWidth - clientWidth - scrollLeft) < 1;
    gradientOverlay.value.style.opacity = isEnd ? '0' : '1';
  }
};

// const getCategoryData = async () => {
//   try {
//     const res = await getCategory({
//       language_os: oSLanguageId.value,
//     });
//     categoryData.value = res?.data || [];
//     emit('getCategoryData', categoryData.value);
//     // console.log('getCategoryData:', res);
//   } catch (error) {
//     console.log(error);
//   }
// };

onMounted(() => {
  // getCategoryData();
});
</script>

<style scoped lang="less">
.category-wrapper {
}
.myscrollbar-hover::-webkit-scrollbar {
  display: none;
}
.myscrollbar-hover:hover::-webkit-scrollbar {
  display: block;
}
.myscrollbar-hover {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent; /* thumb color, track color */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
  }
  &:hover {
    scrollbar-color: #d1d5db transparent; /* thumb color, track color */
    &::-webkit-scrollbar-thumb {
      background-color: #d1d5db;
    }
  }
}
</style>
