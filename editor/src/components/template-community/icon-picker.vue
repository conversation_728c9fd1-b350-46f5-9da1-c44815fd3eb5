<!-- icon 选择器 -->
<template>
  <!-- <div class="">
  </div> -->
  <PopoverRoot>
    <PopoverTrigger>
      <span
        class="material-symbols-rounded w-8 h-8 flex-shrink-0"
        :style="{
          color: currentIconColor,
        }"
        >{{ currentIconName }}</span
      >
      <svg-icon name="triangle_icon" class="w-3 h-3 absolute bottom-[0] right-0"></svg-icon>
    </PopoverTrigger>
    <PopoverPortal>
      <PopoverContent
        side="bottom"
        :side-offset="23"
        style="z-index: 999999"
        class="rounded-[14px] w-[360px] bg-white shadow-lg flex flex-col relative"
        :class="{
          'h-[395px]': windowWidth < 1440,
          'h-[560px]': windowWidth >= 1440,
        }"
      >
        <!-- 顶部小三角，水平居中，贴顶 -->
        <svg-icon
          name="popover_triangle"
          class="w-[47px] h-[13px] absolute left-1/2 -translate-x-1/2 top-[-13px]"
        />
        <!-- 颜色选择 -->
        <div class="w-full flex justify-center border-b-[1px] border-b-[#e0e3e9]">
          <div class="grid grid-cols-6 gap-x-[36px] gap-y-[20px] mb-5 mt-4">
            <div
              v-for="color in Object.values(colorOptions)"
              :key="color"
              class="relative flex items-center justify-center group"
            >
              <!-- 外环：hover/active/选中时显示 -->
              <div
                class="absolute inset-0 rounded-full transition-all pointer-events-none"
                :class="[
                  currentIconColor === color
                    ? 'ring-2 ring-[#cacfd8]'
                    : 'group-hover:ring-2 group-hover:ring-[#e0e3e9] group-active:ring-2 group-active:ring-[#cacfd8]',
                ]"
                style="z-index: 1"
              ></div>
              <!-- 圆点本体 -->
              <div
                class="w-6 h-6 rounded-full cursor-pointer transition-all border-2 border-white"
                :style="{ backgroundColor: color }"
                @click="currentIconColor = color"
              ></div>
            </div>
          </div>
        </div>

        <!-- 图标选择 -->
        <div class="scrollbar-wrapper w-[360px] h-[455px] max-w-full overflow-x-hidden">
          <PerfectScrollbar class="px-[5px] pb-[10px] w-full h-full max-w-full overflow-x-hidden">
            <template v-for="(icons, group) in categoryIcons" :key="group">
              <div class="font-bold text-[#1F1F1F] text-[16px] leading-6 mb-2 ml-[6px] mt-5">
                {{ t(getCategoryLocalizations(group)) }}
              </div>
              <div class="grid grid-cols-6 gap-x-[16px] gap-y-[8px] w-full overflow-x-hidden">
                <button
                  v-for="icon in icons"
                  :key="icon"
                  class="w-10 h-10 flex items-center justify-center rounded-[6px] hover:bg-[#F2F4F7] transition-all"
                  :class="currentIconName === icon ? 'bg-[#F2F4F7]' : ''"
                  @click="currentIconName = icon"
                >
                  <span class="material-symbols-rounded !text-[#858C9B]">{{ icon }}</span>
                </button>
              </div>
            </template>
          </PerfectScrollbar>
        </div>
      </PopoverContent>
    </PopoverPortal>
  </PopoverRoot>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import {
  getRandomColor,
  getRandomIconName,
  colorOptions,
  categoryIcons,
  getCategoryLocalizations,
} from '@/utils/material-icons-collection';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
import {
  PopoverArrow,
  PopoverClose,
  PopoverContent,
  PopoverPortal,
  PopoverRoot,
  PopoverTrigger,
} from 'radix-vue';

const props = defineProps({
  iconColor: {
    type: String,
    default: '',
  },
  iconName: {
    type: String,
    default: '',
  },
});

let currentIconColor = ref(props.iconColor || getRandomColor());
let currentIconName = ref(props.iconName || getRandomIconName());

const getIconInfo = () => {
  return {
    color: currentIconColor.value,
    name: currentIconName.value,
  };
};

const windowWidth = ref(window.innerWidth);

const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

defineExpose({
  getIconInfo,
});
</script>

<style scoped lang="less">
@import '@/assets/styles/scrollbar.less';
</style>
