<!-- badges -->
<template>
  <!-- 主标签 -->
  <div
    class="absolute top-[-4px] h-4 left-[-4px] text-white font-bold text-[12px] px-2 leading-[16px] flex items-center z-10 rounded-tl-[8px] rounded-tr-none rounded-br-[4px] rounded-bl-none"
    :style="{
      background: isLocked ? 'linear-gradient(90deg, #FFE6B5 3.01%, #E7B974 99.66%)' : mainColor,
      color: isLocked ? '#1F1F1F' : 'white',
    }"
  >
    <div class="relative">
      <div>{{ isLocked ? 'Unlimited & Pro' : t(text) }}</div>
      <!-- 左上小三角 -->
      <div
        class="absolute left-triangle left-[-8px] bottom-[-4px] w-0 h-0 border-r-[4px] border-b-[4px] border-b-transparent"
        :style="{
          borderRightColor: isLocked ? '#B78E51' : borderColor,
        }"
      ></div>
      <!-- 右上小三角 -->
      <div
        class="absolute right-triangle right-[-12px] top-0 w-0 h-0 border-l-[4px] border-t-[4px] border-t-transparent"
        :style="{
          borderLeftColor: isLocked ? '#B78E51' : borderColor,
        }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;

const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  status: {
    type: String,
    default: 'green',
  },
  isLocked: {
    type: Boolean,
    default: false,
  },
});

const colorMap = {
  green: {
    main: '#22c55e',
    border: '#178a3a',
  },
  orange: {
    main: '#FF9500',
    border: '#995900',
  },
  red: {
    main: '#FF3B30',
    border: '#99231D',
  },
};

const mainColor = computed(() => colorMap[props.status].main);
const borderColor = computed(() => colorMap[props.status].border);

onMounted(() => {});
</script>

<style scoped lang="less"></style>
