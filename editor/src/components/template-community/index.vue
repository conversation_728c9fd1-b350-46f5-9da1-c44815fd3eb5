<!-- 模版社区 -->
<template>
  <div class="tcommunity-wrapper h-full overflow-y-auto">
    <!-- 顶部 -->
    <TopInfo
      v-if="pageType === 'templateCommunity' && !showDetail"
      class="top-info-fixed py-3 z-[90] min-w-[900px] max-w-[1450px] mx-auto bg-white"
      @handleSubmit="handleSubmit('submit')"
      :class="leftSmallStatus ? '!left-[65px]' : '!left-[242px]'"
    ></TopInfo>
    <div
      v-if="!enableShareWidget"
      class="min-w-[900px] max-w-[1450px] text-black3 pb-3 pt-[53px] text-sm mx-auto relative h-full"
    >
      <!-- 每日精选 -->
      <DailyPicks class="h-[350px]" @showPreview="showPreview"></DailyPicks>
      <!-- 分类 -->
      <Category
        class="pl-8 min-h-[38px]"
        :categoryData="categoryData"
        @goCategoryDetailByIndex="goCategoryDetailByIndex"
      ></Category>
      <!-- 每周趋势 -->
      <WeeklyTrending class="mt-[27px] min-h-[160px]" @showPreview="showPreview"></WeeklyTrending>
      <!-- 我的贡献 -->
      <MyContributions
        ref="myContributionsRef"
        class="mt-[27px] min-h-[160px]"
        @showPreview="showPreview"
        @handleSubmit="handleSubmit"
      ></MyContributions>
      <!-- 一级分类模版 -->
      <ChatTemplates
        ref="chatTemplatesRef"
        class="mt-[27px] min-h-[160px]"
        @goCategoryDetail="goCategoryDetail"
        @showPreview="showPreview"
        @getCategoryData="getCategoryData"
      ></ChatTemplates>
      <!-- 翻译 -->
      <Translate
        class="mt-[3px] pb-[14px]"
        @translateToOriginal="translateToOriginal"
        :viewOriginal="viewOriginal"
      ></Translate>
      <!-- 分类二级模版列表 -->
      <ChatTemplatesList
        class="min-h-[160px] fixed top-0 left-0 right-0 bottom-0 bg-white z-30"
        :class="leftSmallStatus ? 'left-[64px]' : 'left-[241px]'"
        v-if="showDetail"
        :leftSmallStatus="leftSmallStatus"
        :data="chatTemplatesListData"
        @backtoHome="handleBackToHome"
        @showPreview="showPreview"
        @handleSubmit="handleSubmit"
      ></ChatTemplatesList>
    </div>
    <!-- 预览 -->
    <preview
      :userid="userBaseInfo.id"
      ref="previewRef"
      @tryItShow="handleTryItShow"
      @showEdit="showEdit"
      @handleEditMyTemplate="handleEditMyTemplate"
      @submitToCommunity="handleSubmitToCommunity"
      @deleteSuccess="handleDeleteSuccess"
      @cancelDialog="cancelPreviewDialog"
      @handleUse="handleUse"
    ></preview>
    <!-- tryit弹窗 -->
    <!-- @cancel="closePreviewOnly" -->
    <TryDailog ref="tryDailogRef" :userid="userBaseInfo.id"></TryDailog>
    <!-- 编辑模版 -->
    <EditTemplateDailog
      ref="editTemplatesRef"
      :email="userBaseInfo.email"
      :userid="userBaseInfo.id"
      @cancelDialog="cancelDialog"
      @saveSuccess="handleTemplateSaveSuccess"
      @saveCommunitySuccess="handleCommunityTemplateSaveSuccess"
      @deleteSuccess="handleDeleteSuccess"
    ></EditTemplateDailog>
    <!-- 含view的toast-->
    <ToastView
      ref="toastViewRef"
      @viewTemplate="handleViewTemplate"
      :enableShareWidget="enableShareWidget"
    ></ToastView>
    <!-- 我的模版弹窗 -->
    <TemplatesDailog
      ref="templatesDailogRef"
      @createTemplate="handleCreateTemplate"
      @submitToCommunity="handleSubmitToCommunity"
      @handleEditMyTemplate="handleEditMyTemplate"
    ></TemplatesDailog>
    <!-- toast提示 -->
    <ToastInfo ref="toastInfoRef" :msg="toastDeleteMsg" :type="success"></ToastInfo>
  </div>
</template>

<script setup>
import { ref, onMounted, provide, onBeforeMount, watch, computed, nextTick } from 'vue';
import TopInfo from './top-info.vue';
import DailyPicks from './daily-picks.vue';
import Category from './category.vue';
import WeeklyTrending from './weekly-trending.vue';
import ChatTemplates from './chat-templates.vue';
import MyContributions from './my-contributions.vue';
import ChatTemplatesList from './chat-templates-list.vue';
import EditTemplateDailog from './edit-template-dailog.vue';
import Translate from './translate.vue';
import TryDailog from './try-dailog.vue';
import ToastView from './toast-view.vue';
import { toast } from 'sonner';
import Preview from './preview.vue';
import TemplatesDailog from './templates-dailog.vue';
// import CommunitySuccessDailog from './community-success-dailog.vue';
import ToastInfo from './toast-info.vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { getOSLanguage } from '@/utils/language-convert';
import { storage } from '@/utils/storage';

const props = defineProps({
  data: {
    type: Object,
  },
  onChange: {
    type: Function,
  },
  userInfo: {
    type: Object,
  },
  userBaseInfo: {
    type: Object,
  },
  leftSmallStatus: {
    type: Boolean,
    default: false,
  },
  pageType: {
    type: Boolean,
    default: false,
  },
  pageParams: {
    type: Object,
    default: () => {},
  },
});

let viewOriginal = ref(false);
let oSLanguageId = ref(getOSLanguage().tempid);
let oSLanguageName = ref(getOSLanguage().name);
provide('oSLanguageId', oSLanguageId);
provide('oSLanguageName', oSLanguageName);
provide('viewOriginal', viewOriginal);

let showDetail = ref(false);
let chatTemplatesListData = ref({});
let previewRef = ref(null);
let tryDailogRef = ref(null);
let editTemplatesRef = ref(null);
let toastViewRef = ref(null);
let templatesDailogRef = ref(null);
let myContributionsRef = ref(null);
let categoryData = ref([]);
let toastInfoRef = ref(null);
let chatTemplatesRef = ref(null);

const isAuthUser = computed(() =>
  ['pro', 'unlimited', 'backer'].includes(props.userInfo.membership_type),
);
const translateKey = computed(() => {
  let key = viewOriginal.value ? 'orignal' : 'translated';
  // console.log('translateKey-gsy-key:', key);
  storage.set('translateKey', key, props.userBaseInfo.id, 'NoExpiration');
  return key;
});

// 开启共享弹窗组件，不展示模版社区页面
const enableShareWidget = computed(() => {
  return props?.pageParams?.type === 'previewOnly';
});

provide('enableShareWidget', enableShareWidget);
provide('translateKey', translateKey);

// 提供返回首页的方法
const handleBackToHome = () => {
  showDetail.value = false;
};

provide('backToHome', handleBackToHome);

const pageParamsComputed = computed(() => props?.pageParams);

// 监听 pageParams 变化
watch(
  pageParamsComputed,
  (newVal) => {
    // console.log('watch-props.pageParams-newVal:', newVal);
    if (!newVal) return;
    if (newVal && newVal?.type === 'previewOnly') {
      // 使用 nextTick 确保组件已经挂载
      nextTick(() => {
        // TranSummary中的create, 创建个人模版
        if (newVal?.action === 'createTemplate') {
          console.log('watch-props.pageParams-newVal-createTemplate:', newVal?.data);
          handleCreateTemplate(newVal?.data);
        } else if (newVal?.action === 'previewTemplate') {
          let { data, index } = newVal?.data;
          // TranSummary中的preview, 预览分类模版列表
          showPreview(data, index);
        }
      });
    }
  },
  { immediate: true, deep: true },
);

const showUpgradeModel = () => {
  props.onChange('template_locked');
};

// 校验用户是否是会员
const validateAuthUser = () => {
  if (!isAuthUser.value) {
    showUpgradeModel();
    return false;
  }
  return true;
};

provide('validateAuthUser', validateAuthUser);
provide('showUpgradeModel', showUpgradeModel);

const translateToOriginal = (original) => {
  viewOriginal.value = original;
  // console.log('translateToOriginal-gsy-viewOriginal:', viewOriginal.value);
};

// 进入二级分类页面
const goCategoryDetail = (item) => {
  chatTemplatesListData.value = item;
  showDetail.value = true;
};
// 点击首页的卡片进行预览
const showPreview = (data, index = 0) => {
  // console.log('showPreview-gsy-1542:', data, index);
  if (previewRef.value) {
    previewRef.value.showPreview(data, index);
  }
};

// 点击preview 中的personalize
const showEdit = (data) => {
  // console.log('showEdit-gsy:', data);
  if (editTemplatesRef.value) {
    editTemplatesRef.value.show(data, true);
  }
};

// 点击preview 中的try
const handleTryItShow = () => {
  let nevershowFlag = storage.get('communityTryItNeverShow', props.userBaseInfo.id);
  // console.log('handleTryItShow-gsy-nevershowFlag:', nevershowFlag);
  if (!nevershowFlag) {
    tryDailogRef.value.toggleDialog(true);
  } else {
    // The template you selected for trial has been set as the default for next time. You can confidently return to the file list and choose a file to transcribe or summarize again to try this template.
    toast(t('try_it_content'), {
      duration: 3000,
      style: {
        width: '300px',
        whiteSpace: 'pre-line',
        padding: '16px',
        position: 'fixed',
        left: '50%',
        transform: 'translateX(-50%)',
      },
    });
  }
  previewRef.value && previewRef.value.cancelDialog(false);
};

// 编辑弹窗点击cancel
const cancelDialog = (type, flag = false) => {
  console.log('cancelDialog-gsy-type:', type, flag);
  let fromInstance = null;
  if (type === 'tranSummary') {
    if (flag) {
      // 关闭预览
      props.onChange('previewOnly', { action: 'closeTranSummaryDialog' });
    } else {
      // 显示隐藏的预览
      props.onChange('previewOnly', { action: 'revertTranSummaryDialog' });
    }
    return;
  } else {
    switch (type) {
      case 'templates-dailog':
        fromInstance = templatesDailogRef.value;
        break;
      default:
        fromInstance = previewRef.value;
        break;
    }
    if (fromInstance) {
      if (flag) {
        // 关闭预览
        fromInstance.cancelDialog();
      } else {
        // 下一级弹窗点击cancel的时候，显示隐藏的预览
        fromInstance.revertDialog();
      }
    }
  }
};

//  personalize 保存成功后
const handleTemplateSaveSuccess = (data) => {
  // console.log('handleTemplateSaveSuccess-gsy:', data);
  toastViewRef.value && toastViewRef.value.showToast(data);
  templatesDailogRef.value && templatesDailogRef.value.refresh();
  if (enableShareWidget.value) {
    props.onChange('previewOnly', { action: 'saveCustomTemplateSuccess', data });
    // props.onChange('previewOnly', { action: 'revertTranSummaryDialog' });
  }
};

// 提交到社区成功后
const handleCommunityTemplateSaveSuccess = (data) => {
  // console.log('handleCommunityTemplateSaveSuccess-gsy:', data);
  myContributionsRef.value && myContributionsRef.value.refresh();
  previewRef.value && previewRef.value.cancelDialog();
  templatesDailogRef.value && templatesDailogRef.value.cancelDialog();
  if (enableShareWidget.value) {
    props.onChange('previewOnly', { action: 'closeTranSummaryDialog' });
  }
};

// 删除个人/社区模版成功后
const handleDeleteSuccess = (type) => {
  // console.log('handleDeleteSuccess-gsy-type:', type);
  previewRef.value && previewRef.value.cancelDialog();
  if (type === 'community') {
    myContributionsRef.value && myContributionsRef.value.refresh();
  } else {
    templatesDailogRef.value && templatesDailogRef.value.refresh();
  }
  if (type === 'personal') {
    templatesDailogRef.value && templatesDailogRef.value.revertDialog();
  }
  toastInfoRef.value.showToast(t('delete_success_tip'), 'success');
};

const cancelPreviewDialog = () => {
  if (enableShareWidget.value) {
    props.onChange('previewOnly', { action: 'revertTranSummaryDialog' });
  }
};

const handleUse = (data) => {
  if (enableShareWidget.value) {
    props.onChange('previewOnly', { action: 'useTemplate', data });
    // props.onChange('previewOnly', { action: 'closeTranSummaryDialog' });
  }
};

// 编辑个人模版
const handleEditMyTemplate = (data) => {
  // console.log('handleEditMyTemplate-gsy:', data);
  editTemplatesRef.value && editTemplatesRef.value.show(data, true);
  if (data.from === 'templates-dailog') {
    templatesDailogRef.value && templatesDailogRef.value.hideDialog();
  } else {
    previewRef.value && previewRef.value.hideDialog();
  }
};

// personalize 保存成功后，点击view
const handleViewTemplate = (data) => {
  // console.log('handleViewTemplate-gsy:', data);
  if (previewRef.value) {
    previewRef.value.showPreview([data], 0);
  }
};

// 我的模版弹窗，点击底部create
const handleCreateTemplate = (data) => {
  // console.log('handleCreateTemplate-gsy:', data);
  if (data.from === 'tranSummary') {
    editTemplatesRef.value && editTemplatesRef.value.show(data, true);
  } else {
    editTemplatesRef.value && editTemplatesRef.value.show(data, true);
    templatesDailogRef.value && templatesDailogRef.value.hideDialog();
  }
};

// 提交到社区逻辑
const handleSubmitToCommunity = (data) => {
  // console.log('handleSubmitToCommunity-gsy:', data);
  editTemplatesRef.value && editTemplatesRef.value.show(data, true);
  if (data.from === 'preview') {
    previewRef.value && previewRef.value?.hideDialog();
  } else if (data.from === 'templates-dailog') {
    templatesDailogRef.value && templatesDailogRef.value?.hideDialog();
  }
};

// 点击"submit"按钮
const handleSubmit = (type) => {
  templatesDailogRef.value.show(type);
};

// 获取分类数据
const getCategoryData = (data) => {
  // console.log('getCategoryData-gsy:', data);
  categoryData.value = data;
};

// 根据类别索引跳转二级分类详情
const goCategoryDetailByIndex = (index) => {
  // console.log('goCategoryDetailByIndex-gsy:', index);
  chatTemplatesRef.value.goCategoryDetailByIndex(index);
};

// 添加一个方法来处理弹窗显示
const showTryDialog = () => {
  if (tryDailogRef.value) {
    // console.log('Showing try dialog...');
    tryDailogRef.value.toggleDialog(true);
  } else {
    // console.log('tryDailogRef is not ready');
  }
};

onMounted(() => {
  // console.log('onMounted-props.userInfo.membership_type:', props.userInfo.membership_type);
  // tryDailogRef.value.toggleDialog(true);
  // templatesDailogRef.value.show();
  // if (props?.pageParams?.type === 'previewOnly') {
  //   nextTick(() => {
  //     showTryDialog();
  //   });
  // }
});
</script>

<style scoped lang="less">
.tcommunity-wrapper {
}
.top-info-fixed {
  position: fixed;
  top: 0;
  left: 242px;
  right: 0;
  background: #fff; /* 保证内容不透明 */
}
</style>
