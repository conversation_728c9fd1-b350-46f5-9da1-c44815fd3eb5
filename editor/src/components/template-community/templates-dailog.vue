<!-- 选择自定义模版弹窗 -->
<template>
  <DialogRoot v-model:open="dialogStatus">
    <DialogPortal>
      <!-- @pointerdown.stop="cancelDialog" -->
      <DialogOverlay
        class="data-[state=open]:animate-overlayShow fixed inset-0 z-[999] bg-black/40 backdrop-blur-2xl"
      />
      <DialogContent
        v-show="dialogShow"
        class="audio-dailog fixed top-[90px] bottom-[90px] left-[50%] w-[840px] h-[calc(100%-180px)] text-[#1F1F1F] translate-x-[-50%] bg-[#F2F4F7] focus:outline-none z-[9999] flex flex-col rounded-[13px] overflow-hidden"
      >
        <div class="flex flex-col h-full">
          <!-- 顶部标题 -->
          <div class="flex items-center justify-between px-4 pt-4 pb-[6px]">
            <div class="text-[18px] leading-[28px] font-semibold text-[#1F1F1F]">
              {{
                t(
                  `${dialogType == 'submit' ? 'select_your_template_text' : 'my_summary_template_text'}`,
                )
              }}
            </div>
            <svg-icon
              name="x_mark"
              class="w-[30px] h-[30px] cursor-pointer"
              @click="cancelDialog"
            ></svg-icon>
          </div>
          <div class="flex flex-1 bg-[#F2F4F7] overflow-hidden">
            <!-- 左侧模板列表 -->
            <div class="scrollbar-wrapper w-[320px] flex flex-col">
              <PerfectScrollbar class="w-full space-y-2 px-5 py-6">
                <div
                  v-for="(tpl, idx) in currentData"
                  :key="tpl.id"
                  :data-pld="`community-template-list-click-${tpl.id}`"
                  @click="selectTemplate(idx)"
                  :class="[
                    'relative list-item cursor-pointer rounded-[14px] w-full h-[120px]  border transition-all bg-white border-transparent overflow-hidden',
                    idx === selectedIndex ? 'active' : '',
                  ]"
                >
                  <svg-icon
                    v-if="idx === selectedIndex"
                    name="check_mark"
                    class="w-[17px] h-[17px] absolute bottom-[1px] right-[1px]"
                  ></svg-icon>
                  <div
                    class="bg-white px-3 py-2 border-4 border-transparent m-[1px] rounded-[14px] h-[116px] overflow-hidden"
                  >
                    <div class="flex flex-col">
                      <img
                        src="static/template_icon_customize.png"
                        class="w-8 h-8 mb-1 align-middle inline-block"
                        style="vertical-align: middle"
                      />
                      <span class="font-bold text-[14px] leading-[20px] mb-1">{{ tpl.title }}</span>
                    </div>
                    <div class="text-[12px] leading-[18px] text-[#858C9B] line-clamp-2">
                      {{ tpl.content }}
                    </div>
                  </div>
                </div>
              </PerfectScrollbar>
            </div>
            <!-- 右侧内容区 -->
            <div class="scrollbar-wrapper flex-1 flex flex-col mr-2 bg-white rounded-[14px]">
              <PerfectScrollbar ref="scrollbarRef">
                <div
                  class="p-6 prose max-w-none flex-1 text-[14px] leading-[20px]"
                  v-html="getDefineStr(selectedTemplate?.content || '')"
                ></div>
              </PerfectScrollbar>
            </div>
          </div>
          <!-- 底部按钮栏 -->
          <div
            class="flex items-center relative justify-between w-full h-[68px] pl-8 pr-5 py-4 bg-whit mt-0"
          >
            <!-- mask -->
            <div
              class="mask absolute left-0 top-[-24px] w-[320px] h-6 bg-[linear-gradient(180deg,_rgba(242,244,247,0)_0%,_#F2F4F7_100%)]"
            ></div>
            <!-- create_tip -->
            <div
              v-if="tipShow"
              class="tip z-[999999] bg-[#2388FF] min-h-[48px] absolute top-[-42px] left-[16px] w-[320px] p-3 text-white text-[12px] leading-[18px] rounded-[10px]"
            >
              {{ t('create_tip') }}
              <span class="absolute top-3 right-3 cursor-pointer" @click="closeTip">
                <svg-icon name="close_bgwhite" class="w-4 h-4 cursor-pointer"></svg-icon>
              </span>
              <span
                class="absolute left-[25px] bottom-[-10px] w-0 h-0"
                style="
                  border-left: 10px solid transparent;
                  border-right: 10px solid transparent;
                  border-top: 10px solid #2388ff;
                "
              ></span>
            </div>
            <!-- create按钮 -->
            <div
              class="flex cursor-pointer relative"
              data-pld="community-template-create"
              @click="createTemplate"
            >
              <svg-icon name="plus_icon" class="w-6 h-6 mr-1"></svg-icon>
              <div
                class="text-[16px] leading-6 font-bold bg-[linear-gradient(90deg,_#949FFF_0%,_#FF7CBB_100%)] bg-clip-text text-transparent"
              >
                {{
                  t(
                    `${dialogType == 'myTemplates' ? 'Folder_create_button' : 'create_template_text'}`,
                  )
                }}
              </div>
            </div>
            <!-- 右侧按钮 -->
            <div class="flex items-center" v-if="hasData">
              <!-- 从"我的模版"进来-展示提交到"社区按钮"和"编辑按钮" -->
              <template v-if="dialogType == 'myTemplates'">
                <button
                  class="py-[10px] pl-6 pr-5 rounded-[8px] text-[16px] leading-6 bg-[#E4E7EC] text-[#1F1F1F] font-bold transition focus:outline-none"
                  data-pld="community-template-submit-to-community-click"
                  @click="submitToCommunity(selectedTemplate)"
                >
                  {{ t('submit_community_text') }}
                </button>
                <button
                  v-if="dialogType == 'myTemplates'"
                  class="py-[10px] pl-6 pr-5 ml-3 rounded-[8px] text-[16px] leading-6 bg-black text-white font-bold hover:bg-gray-800 transition"
                  data-pld="community-template-mytemplates-edit"
                  @click="handleEditMyTemplate(selectedTemplate)"
                >
                  {{ t('Filedetail_transcription_textfunction_buttontwo') }}
                </button>
              </template>
              <!-- 从"submit"进来-展示提交到"社区按钮" -->
              <button
                v-else
                class="py-[10px] pl-6 pr-5 rounded-[8px] text-[16px] leading-6 bg-black text-white font-bold hover:bg-gray-800 transition"
                data-pld="community-template-submit-to-community-click"
                @click="submitToCommunity(selectedTemplate)"
              >
                {{ t('submit_community_text') }}
              </button>
            </div>
          </div>
        </div>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup>
import { ref, onMounted, inject, watch, computed, nextTick } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import {
  DialogClose,
  DialogContent,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
} from 'radix-vue';
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
import { markdownSpeakerEditFormat } from '@/utils/markdown';
import { getMineTemplatesList } from '@/apis/templateCommunity';

const validateAuthUser = inject('validateAuthUser');

const emit = defineEmits([
  'handleSubmit',
  'createTemplate',
  'submitToCommunity',
  'handleEditMyTemplate',
]);

const props = defineProps({
  userid: {
    type: String,
    required: true,
  },
});

let dialogStatus = ref(false);
let dialogShow = ref(true);
let tipShow = ref(false);

const currentData = ref([]);
// 弹窗类型：submit、myTemplates
const dialogType = ref('myTemplates');
let fromType = ref('templates-dailog');

const selectedIndex = ref(0);
const selectedTemplate = computed(() => currentData.value[selectedIndex.value]);
const hasData = computed(() => currentData.value.length > 0);

// 监听 selectedTemplate 变化
watch(
  () => selectedTemplate.value,
  () => {
    nextTick(() => {
      if (scrollbarRef.value) {
        // 获取内部 DOM 元素并置顶
        const el = scrollbarRef.value.$el?.querySelector('.ps') || scrollbarRef.value.$el;
        if (el) el.scrollTop = 0;
      }
    });
  },
);

const init = () => {
  dialogStatus.value = false;
  dialogShow.value = true;
  selectedIndex.value = 0;
};
const selectTemplate = (idx) => {
  selectedIndex.value = idx;
};

const createTemplate = () => {
  if (!validateAuthUser()) {
    dialogStatus.value = false;
    return;
  }
  let custom_status = dialogType.value == 'myTemplates' ? 'saveAsTemplate' : 'create';
  // 创建新模板逻辑
  emit('createTemplate', { custom_status, from: fromType.value });
};

const cancelDialog = () => {
  dialogStatus.value = false;
  init();
};

const revertDialog = () => {
  // console.log('revertDialog-gsy:');
  dialogShow.value = true;
};

const hideDialog = () => {
  dialogShow.value = false;
};

const getDefineStr = (str) => {
  if (!str) return str;
  return str.replace(/\n/g, '<br/>');
};

const submitToCommunity = (item) => {
  // console.log('submitToCommunity-gsy:', item);
  // 创建新模板逻辑
  emit('submitToCommunity', { ...item, custom_status: 'create', from: fromType.value });
};

const handleEditMyTemplate = (item) => {
  // console.log('handleEdit-gsy:', item);
  emit('handleEditMyTemplate', {
    ...item,
    custom_status: 'saveAsTemplate',
    from: fromType.value,
  });
};

const getMineTemplatesData = async () => {
  // console.log('getMineTemplatesData-gsy:');
  try {
    const res = await getMineTemplatesList({});

    if (!res?.data) {
      console.warn('No data received from weekly recommend API');
      currentData.value = [];
      return;
    }

    currentData.value = (res.data || []).map((item) => {
      return {
        ...item,
        title: item.name,
      };
    });

    if (res?.data.length === 0) {
      tipShow.value = true;
    }
    // console.log('getMineTemplatesData-gsy:', currentData.value);
  } catch (error) {
    console.error('Failed to fetch weekly data:', error);
    currentData.value = [];
  }
};

const show = (type) => {
  dialogType.value = type;
  dialogStatus.value = true;
};

const closeTip = () => {
  tipShow.value = false;
};

const scrollbarRef = ref(null);

onMounted(() => {
  getMineTemplatesData(); // 移除这里的自动请求
});

defineExpose({
  refresh: getMineTemplatesData,
  show,
  cancelDialog,
  hideDialog,
  revertDialog,
});
</script>

<style scoped lang="less">
@import '@/assets/styles/scrollbar.less';
.active {
  border: 1px solid transparent;
  padding: 0;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image:
    linear-gradient(to right, #c366f2, #ff89ae), linear-gradient(90deg, #8b8cf9, #fa8865);
}
</style>
