<!-- 模版 -->
<template>
  <div
    class="title-wrapper flex items-center text-[20px] leading-[30px] font-bold w-full"
    :class="{ 'cursor-pointer': showIcon }"
  >
    <div class="title mr-1">{{ data }}</div>
    <svg-icon name="drill_in" class="w-[11px] h-[22px]" v-if="showIcon"></svg-icon>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { getOSLanguage } from '@/utils/language-convert';

const props = defineProps({
  data: {
    type: String,
    default: '',
    // type: Object,
    // default: {
    //   name: 'title',
    //   key: 'title_id',
    // },
  },
  showIcon: {
    type: Boolean,
    default: false,
  },
});

onMounted(() => {});
</script>

<style scoped lang="less"></style>
