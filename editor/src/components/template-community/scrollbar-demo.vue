<!-- 模版社区 -->
<template>
  <div class="scrollbar-container" style="height: 200px; position: relative; overflow: hidden">
    <PerfectScrollbar :options="{ suppressScrollX: true }">
      <div class="content-wrapper">
        <div v-for="item in 100" :key="item">PerfectScrollbar</div>
      </div>
    </PerfectScrollbar>
  </div>
  <!-- </div> -->
  <!-- <div class="tcommunity-wrapper h-full overflow-y-auto">
    <div class="min-w-[1100px] max-w-[1450px] text-black3 py-3 text-sm mx-auto relative h-full">
      <TopInfo></TopInfo>
      <DailyPicks class="h-[350px]"></DailyPicks>
      <Category class="pl-8"></Category>
      <WeeklyTrending class="mt-[27px] min-h-[160px]"></WeeklyTrending>
      <MyContributions class="mt-[27px] min-h-[160px]"></MyContributions>
      <ChatTemplates
        class="mt-[27px] min-h-[160px]"
        @goCategoryDetail="goCategoryDetail"
      ></ChatTemplates>
      <Translate class="mt-[3px] mb-[14px]" @translateToOriginal="translateToOriginal"></Translate>
      <ChatTemplatesList
        class="fixed top-0 left-0 right-0 bottom-0 bg-white z-30"
        :class="leftSmallStatus ? 'left-[64px]' : 'left-[241px]'"
        v-if="showDetail"
        :data="chatTemplatesListData"
        @backtoHome="goBackHome"
        @showPreview="showPreview"
      ></ChatTemplatesList>
      <preview ref="previewRef"></preview>
    </div>
  </div> -->
</template>

<script setup>
import { ref, onMounted, provide, onBeforeMount, watch, computed } from 'vue';
import TopInfo from './top-info.vue';
import DailyPicks from './daily-picks.vue';
import Category from './category.vue';
import WeeklyTrending from './weekly-trending.vue';
import ChatTemplates from './chat-templates.vue';
import MyContributions from './my-contributions.vue';
import ChatTemplatesList from './chat-templates-list.vue';
import Translate from './translate.vue';
import Preview from './preview.vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { getOSLanguage } from '@/utils/language-convert';
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';

const props = defineProps({
  data: {
    type: Object,
  },
  onChange: {
    type: Function,
  },
  userInfo: {
    type: Object,
  },
  userBaseInfo: {
    type: Object,
  },
  leftSmallStatus: {
    type: Boolean,
    default: false,
  },
});

let viewOriginal = ref(false);
let oSLanguageId = ref(getOSLanguage().tempid);
let oSLanguageName = ref(getOSLanguage().name);
provide('oSLanguageId', oSLanguageId);
provide('oSLanguageName', oSLanguageName);
provide('viewOriginal', viewOriginal);

let showDetail = ref(false);
let chatTemplatesListData = ref({});
let previewRef = ref(null);

const translateKey = computed(() => {
  return viewOriginal.value ? 'translated' : 'orignal';
});

provide('translateKey', translateKey);

// 提供返回首页的方法
const handleBackToHome = () => {
  showDetail.value = false;
};
provide('backToHome', handleBackToHome);

const translateToOriginal = (original) => {
  viewOriginal.value = original;
};

const goCategoryDetail = (item) => {
  chatTemplatesListData.value = item;
  showDetail.value = true;
};

const goBackHome = () => {
  showDetail.value = false;
};

const showPreview = (data) => {
  if (previewRef.value) {
    previewRef.value.showPreview(data);
  }
};

onMounted(() => {});
</script>

<style scoped lang="less">
.scrollbar-container {
  :deep(.ps) {
    height: 100%;
    position: relative;
  }
  :deep(.ps__rail-y) {
    right: 0;
  }
  :deep(.ps__rail-x) {
    display: none;
  }
}
.content-wrapper {
  padding: 10px;
}
</style>
