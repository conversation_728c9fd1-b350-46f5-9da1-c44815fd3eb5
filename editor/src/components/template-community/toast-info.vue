<!-- toast-info -->
<template>
  <ToastProvider :duration="2000">
    <ToastRoot
      v-model:open="open"
      class="ToastRoot flex items-center px-5 py-3 bg-white rounded-xl shadow-lg border border-gray-100"
    >
      <svg-icon :name="iconName" class="w-6 h-6 mr-2" />
      <span class="text-4 leading-6 text-[#1F1F1F] flex-1 font-bold">{{ msg }}</span>
      <ToastAction as-child alt-text="View details"> </ToastAction>
      <ToastClose as-child>
        <button
          class="ml-2 p-1 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600 transition"
          aria-label="Close"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </ToastClose>
    </ToastRoot>
    <ToastViewport
      class="[--viewport-padding:_25px] fixed top-6 left-1/2 -translate-x-1/2 flex flex-col p-[var(--viewport-padding)] gap-[10px] w-[420px] max-w-[90vw] m-0 list-none z-[**********] outline-none"
    />
  </ToastProvider>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ToastAction, ToastClose, ToastProvider, ToastRoot, ToastViewport } from 'radix-vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;

const props = defineProps({});
let msg = ref('');
let type = ref('info');
const open = ref(false);
const iconName = computed(() => {
  switch (type.value) {
    case 'success':
      return 'success_icon';
    // case 'error':
    //   return 'error_icon';
    // case 'warning':
    //   return 'warning_icon';
    default:
      return 'info_icon';
  }
});
const showToast = (title, type) => {
  open.value = true;
  msg.value = title;
  type.value = type;
};

defineExpose({
  showToast,
});
</script>

<style scoped lang="less"></style>
