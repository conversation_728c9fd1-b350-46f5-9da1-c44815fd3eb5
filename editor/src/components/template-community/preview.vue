<!-- 模版Preview -->
<template>
  <div>
    <DialogRoot v-model:open="dialogStatus">
      <DialogPortal>
        <DialogOverlay
          @pointerdown.stop
          class="preview-overlay data-[state=open]:animate-overlayShow fixed inset-0 z-[9999] bg-black/40 backdrop-blur-2xl"
        />
        <!--   @pointerdown.stop -->

        <DialogContent
          v-show="previewShow"
          class="audio-dailog mavon-view fixed left-[50%] w-full translate-x-[-50%] bg-transparent focus:outline-none z-[99999] flex flex-col"
          :class="{
            'top-[9vh] h-[91vh]': windowWidth <= 1440,
            'top-[10vh] h-[90vh]': windowWidth > 1440 && windowWidth <= 1600,
            'top-[20vh] h-[80vh]': windowWidth > 1600,
          }"
        >
          <MySwiper
            class="preview-swiper pl-0 !min-w-[560px] !w-[29vw] mx-auto h-full"
            :data="listData"
            :options="options"
            :needSwiperWrapper="false"
            :showNavigation="true"
            :isOverflowHidden="false"
            @slideChange="handleSwiperSlideChange"
          >
            <template #default="{ item, index }">
              <div
                class="my-swiper-item w-full bg-white rounded-t-[20px]"
                :style="{
                  'pl-[20px]': index === 10,
                  height: windowWidth <= 1440 ? '91vh' : windowWidth <= 1600 ? '90vh' : '80vh',
                }"
                @click="handleSwiperSlideClick(item)"
              >
                <div
                  class="top-box w-full h-[200px] rounded-t-[20px] absolute top-0 left-0 right-0 z-[-1] duration-300"
                  :style="{
                    background: `linear-gradient(180deg, ${getFileDetailColor(item, currentTranslateKey)} 0%, rgba(255, 238, 215, 0) 100%)`,
                    zIndex: contentScrollTop > 0 ? 0 : 10,
                    opacity: contentScrollTop > 0 ? 0.5 : 1,
                  }"
                ></div>

                <div class="rounded-t-[20px] absolute top-0 left-0 right-0 z-[100]">
                  <div
                    class="info-box flex items-center justify-between px-4 py-2 rounded-t-[20px] absolute top-0 left-0 right-0"
                  >
                    <svg-icon name="close_icon" class="w-6 h-6" @click="cancelDialog"></svg-icon>
                    <!-- 右侧按钮组 -->
                    <div class="flex items-center space-x-2 ml-auto font-bold">
                      <template v-if="item.custom_status === 'saveAsTemplate'">
                        <template v-if="item[currentTranslateKey]?.status === 'published'">
                          <button
                            v-if="!item[currentTranslateKey]?.noEdit"
                            class="bg-black text-white px-4 py-1 rounded-full"
                            data-pld="community-template-submit-to-community"
                            @click.stop="submitToCommunity(item)"
                          >
                            {{ t('update_template_text') }}
                          </button>
                        </template>
                        <template
                          v-else-if="
                            ['in_review', 'new_in_review'].includes(
                              item[currentTranslateKey]?.status,
                            )
                          "
                        >
                        </template>
                        <template
                          v-else-if="
                            ['rejected', 'unavailable'].includes(item[currentTranslateKey]?.status)
                          "
                        >
                          <button
                            class="mr-[6px] bg-black/5 text-[#FF3B30] px-4 py-1 rounded-full flex items-center"
                            data-pld="community-template-delete-click"
                            @click="handleDelete(item)"
                          >
                            <!-- <loadingSpin class="mr-1 !w-4 !h-4" v-if="deleteLoading"></loadingSpin> -->
                            {{ t('CropAudio_bottom_action_Delete') }}
                          </button>
                          <button
                            class="bg-black text-white px-4 py-1 rounded-full"
                            data-pld="community-template-submit-to-community"
                            @click="submitToCommunity(item)"
                          >
                            {{ t('Filedetail_transcription_textfunction_buttontwo') }}
                          </button>
                        </template>
                        <template v-else>
                          <button
                            class="bg-black/5 text-gray-800 px-4 py-1 rounded-full"
                            data-pld="community-template-submit-to-community"
                            @click.stop="submitToCommunity(item)"
                          >
                            {{ t('submit_community_text') }}
                          </button>
                          <button
                            class="bg-black text-white px-4 py-1 rounded-full"
                            data-pld="community-template-edit"
                            @click="handleEdit(item, 'saveAsTemplate')"
                          >
                            {{ t('Filedetail_transcription_textfunction_buttontwo') }}
                          </button>
                        </template>
                      </template>
                      <template v-else>
                        <button
                          v-if="item.custom_type === 'community'"
                          class="bg-black/5 text-gray-800 px-4 py-1 rounded-full"
                          data-pld="community-template-edit"
                          @click.stop="handleEdit(item, 'personalize')"
                        >
                          {{ t('personalize_text') }}
                        </button>
                        <!-- vue2 tranSummary 预览的Use button -->
                        <button
                          v-if="enableShareWidget"
                          class="bg-black text-white px-4 py-1 rounded-full"
                          data-pld="community-template-use"
                          @click="handleUse(item, index)"
                        >
                          {{ t('use_text') }}
                        </button>
                        <button
                          v-else
                          class="bg-black text-white px-4 py-1 rounded-full"
                          data-pld="community-template-try"
                          @click="handleTryIt(item, 'personalize')"
                        >
                          {{ t('try_it_text') }}
                        </button>
                      </template>
                    </div>
                  </div>
                </div>

                <div
                  :class="`content-box h-full overflow-y-auto absolute px-6 pt-10 pb-[12px] top-[44px] left-0 right-0 z-[10] ${contentScrollTop > 0 ? 'bg-white' : ''}`"
                  @scroll="handleContentScroll"
                  ref="contentBoxRef"
                >
                  <!-- 根据审核status显示不同的内容 -->
                  <template v-if="item.custom_status === 'saveAsTemplate'">
                    <div
                      v-if="['unavailable', 'rejected'].includes(item[currentTranslateKey]?.status)"
                      class="description-tip text-[12px] leading-[18px] py-[6px] px-3 mb-6 text-[#CC2F26] border border-[#FF6259] bg-[#FFEBEA] rounded-[8px]"
                    >
                      <div class="font-bold">{{ t(item[currentTranslateKey]?.badgeText) }}</div>
                      <div>{{ item[currentTranslateKey]?.comment }}</div>
                    </div>
                    <div
                      v-else-if="
                        ['in_review', 'new_in_review'].includes(item[currentTranslateKey]?.status)
                      "
                      class="description-tip text-[12px] leading-[18px] py-[6px] px-3 mb-6 text-[#C70] border border-[#FA3] bg-[#FFF4E5] rounded-[8px]"
                    >
                      <div class="font-bold">{{ t(item[currentTranslateKey]?.badgeText) }}</div>
                      <div
                        v-html="
                          getTranslateStr(
                            'description_inreview_tip',
                            '%s',
                            item[currentTranslateKey]?.authorEmail,
                          )
                        "
                      ></div>
                    </div>
                  </template>

                  <!-- 会员状态显示 -->
                  <div
                    v-if="item[currentTranslateKey]?.isLocked"
                    class="member-status mb-[2px] w-fit mx-auto px-2 leading-[18px] rounded-[20px] font-bold text-[12px] text-[#1F1F1F] border border-[rgba(0, 0, 0, 0.10)]"
                    style="background: linear-gradient(90deg, #ffe6b5 3.01%, #e7b974 99.66%)"
                  >
                    Unlimited & Pro
                  </div>
                  <!-- title -->
                  <div class="mb-[14px]">
                    <p
                      class="w-full leading-[28px] text-[18px] text-[#1f1f1f] font-bold break-all text-center"
                    >
                      <span
                        v-if="item.custom_type === 'community'"
                        class="material-symbols-rounded flex-shrink-0 align-middle inline-block mr-2"
                        :style="{
                          color: item[currentTranslateKey]?.iconColor,
                          fontSize: '32px',
                          verticalAlign: 'middle',
                        }"
                        >{{ item[currentTranslateKey]?.iconName }}</span
                      >
                      <img
                        v-else-if="item.icon"
                        :src="item.icon"
                        class="w-8 h-8 align-middle inline-block mr-2"
                        style="vertical-align: middle"
                      />
                      <img
                        v-else-if="item.id"
                        src="static/template_icon_customize.png"
                        class="w-8 h-8 align-middle inline-block mr-2"
                        style="vertical-align: middle"
                      />
                      <img
                        v-else
                        src="static/template_icon_blank.png"
                        class="w-8 h-8 align-middle inline-block mr-2"
                        style="vertical-align: middle"
                      />
                      <span class="align-middle">{{
                        item.custom_type === 'community'
                          ? item[currentTranslateKey].title
                          : item.name
                      }}</span>
                    </p>
                  </div>
                  <!-- subtitle -->
                  <!-- 来自my contribution 有审核状态的数据 -->
                  <div
                    v-if="
                      item.custom_status === 'saveAsTemplate' && item[currentTranslateKey]?.status
                    "
                    class="flex justify-center items-center mb-[25px] text-4 text-[#858C9B] h-[18px] leading-[18px]"
                  >
                    <template v-if="item.custom_type === 'community'">
                      <span class="truncate whitespace-nowrap">{{
                        item[currentTranslateKey].category
                      }}</span>
                      <template
                        v-if="
                          ['published', 'new_in_review'].includes(item[currentTranslateKey]?.status)
                        "
                      >
                        <span class="mx-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
                        <svg-icon name="dashboard" class="w-4 h-4 mr-[2px]"></svg-icon>
                        <span class="mr-2">{{ item[currentTranslateKey].usageCount }}</span>
                      </template>
                      <span class="mx-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
                      <span class="truncate whitespace-nowrap">{{
                        item[currentTranslateKey].authorName
                      }}</span>
                      <!--  -->
                      <!-- v-if="!['published'].includes(item[currentTranslateKey]?.status)" -->
                      <!-- <template> -->
                      <span class="mx-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
                      <span class="truncate whitespace-nowrap"
                        >{{ t('submit_dot_text') }}{{ item[currentTranslateKey].createdAt }}</span
                      >
                      <!-- </template> -->
                    </template>
                  </div>
                  <template v-else>
                    <div
                      class="flex justify-center items-center mb-[25px] text-4 text-[#858C9B] h-[18px] leading-[18px]"
                      v-if="item.custom_type === 'community'"
                    >
                      <span class="truncate whitespace-nowrap">{{
                        item[currentTranslateKey].category
                      }}</span>

                      <span class="mx-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
                      <svg-icon name="dashboard" class="w-4 h-4 mr-[2px]"></svg-icon>
                      <span class="mr-2">{{ item[currentTranslateKey].usageCount }}</span>
                      <span class="mx-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
                      <span class="truncate whitespace-nowrap">{{
                        item[currentTranslateKey].authorName
                      }}</span>
                    </div>
                    <div
                      class="flex justify-center items-center mb-[25px] text-4 text-[#858C9B] h-[18px] leading-[18px]"
                      v-else-if="item.category"
                    >
                      <span class="truncate whitespace-nowrap">{{ item.category }}</span>
                      <span class="mx-3 w-1 h-1 rounded-full bg-[#858C9B]"></span>
                      <svg-icon name="plaud_icon" class="w-4 h-4 mr-1"></svg-icon>
                      <span class="text-[12px]">PLAUD.AI</span>
                    </div>
                  </template>
                  <!-- 分割线 -->
                  <div class="line w-full h-[1px] bg-[#E4E7EC] mb-6"></div>

                  <div
                    class="rounded-[8px] mb-6 bg-[#f2f4f7] px-4 py-2 text-[#858c9b] text-[14px] leading-[19px] font-normal"
                    v-if="item.custom_type === 'community' && !item[currentTranslateKey]?.status"
                  >
                    <Translate
                      class="!justify-start"
                      :viewOriginal="currenViewOriginal"
                      @translateToOriginal="translateToOriginal"
                    ></Translate>
                  </div>
                  <!-- 内容 -->
                  <template v-if="item.custom_type === 'system'">
                    <div class="descrption text-[12px] text-[#858C9B] mb-[6px] leading-[18px]">
                      {{ t('description_text') }}
                    </div>
                    <div class="mb-6 text-[#1F1F1F] text-[14px] leading-[20px] font-normal">
                      {{ item.desc }}
                    </div>
                    <div class="prompt text-[12px] text-[#858C9B] mb-[6px] leading-[18px]">
                      {{ t('prompt_name') }}
                    </div>
                    <div class="flex-1 mb-[60px]">
                      <mavon-editor
                        style="min-height: 0px; border: none; font-size: 14px; line-height: 18px"
                        v-model="item.markdown"
                        placeholder="输入内容"
                        :boxShadow="false"
                        :subfield="false"
                        :toolbarsFlag="false"
                        fontSize="14px"
                        :tabSize="8"
                        previewBackground="transparent"
                        defaultOpen="preview"
                      />
                    </div>
                  </template>
                  <template v-else-if="item.custom_type === 'community'">
                    <div class="descrption text-[12px] text-[#858C9B] mb-[6px] leading-[18px]">
                      {{ t('description_text') }}
                    </div>
                    <div class="mb-6 text-[#1F1F1F] text-[14px] leading-[20px] font-normal">
                      {{ item[currentTranslateKey].description }}
                    </div>
                    <div class="prompt text-[12px] text-[#858C9B] mb-[6px] leading-[18px]">
                      {{ t('Custom_template_edit_prompt') }}
                    </div>

                    <div
                      class="flex-1 mb-[60px] text-[#1f1f1f] text-[15px] leading-[21px] font-normal"
                      v-html="getDefineStr(item[currentTranslateKey].content)"
                    ></div>

                    <!--  <div class="flex-1 mb-[60px]">
                      <mavon-editor
                        style="min-height: 0px; border: none; font-size: 14px; line-height: 18px"
                        v-model="item[currentTranslateKey].markdown"
                        placeholder="输入内容"
                        :boxShadow="false"
                        :subfield="false"
                        :toolbarsFlag="false"
                        fontSize="14px"
                        :tabSize="8"
                        previewBackground="transparent"
                        defaultOpen="preview"
                      />
                    </div> -->
                  </template>
                  <template v-else>
                    <div class="prompt text-[12px] text-[#858C9B] mb-[6px] leading-[18px]">
                      {{ t('Custom_template_edit_prompt') }}
                    </div>
                    <div
                      class="flex-1 mb-[60px] text-[#1f1f1f] text-[15px] leading-[21px] font-normal"
                      v-html="getDefineStr(item.content)"
                    ></div>
                  </template>
                </div>
              </div>
            </template>
          </MySwiper>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
    <ConformDailog
      ref="conformDailogRef"
      :title="t('confirm_delete_title')"
      :content="t('confirm_delete_content')"
      @handleConfirm="handleDeleteConfirm"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch, computed, nextTick, onBeforeUnmount } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import {
  DialogClose,
  DialogContent,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
} from 'radix-vue';
import MySwiper from '@/components/swiper/index.vue';
import { mavonEditor } from 'mavon-editor';
import 'mavon-editor/dist/css/index.css';
import { markdownSpeakerEditFormat } from '@/utils/markdown';
import { getFileDetailColor } from '@/utils/material-icons-collection';
import Translate from './translate.vue';
import { storage } from '@/utils/storage';
import ConformDailog from './conform-dailog.vue';
import loadingSpin from '@/components/loading-spin';
import { deleteCommunityTemplates } from '@/apis/templateCommunity';
import { getTranslateStr } from '@/utils/lang';

const viewOriginal = inject('viewOriginal');
const validateAuthUser = inject('validateAuthUser');
const showUpgradeModel = inject('showUpgradeModel');
const enableShareWidget = inject('enableShareWidget');

const emit = defineEmits([
  'toggleDialog',
  'showEdit',
  'handleEditMyTemplate',
  'submitToCommunity',
  'deleteSuccess',
  'handleUse',
]);

const props = defineProps({
  userid: {
    type: String,
    required: true,
  },
});

const dialogStatus = ref(false);
const currenViewOriginal = ref(false);
const windowWidth = ref(window.innerWidth);
const options = ref({
  slidesPerView: 1,
  slidesPerGroup: 1,
  centeredSlides: true,
  spaceBetween: 20,
  initialSlide: 0,
  // effect: 'fade',
  // fadeEffect: {
  //   crossFade: true,
  // },
  // speed: 500,
  // width: 560,
});

let currentIndex = ref(0);
let currentIds = ref({});
let listData = ref([]);
let contentBoxRef = ref(null);
let contentScrollTop = ref(0);
let previewShow = ref(true);
let fromType = ref('preview');
let deleteLoading = ref(false);
let conformDailogRef = ref(null);

let currentTranslateKey = computed(() => {
  return currenViewOriginal.value ? 'orignal' : 'translated';
});

watch(
  () => viewOriginal.value,
  (newVal) => {
    currenViewOriginal.value = newVal;
    // console.log('preview-gsy-currenViewOriginal:', currenViewOriginal.value);
  },
  { immediate: true },
);

// Add window resize handler
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

const cancelDialog = () => {
  emit('cancelDialog');
  previewShow.value = true;
  dialogStatus.value = false;
  initScrollInfo();
};

const hideDialog = () => {
  previewShow.value = false;
};

/**
 * 显示预览 status：
 * published: 已发布, 右上角显示"Update Template"按钮，走"submit to the community"流程
 * in_review: 审核中
 * new in review: 新提交审核
 * rejected: 审核不通过
 * unavailable: 不可用
 */
const showPreview = (data, index) => {
  console.log('showPreview-gsy:', data, index);
  options.value.initialSlide = index;
  dialogStatus.value = true;
  currentIndex.value = index;
  listData.value = data.map((item) => {
    if (item.custom_type === 'community') {
      if (item.orignal) {
        item.orignal.markdown = markdownSpeakerEditFormat(item.orignal.content);
      }
      if (item.translated) {
        item.translated.markdown = markdownSpeakerEditFormat(item.translated.content);
      }
    } else if (item.custom_type === 'system') {
      item.markdown = markdownSpeakerEditFormat(item.pre_markdown);
    } else {
      // item?.custom_status === 'saveAsTemplate'
      item.markdown = markdownSpeakerEditFormat(item.content);
    }
    return item;
  });
  // console.log('showPreview-gsy:', data, index);
};

const revertDialog = () => {
  previewShow.value = true;
};

const handleSwiperSlideClick = (item) => {
  // console.log('handleSwiperSlideClick-gsy:', item);
};

const initScrollInfo = () => {
  if (contentBoxRef.value) {
    contentBoxRef.value.scrollTop = 0;
    contentScrollTop.value = 0;
  }
};

const handleContentScroll = (e) => {
  contentScrollTop.value = e.target.scrollTop;
};

const translateToOriginal = (original) => {
  currenViewOriginal.value = original;
};

const handleSwiperSlideChange = (e) => {
  contentScrollTop.value = 0; // 直接设置值为0
  currenViewOriginal.value = viewOriginal.value;
  nextTick(() => {
    if (contentBoxRef.value) {
      contentBoxRef.value.scrollTop = 0;
    }
  });
  // console.log('handleSwiperSlideChange-gsy:', e);
};

const getDefineStr = (str) => {
  if (!str) return str;
  return str.replace(/\n/g, '<br/>');
};

// 校验用户是否是会员，如果不是则关闭preview弹窗, 并弹出会员窗
const validateAuthUserAction = () => {
  if (!validateAuthUser()) {
    cancelDialog();
    return false;
  }
  return true;
};

const handleTryIt = (item) => {
  let { custom_type } = item;
  let nofreeFlag = false;
  if (custom_type === 'community') {
    let { isLocked, id } = item[currentTranslateKey.value];
    item.id = id;
    nofreeFlag = isLocked;
  } else {
    nofreeFlag = item.isLocked;
  }
  // console.log('handleTryIt-gsy-item:', item);
  // console.log('handleTryIt-gsy-nofreeFlag:', nofreeFlag);
  if (nofreeFlag) {
    if (!validateAuthUserAction()) return;
  }

  // console.log('handleTryIt-gsy-props.userid:', props.userid);
  storage.set('tryItTemplate', JSON.stringify(item), props.userid, 'NoExpiration');
  emit('tryItShow');
};

const handleUse = (item, index) => {
  console.log('handleUse-gsy-item:', item, index);
  if (item?.isLocked || item[currentTranslateKey.value]?.isLocked) {
    showUpgradeModel();
    cancelDialog();
    return;
  }
  emit('handleUse', { data: item, index });
  cancelDialog();
};

const handleEdit = (item, custom_status) => {
  if (!validateAuthUserAction()) return;
  let id = '';
  let editData = {};
  // console.log('handleEdit-gsy:', item, custom_status);
  if (item.custom_type === 'community') {
    // editData = JSON.parse(JSON.stringify(item[currentTranslateKey.value]));
    editData = JSON.parse(JSON.stringify(item.orignal));
  } else {
    editData = JSON.parse(JSON.stringify(item));
  }
  editData.custom_status = custom_status;
  editData.from = fromType.value;
  // console.log('editData-gsy:', editData);
  previewShow.value = false;
  emit('showEdit', editData);
};

const submitToCommunity = (item) => {
  if (!validateAuthUserAction()) return;
  // console.log('submitToCommunity-gsy:', item);
  emit('submitToCommunity', { ...item, custom_status: 'create', from: 'preview' });
};

const handleApiError = (error) => {
  if (error.status === -1) {
    console.log('no permission');
  } else if (error.status === -2) {
    console.log(t('Custom_template_toast_maxlimit'));
  }
};

// 点击"delete"按钮
const handleDelete = (item) => {
  // console.log('handleDelete-gsy-item:', item);
  let { templateId, versionId } = item[currentTranslateKey.value];
  currentIds.value = {
    template_id: templateId,
    version_id: versionId,
  };
  conformDailogRef.value.show();
};

// 确认删除
const handleDeleteConfirm = async () => {
  // console.log('handleDeleteConfirm-gsy:');
  deleteLoading.value = true;
  try {
    let res = await deleteCommunityTemplates(currentIds.value);
    dialogStatus.value = false;
    emit('deleteSuccess', 'community');
  } catch (error) {
    handleApiError(error);
  } finally {
    deleteLoading.value = false;
  }
};

onMounted(() => {
  initScrollInfo();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});

defineExpose({
  showPreview,
  revertDialog,
  cancelDialog,
  hideDialog,
});
</script>

<style scoped lang="less"></style>

<style lang="less">
@import '@/assets/styles/mavon-view.less';

.preview-swiper {
  .swiper-button-next {
    position: absolute !important;
    right: -90px !important;
  }

  .swiper-button-prev {
    position: absolute !important;
    left: -90px !important;
  }
}

.top-box {
  transition: all 0.3s ease-in-out;
}
</style>
