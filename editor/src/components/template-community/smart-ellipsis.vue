<template>
  <div ref="containerRef" class="smart-ellipsis">
    <div ref="titleRef" class="title text-[16px] font-semibold text-[#1f1f1f] mb-1 line-clamp-3">
      {{ textOne }}
    </div>
    <template v-if="type === 'community'">
      <div
        v-if="!isTitleOverflow"
        ref="descRef"
        class="description text-[13px] text-[#98a2b3] leading-[18px]"
        :style="{ WebkitLineClamp: descClamp }"
      >
        {{ textTwo }}
      </div>
    </template>
    <template v-else>
      <div
        class="keyWords flex items-center flex-wrap"
        v-if="!isTitleOverflow && textThree && textThree.length > 0"
        :style="{ WebkitLineClamp: descClamp }"
      >
        <div
          class="wordItem rounded-[4px] whitespace-nowrap px-1 h-4 leading-4 mr-2 mb-1 bg-[#f2f4f7] text-[#667085] text-[10px] font-medium"
          v-for="w in textThree"
          :key="w"
        >
          {{ w }}
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted, watchEffect, nextTick } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'community',
  },
  textOne: {
    type: String,
    default: '',
  },
  textTwo: {
    type: String,
    default: '',
  },
  textThree: {
    type: Array,
    default: [],
  },
  // 假设每行高为 20px，可根据实际字体大小微调
  lineHeight: {
    type: Number,
    default: 20,
  },
  maxLines: {
    type: Number,
    default: 3,
  },
});

const titleRef = ref(null);
const descRef = ref(null);
const containerRef = ref(null);

const isTitleOverflow = ref(false);
const descClamp = ref(0);

watchEffect(() => [props.title, props.description], updateClamp);
const updateClamp = () => {
  nextTick(() => {
    const titleHeight = titleRef.value?.clientHeight || 0;
    const titleLines = Math.round(titleHeight / props.lineHeight);

    if (titleLines >= props.maxLines) {
      isTitleOverflow.value = true;
    } else {
      isTitleOverflow.value = false;
      descClamp.value = props.maxLines - titleLines;
    }
  });
};

onMounted(updateClamp);
</script>

<style scoped lang="less">
.smart-ellipsis {
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: hidden;
}
.title,
.description {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  line-height: 20px;
}
.title {
  -webkit-line-clamp: 3;
}
.description {
  -webkit-line-clamp: 1; /* 默认先1，js计算后覆盖 */
}
</style>
