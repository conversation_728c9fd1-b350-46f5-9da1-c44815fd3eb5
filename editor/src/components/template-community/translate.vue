<!-- translate -->
<template>
  <div class="flex items-center justify-center py-[2px] text-[14px] leading-[20px] text-[#858C9B]">
    <span class="inline-flex items-center">
      <svg-icon name="translate_icon" class="w-4 h-4 mr-1"></svg-icon>
      <div v-html="text"></div>
      <button
        class="ml-1 underline cursor-pointer text-black"
        data-pld="community-template-translate-click"
        @click="toggleTranslate"
      >
        {{ buttonText }}
      </button>
    </span>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, inject } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import { getTranslateStr } from '@/utils/lang';
import { throttle } from 'lodash-es';

const props = defineProps({
  viewOriginal: {
    type: Boolean,
    default: false,
  },
});
const oSLanguageName = inject('oSLanguageName');

let text = ref(false);
let buttonText = ref(false);

const emit = defineEmits(['translateToOriginal']);

watch(
  () => props.viewOriginal,
  (newValue) => {
    text.value = newValue
      ? t('translate_two_text')
      : getTranslateStr('translate_one_text', '%s', oSLanguageName.value);
    buttonText.value = newValue ? t('translate_two_button') : t('translate_one_button');
  },
  { immediate: true },
);

const toggleTranslate = throttle(
  () => {
    emit('translateToOriginal', !props.viewOriginal);
  },
  1000,
  { trailing: false },
);

onMounted(() => {});
</script>

<style scoped lang="less"></style>
