<!-- weekly trending -->
<template>
  <keep-alive>
    <div class="weekly-wrapper">
      <TitleBlock class="px-10" :data="t('weekly_trending')" />
      <MySwiper
        class="weekly-swiper"
        :data="currentData"
        :options="options"
        :needSwiperWrapper="true"
      >
        <template #default="{ item, index }">
          <!-- class="my-swiper-item ml-[20px] h-[160px] relative min-w-[240px] max-w-[320px] rounded-[14px] mb-6 mt-4 bg-white" -->

          <div
            class="my-swiper-item ml-[20px] h-[160px] relative max-w-[320px] rounded-[14px] mb-6 mt-4 bg-white"
            :class="{ 'mr-[20px]': index === currentData.length - 1 }"
            :data-pld="`community-template-weekly-trending-click-${item.id}`"
            @click="handleSwiperSlideClick(currentData, index)"
          >
            <CardBox
              :data="item.custom_type === 'community' ? item[translateKey] : item"
              :index="index"
              :showIndex="true"
            ></CardBox>
          </div>
        </template>
      </MySwiper>
    </div>
  </keep-alive>
</template>

<script setup>
import { ref, onMounted, inject, watch } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import TitleBlock from './title-block.vue';
import CardBox from './card-box.vue';
import MySwiper from '@/components/swiper/index.vue';
import { getWeeklyRecommend } from '@/apis/templateCommunity';
import { findIconCodeToName, convertToHexColor } from '@/utils/material-icons-collection.js';
const props = defineProps({});

const oSLanguageId = inject('oSLanguageId');
const translateKey = inject('translateKey');

const options = ref({
  // autoplay: {
  //   delay: 3000,
  //   pauseOnMouseEnter: true,
  // },
  slidesPerView: 4.2,
  slidesPerGroup: 4,
});

let currentData = ref([]);

const emit = defineEmits(['showPreview']);
const processWeeklyData = (item) => {
  if (!item?.latest_published_version || !item?.translated_published_version) {
    console.warn('Invalid item data:', item);
    return null;
  }

  const baseTemplateData = {
    custom_type: 'community',
    authorName: item.author_name || '',
    usageCount: item.usage_count_last_week || 0,
    isLocked: item?.is_locked === 1 || false,
  };

  const processVersion = (version) => ({
    ...baseTemplateData,
    iconColor: convertToHexColor(version.icon_color),
    iconName: findIconCodeToName(version.icon),
    title: version.title || '',
    description: version.description || '',
    id: version.id,
    category: version.category || '',
    category_id: version.category_id,
    category_icon: version.category_icon || '',
    content: version.content || '',
  });

  return {
    custom_type: 'community',
    orignal: processVersion(item.latest_published_version),
    translated: processVersion(item.translated_published_version),
  };
};

const getWeeklyData = async () => {
  try {
    const res = await getWeeklyRecommend({
      language_os: oSLanguageId.value,
    });

    if (!res?.data) {
      console.warn('No data received from weekly recommend API');
      currentData.value = [];
      return;
    }
    currentData.value = (res.data || []).map((list) => processWeeklyData(list)).filter(Boolean);
    // console.log('currentData.value-weekly:', currentData.value);
  } catch (error) {
    console.error('Failed to fetch weekly data:', error);
    currentData.value = [];
    // 可以在这里添加错误提示UI
  }
};

const handleSwiperSlideClick = (data, index) => {
  // console.log('handleSwiperSlideClick:', data, index);
  emit('showPreview', data, index);
};

onMounted(() => {
  getWeeklyData();
});
</script>

<style lang="less">
.weekly-swiper {
  .swiper-button-next {
    right: -5px !important;
  }
  .swiper-button-prev {
    left: 0px !important;
  }
}
</style>
