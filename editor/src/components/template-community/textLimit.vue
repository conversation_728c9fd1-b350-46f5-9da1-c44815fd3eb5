<!-- 模版 -->
<template>
  <div class="flex justify-end items-center text-[12px] text-[#999]">
    {{ inputLength }} / {{ maxLength }}
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;

const props = defineProps({
  inputText: {
    type: String,
    default: '',
  },
  maxLength: {
    type: Number,
    default: 0,
  },
});

const inputLength = computed(() => {
  return props.inputText?.length || 0;
});

onMounted(() => {});
</script>

<style scoped lang="less"></style>
