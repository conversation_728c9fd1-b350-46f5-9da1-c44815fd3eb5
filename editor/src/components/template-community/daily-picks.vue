<!-- 模版社区-top -->
<template>
  <div
    class="daily-wrapper text-black3 w-full text-white cursor-pointer relative"
    v-if="currentData.length > 0 || isLoading"
  >
    <Loading v-if="isLoading" />
    <template v-else>
      <div class="topinfo px-10">
        <div class="title text-[20px] text-black3 leading-[30px] mb-1 font-bold">
          {{ t('template_community_daily_title') }}
        </div>
        <div class="desc text-[12px] text-[#858C9B] leading-[18px]">
          {{ t('template_community_daily_description_one') }}—{{
            t('template_community_daily_description_two')
          }}
        </div>
      </div>
      <MySwiper
        class="daily-swiper"
        :data="currentData"
        :options="options"
        :needSwiperWrapper="true"
      >
        <template #default="{ item, index }">
          <!-- min-w-[320px] max-w-[440px] -->
          <div
            class="my-swiper-item ml-[20px] flex flex-col relative flex-1 rounded-[14px] mb-6 mt-4 shadow-[0_0_32px_0_rgba(0,0,0,0.10)]"
            :class="{ 'mr-[20px]': index === currentData.length - 1 }"
            data-pld="community-template-daily-picks-click"
            @click="handleSwiperSlideClick(currentData, index)"
          >
            <img
              class="w-full h-[200px] object-cover object-center rounded-tl-[14px] rounded-tr-[14px]"
              :src="item[translateKey].bannerUrl"
            />
            <div class="absolute top-4 left-4 mr-4">
              <div class="date text-[12px] leading-[18px]">
                {{ formatDate(item[translateKey].recommendDate) }}
              </div>
              <div class="date-desc text-[16px] leading-[24px] font-semibold line-clamp-1">
                {{ item[translateKey].reason }}
              </div>
            </div>
            <div class="flex px-4 py-[10px] justify-between items-center">
              <div class="flex items-center min-w-0 flex-1">
                <span
                  class="material-symbols-rounded mr-4 flex-shrink-0"
                  :style="{
                    color: item[translateKey].iconColor,
                  }"
                  >{{ item[translateKey].iconName }}</span
                >
                <div class="flex flex-col min-w-0">
                  <div
                    class="template-name truncate text-[17px] leading-[22px] text-black3 font-semibold"
                  >
                    {{ item[translateKey].title }}
                  </div>
                  <div class="author-name truncate text-[13px] text-[#858C9B] leading-[18px]">
                    {{ item[translateKey].authorName }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </MySwiper>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, onBeforeMount, watch } from 'vue';
import { i18n } from '../../locales/i18n';
const { t } = i18n.global;
import MySwiper from '@/components/swiper/index.vue';
import { findIconCodeToName, convertToHexColor } from '@/utils/material-icons-collection.js';
import { getDailyRecommend } from '@/apis/templateCommunity';
import Loading from '@/components/loading/index.vue';

const props = defineProps({});

const oSLanguageId = inject('oSLanguageId');
const translateKey = inject('translateKey');

const options = ref({
  // autoplay: {
  //   delay: 3000,
  //   pauseOnMouseEnter: true,
  // },
  slidesPerView: 3.1,
  slidesPerGroup: 3,
  spaceBetween: 0,
  // breakpoints: {
  //   1024: {
  //     slidesPerView: 1,
  //   },
  // },
});

let currentData = ref([]);
let isLoading = ref(true);

const emit = defineEmits(['showPreview']);

const formatDate = (dateStr) => {
  return dateStr ? new Date(dateStr).FormatTxt() : '';
};
const handleSwiperSlideClick = (data, index) => {
  // console.log('handleSwiperSlideClick:', data);
  emit('showPreview', data, index);
};

const processDailyData = (item) => {
  const baseTemplateData = {
    authorName: item.author_name,
    bannerUrl: item.banner_url,
    reason: item.reason,
    recommendDate: item.recommend_date,
    usageCount: item.usage_count,
    isLocked: item.is_locked === 1 || false,
  };
  return {
    custom_type: 'community',
    orignal: {
      ...baseTemplateData,
      ...item.latest_published_version,
      iconColor: convertToHexColor(item.latest_published_version.icon_color),
      iconName: findIconCodeToName(item.latest_published_version.icon),
      status: '',
    },
    translated: {
      ...baseTemplateData,
      ...item.translated_published_version,
      iconColor: convertToHexColor(item.translated_published_version.icon_color),
      iconName: findIconCodeToName(item.translated_published_version.icon),
      status: '',
    },
  };
};

const getDailyData = async () => {
  isLoading.value = true;
  try {
    const res = await getDailyRecommend({
      language_os: oSLanguageId.value,
    });
    currentData.value = (res?.data || []).map((item) => processDailyData(item));
  } catch (error) {
    console.log(error);
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  // console.log('daily-picks-gsy-translateKey:', translateKey.value);
  getDailyData();
});
</script>

<style lang="less" scoped>
.daily-wrapper {
}
</style>
