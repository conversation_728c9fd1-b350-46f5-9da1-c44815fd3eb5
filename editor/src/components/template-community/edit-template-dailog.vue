<!-- 模版编辑 -->
<template>
  <div>
    <DialogRoot v-model:open="dialogStatus">
      <DialogPortal>
        <DialogOverlay
          @pointerdown.stop
          class="z-[9999] edit-template-overlay data-[state=open]:animate-overlayShow fixed inset-0 bg-black/40 backdrop-blur-2xl"
          :class="{ '!opacity-0': bgTransparent }"
        />
        <DialogContent
          class="audio-dailog fixed bottom-0 left-[50%] w-full translate-x-[-50%] bg-transparent focus:outline-none z-[99999] flex flex-col"
          :class="{
            'top-[9vh] h-[91vh]': windowWidth <= 1440,
            'top-[10vh] h-[90vh]': windowWidth > 1440 && windowWidth <= 1600,
            'top-[20vh] h-[80vh]': windowWidth > 1600,
          }"
        >
          <div
            v-if="selectOpen"
            @click="selectOpen = false"
            class="pointer-events-auto fixed top-0 left-0 bottom-0 right-0 w-full h-screen z-[99999] bg-transparent"
          ></div>
          <div
            class="pl-0 !min-w-[560px] !w-[29vw] mx-auto bg-[#F2F4F7] rounded-t-[20px] relative"
            :style="{
              height: windowWidth <= 1440 ? '91vh' : windowWidth <= 1600 ? '90vh' : '80vh',
            }"
          >
            <!-- 顶部 -->
            <div class="rounded-t-[20px] absolute top-0 left-0 right-0 z-[100]">
              <div class="info-box flex items-center justify-between px-4 pt-4 mb-4">
                <!-- 左侧按钮 -->
                <div class="flex items-center">
                  <template v-if="info.custom_status === 'create' && nextFlag">
                    <svg-icon name="back_button" class="w-7 h-7" @click="goBack"></svg-icon>
                  </template>
                  <template v-else>
                    <!-- cancel 按钮 -->
                    <button
                      class="bg-black/5 text-gray-800 px-4 py-1 rounded-full font-medium"
                      @click="cancelDialog(false)"
                    >
                      {{ t('Device_BLEclose_button_left') }}
                    </button>
                    <!-- delete 按钮 -->
                    <button
                      v-if="info.custom_status === 'saveAsTemplate' && info.id"
                      class="ml-[6px] bg-black/5 text-[#FF3B30] px-4 py-1 rounded-full font-medium flex items-center"
                      :data-pld="`community-template-success-delete-${info.id}`"
                      @click="handleDelete"
                    >
                      <!-- <loadingSpin class="mr-1 !w-4 !h-4" v-if="deleteLoading"></loadingSpin> -->
                      {{ t('CropAudio_bottom_action_Delete') }}
                    </button>
                  </template>
                </div>

                <!-- 标题 -->
                <div
                  class="flex-1 text-center font-semibold text-[14px] leading-[20px] absolute left-0 right-0 pointer-events-none"
                >
                  <template v-if="info.custom_status === 'saveAsTemplate'">
                    {{ t('Custom_template_edit_title') }}
                  </template>
                  <template v-else-if="info.custom_status === 'create'">
                    {{
                      t(
                        `${nextFlag ? 'fill_in_the_template_text' : 'confirm_template_information_text'}`,
                      )
                    }}
                  </template>
                  <template v-else>
                    {{ t('Custom_template_edit_btn_create') }}
                  </template>
                </div>

                <!-- 右侧按钮 -->
                <div class="flex items-center">
                  <template v-if="info.custom_status === 'create'">
                    <button
                      v-if="!nextFlag"
                      class="bg-black text-white px-4 py-1 rounded-full"
                      @click="handleNext"
                    >
                      {{ t('Me_account_deleteone_button') }}
                    </button>
                    <button
                      v-else
                      class="bg-black text-white px-4 py-1 rounded-full flex items-center"
                      data-pld="community-template-success-submit"
                      @click="handleSubmit"
                    >
                      <!-- <loadingSpin class="mr-1 !w-4 !h-4" v-if="submitLoading"></loadingSpin> -->
                      {{ t('submit_text') }}
                    </button>
                  </template>

                  <button
                    v-else
                    class="bg-black text-white px-4 py-1 rounded-full font-medium flex items-center"
                    data-pld="community-template-save"
                    @click="saveDefine(data)"
                  >
                    <!-- <loadingSpin class="mr-1 !w-4 !h-4" v-if="saveDefineLoading"></loadingSpin> -->
                    {{ t('Custom_template_edit_btn_save') }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 内容 -->
            <div
              v-show="!nextFlag"
              :class="`content-box h-full overflow-y-auto absolute px-6 pt-4 pb-[12px] top-[44px] left-0 right-0 z-[10] ${contentScrollTop > 0 ? 'bg-white' : ''}`"
              ref="contentBoxRef"
            >
              <div
                ref="saveAsTemplateRef"
                v-if="info.custom_status === 'personalize'"
                class="bg-[#E4E7EC] text-[#858C9B] rounded-[8px] px-4 py-2 text-[13px] leading-[18px] px-3 py-[6px] mb-6"
              >
                {{ t('save_as_template_text') }}
              </div>

              <!-- 根据审核status显示不同的内容 -->
              <template v-if="info.custom_status === 'create'">
                <div
                  ref="descriptionTipRef"
                  v-if="info[currentTranslateKey]?.status === 'published'"
                  class="description-tip text-[12px] leading-[18px] py-[6px] px-3 mb-6 text-[#858C9B] border border-[#D0D5DD] bg-[#E4E7EC] rounded-[8px]"
                >
                  {{ t('description_published_tip') }}
                </div>
              </template>

              <!-- :text="getTranslateStr('Audio_File_Length_Limitation_content', '%s', maxHour)" -->

              <div class="defineForm">
                <!-- Template Name -->
                <div class="">
                  <label
                    class="flex items-center h-[18px] overflow-hidden text-[12px] leading-[18px] font-semibold text-[#1F1F1F] mb-[6px]"
                  >
                    {{ t('template_name') }}
                    <svg-icon name="star_icon" class="w-3 h-3 text-red-500"></svg-icon>
                  </label>

                  <div class="flex items-center">
                    <!-- 图标 -->
                    <div
                      class="relative cursor-pointer w-[44px] h-[44px] rounded-[14px] mr-[6px] bg-white flex items-center justify-center flex-shrink-0 overflow-hidden"
                      v-if="info.custom_status === 'create'"
                    >
                      <IconPicker
                        :iconColor="info?.iconColor || ''"
                        :iconName="info?.iconName || ''"
                        ref="iconPickerRef"
                      ></IconPicker>
                    </div>
                    <!-- 输入框 -->
                    <input
                      type="text"
                      v-model="info.title"
                      class="w-full rounded-[14px] bg-white px-4 py-3 text-[14px] leading-5 text-[#1F1F1F] outline-none"
                      @input="changeIpt('title')"
                      :maxlength="titleMaxlength"
                      :placeholder="t('template_name_placeholder_text')"
                    />
                  </div>
                  <div class="flex justify-between items-center">
                    <div
                      class="formError text-[14px] leading-5 text-red-500 mt-1"
                      :class="{ visible: formError.title && formError.title.status }"
                    >
                      {{ formError.title ? formError.title.msg : '' }}
                    </div>
                    <TextLimit class="mt-1" :inputText="info.title" :maxLength="titleMaxlength" />
                  </div>
                </div>

                <!-- Prompt -->
                <div class="mb-10">
                  <label
                    class="flex items-center h-[18px] overflow-hidden text-[12px] leading-[18px] font-semibold text-[#1F1F1F] mb-[6px]"
                  >
                    {{ t('prompt_name') }}
                    <svg-icon name="star_icon" class="w-3 h-3 text-red-500"></svg-icon>
                  </label>

                  <template v-if="info.custom_status === 'saveAsTemplate'">
                    <PopoverRoot>
                      <PopoverTrigger>
                        <div class="text-xs text-[#007AFF] mb-2 leading-[18px]">
                          {{ t('Custom_template_edit_prompt_sub') }}
                        </div>
                      </PopoverTrigger>
                      <PopoverPortal>
                        <PopoverContent
                          side="right"
                          align="end"
                          :side-offset="80"
                          style="z-index: 99999"
                          class="rounded-[20px] mt-[70px] w-[560px] bg-white shadow-[0_10px_38px_-10px_hsla(206,22%,7%,.35),0_10px_20px_-15px_hsla(206,22%,7%,.2)] focus:shadow-[0_10px_38px_-10px_hsla(206,22%,7%,.35),0_10px_20px_-15px_hsla(206,22%,7%,.2),0_0_0_2px_theme(colors.green7)] will-change-[transform,opacity] data-[state=open]:data-[side=top]:animate-slideDownAndFade data-[state=open]:data-[side=right]:animate-slideLeftAndFade data-[state=open]:data-[side=bottom]:animate-slideUpAndFade data-[state=open]:data-[side=left]:animate-slideRightAndFade"
                        >
                          <!-- src="https://plaud-web-dist-test.pages.dev/common.html#/templates_help?from=web" -->
                          <iframe
                            src="/common.html#/templates_help?from=web"
                            frameborder="0"
                            style="width: 100%; height: 70vh; border-radius: 20px"
                          ></iframe>
                          <PopoverClose asChild>
                            <svg-icon
                              name="close_icon"
                              class="w-6 h-6 absolute top-[5px] right-2 cursor-pointer"
                            ></svg-icon>
                          </PopoverClose>
                        </PopoverContent>
                      </PopoverPortal>
                    </PopoverRoot>
                  </template>
                  <template v-else>
                    <div class="text-xs text-gray-400 mb-2">
                      {{ t('save_as_template_tip') }}
                    </div>
                  </template>

                  <textarea
                    ref="contentTextarea"
                    v-model="info.content"
                    class="w-full rounded-xl bg-white px-[14px] py-4 text-[14px] text-[#1F1F1F] leading-[20px] outline-none"
                    :style="{ height: textareaHeight }"
                    @input="changeIpt('content')"
                    :maxlength="contentMaxLength"
                    :placeholder="t('prompt_placeholder_text')"
                  ></textarea>
                  <div class="flex justify-between items-center">
                    <div
                      class="formError text-[14px] leading-5 text-red-500 mt-1"
                      :class="{ visible: formError.content && formError.content.status }"
                    >
                      {{ formError.content ? formError.content.msg : '' }}
                    </div>
                    <TextLimit :inputText="info.content" :maxLength="contentMaxLength" />
                  </div>
                </div>
              </div>
            </div>

            <!-- NEXT内容 -->
            <div
              v-if="info.custom_status === 'create' && nextFlag"
              :class="`content-box h-full overflow-y-auto absolute px-6 pt-4 pb-[12px] top-[44px] left-0 right-0 z-[10] ${contentScrollTop > 0 ? 'bg-white' : ''}`"
              ref="contentBoxRef"
            >
              <div class="defineForm">
                <!-- description -->
                <div class="">
                  <div class="description-box" ref="descriptionBoxRef">
                    <label
                      class="flex items-center h-[18px] overflow-hidden text-[12px] leading-[18px] font-semibold text-[#1F1F1F]"
                    >
                      <span>{{ t('description_text') }}</span>
                      <svg-icon name="star_icon" class="w-3 h-3 text-red-500"></svg-icon>
                    </label>
                    <div class="description-tip text-[12px] text-[#858C9B] leading-[18px] mb-[6px]">
                      {{ t('description_tip_text') }}
                    </div>
                  </div>

                  <div class="flex items-center">
                    <!-- 输入框 -->
                    <textarea
                      ref="descriptionTextarea"
                      v-model="info.description"
                      class="w-full rounded-xl bg-white px-[14px] py-4 text-[14px] text-[#1F1F1F] leading-[20px] outline-none min-h-[180px]"
                      @input="changeIpt('description')"
                      :maxlength="descriptionMaxlength"
                      :placeholder="t('community_template_edit_description_error')"
                    ></textarea>
                  </div>
                  <div class="flex justify-between items-center">
                    <div
                      class="formError text-[14px] leading-5 text-red-500 mt-1"
                      :class="{ visible: formError.description && formError.description.status }"
                    >
                      {{ formError.description ? formError.description.msg : '' }}
                    </div>
                    <TextLimit
                      class="mt-1"
                      :inputText="info.description"
                      :maxLength="descriptionMaxlength"
                    />
                  </div>
                </div>

                <!-- category -->
                <div class="mb-6">
                  <label
                    class="flex items-center h-[18px] overflow-hidden text-[12px] leading-[18px] font-semibold text-[#1F1F1F] mb-[6px]"
                  >
                    {{ t('category_text') }}
                    <svg-icon name="star_icon" class="w-3 h-3 text-red-500"></svg-icon>
                  </label>

                  <div class="category-select relative">
                    <SelectRoot v-model="info.category" :open="selectOpen">
                      <SelectTrigger
                        class="pointer-events-auto z-[9999] my-select relative w-full h-[48px] rounded-[14px] border border-solid focus:shadow-none focus:outline-none px-4 text-[14px] text-[#1F1F1F] bg-white flex items-center justify-between data-[placeholder]:text-[#858C9B] transition-colors"
                        aria-label="Customise options"
                        @click="handleSelectOpen"
                      >
                        <div v-if="info?.category && !categoryNames.includes(info?.category)">
                          {{ info?.category }}
                        </div>
                        <SelectValue :placeholder="t('community_template_edit_category_error')" />
                        <svg-icon
                          name="chevron_down"
                          class="w-6 h-6 absolute right-4 top-1/2 -translate-y-1/2"
                        ></svg-icon>
                      </SelectTrigger>

                      <SelectPortal>
                        <SelectContent
                          class="z-[99999] min-w-[512px] max-h-[300px] bg-white rounded-[14px] shadow-[0px_10px_38px_-10px_rgba(22,_23,_24,_0.35),_0px_10px_20px_-15px_rgba(22,_23,_24,_0.2)] will-change-[opacity,transform] data-[side=top]:animate-slideDownAndFade data-[side=right]:animate-slideLeftAndFade data-[side=bottom]:animate-slideUpAndFade data-[side=left]:animate-slideRightAndFade"
                          position="popper"
                          side="bottom"
                          align="start"
                          side-offset="0.8"
                          @click="selectOpen = false"
                        >
                          <SelectViewport class="p-[5px]">
                            <SelectGroup>
                              <SelectItem
                                v-for="(option, index) in currentCategoryData"
                                :key="index"
                                class="mx-1 mb-1 px-2 h-[30px] leading-[30px] rounded-[10px] text-[14px] text-[#1F1F1F] hover:bg-[#F2F4F7] cursor-pointer data-[highlighted]:outline-none data-[highlighted]:border-none data-[highlighted]:bg-[#F2F4F7]"
                                :class="{ 'bg-[#F2F4F7]': option.category === info?.category }"
                                :value="option.category"
                              >
                                <SelectItemText>
                                  {{ option.category }}
                                </SelectItemText>
                              </SelectItem>
                            </SelectGroup>
                          </SelectViewport>
                        </SelectContent>
                      </SelectPortal>
                    </SelectRoot>
                  </div>

                  <div
                    class="formError text-[14px] leading-5 text-red-500 mt-1"
                    :class="{ visible: formError.category && formError.category.status }"
                  >
                    {{ formError.category ? formError.category.msg : '' }}
                  </div>
                </div>

                <!-- Email -->
                <div class="">
                  <label
                    class="flex items-center h-[18px] overflow-hidden text-[12px] leading-[18px] font-semibold text-[#1F1F1F] mb-[6px]"
                  >
                    {{ t('creator_email_ address') }}
                    <svg-icon name="star_icon" class="w-3 h-3 text-red-500"></svg-icon>
                  </label>
                  <div class="description-tip text-[12px] text-[#858C9B] leading-[18px] mb-[6px]">
                    {{ t('description_tip_text') }}
                  </div>

                  <div class="flex items-center">
                    <!-- 输入框 -->
                    <input
                      type="text"
                      v-model="info.author_email"
                      class="w-full rounded-[14px] bg-white px-4 py-3 text-[14px] leading-5 text-[#1F1F1F] outline-none"
                      @input="changeIpt('author_email')"
                      :placeholder="t('community_template_edit_email_error')"
                    />
                  </div>
                  <div
                    class="formError text-[14px] leading-5 text-red-500 mt-1"
                    :class="{ visible: formError.author_email && formError.author_email.status }"
                  >
                    {{ formError.author_email ? formError.author_email.msg : '' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
    <ConformDailog
      ref="conformDailogRef"
      :title="t('confirm_delete_title')"
      :content="t('confirm_delete_content')"
      @handleConfirm="handleDeleteConfirm"
    />
    <!-- 社区成功弹窗 -->
    <CommunitySuccessDailog
      ref="communitySuccessDailogRef"
      :isEdit="isEditCommunity"
    ></CommunitySuccessDailog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, inject, watch, computed, nextTick } from 'vue';
import IconPicker from './icon-picker.vue';
import loadingSpin from '@/components/loading-spin';
import { getTranslateStr } from '@/utils/lang';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import CommunitySuccessDailog from './community-success-dailog.vue';
import { getCategory } from '@/apis/templateCommunity';
import { storage } from '@/utils/storage';
import TextLimit from './textLimit.vue';

import {
  DialogClose,
  DialogContent,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  PopoverArrow,
  PopoverClose,
  PopoverContent,
  PopoverPortal,
  PopoverRoot,
  PopoverTrigger,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectItemText,
  SelectLabel,
  SelectPortal,
  SelectRoot,
  SelectTrigger,
  SelectValue,
  SelectViewport,
} from 'radix-vue';
// import { mavonEditor } from 'mavon-editor';
// import 'mavon-editor/dist/css/index.css';
import { markdownSpeakerEditFormat } from '@/utils/markdown';

import { findIconToCode, findColorToKey } from '@/utils/material-icons-collection';
import { toast } from 'sonner';
import ConformDailog from './conform-dailog.vue';
import {
  editCustomtemplates,
  createCustomtemplates,
  createCommunityTemplates,
  deleteCustomtemplates,
  editCommunityTemplates,
} from '@/apis/templateCommunity';
import ToastInfo from './toast-info.vue';

const viewOriginal = inject('viewOriginal');
const oSLanguageId = inject('oSLanguageId');
const enableShareWidget = inject('enableShareWidget');

const emit = defineEmits([
  'toggleDialog',
  'tryItShow',
  'saveSuccess',
  'cancelDialog',
  'saveCommunitySuccess',
]);

const props = defineProps({
  userid: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    default: '',
  },
});

const dialogStatus = ref(false);
const info = ref({
  title: '',
  content: '',
  id: '',
  custom_type: '',
  orignal: null,
  translated: null,
  iconColor: '',
  iconName: '',
});
const currenViewOriginal = ref(false);

let listData = ref([]);
const contentBoxRef = ref(null);
const contentScrollTop = ref(0);
let formError = ref({});
let isLoading = ref(false);
let contentMaxLength = ref(5000);
let titleMaxlength = ref(255);
const windowWidth = ref(window.innerWidth);

// let contentMaxLength = ref(5);
// let titleMaxlength = ref(5);
let nextFlag = ref(false);
let descriptionMaxlength = ref(100);

const contentTextarea = ref(null);
let bgTransparent = ref(true);
let iconPickerRef = ref(null);
let conformDailogRef = ref(null);
let deleteLoading = ref(false);
let saveDefineLoading = ref(false);
let submitLoading = ref(false);
let toastDeleteMsg = ref(t('delete_success_tip'));
let fromType = ref('');
let communitySuccessDailogRef = ref(null);
let isEditCommunity = ref(false);
let currentCategoryData = ref([]);
let categoryNames = ref([]);
let selectOpen = ref(false);
const descriptionTipRef = ref(null);
const descriptionBoxRef = ref(null);
const saveAsTemplateRef = ref(null);

const currentTranslateKey = computed(() => {
  return currenViewOriginal.value ? 'orignal' : 'translated';
});

// 计算 textarea 的动态高度
const textareaHeight = computed(() => {
  let baseHeight = 235;
  let descriptionTipHeight = 0;
  let descriptionBoxHeight = 0;
  let saveAsTemplateHeight = 0;

  // 如果 descriptionTipRef 存在且可见，获取其高度
  if (descriptionTipRef.value) {
    descriptionTipHeight = descriptionTipRef.value.offsetHeight + 24;
  }

  // 如果 descriptionBoxRef 存在且可见，获取其高度
  if (descriptionBoxRef.value) {
    descriptionBoxHeight = descriptionBoxRef.value.offsetHeight + 6;
  }
  // 如果 saveAsTemplateRef 存在且可见，获取其高度
  if (saveAsTemplateRef.value) {
    saveAsTemplateHeight = saveAsTemplateRef.value.offsetHeight + 24;
  }

  const totalDeduction =
    baseHeight + descriptionTipHeight + descriptionBoxHeight + saveAsTemplateHeight;

  if (windowWidth.value <= 1440) {
    return `calc(91vh - ${totalDeduction}px)`;
  } else if (windowWidth.value > 1440 && windowWidth.value <= 1600) {
    return `calc(90vh - ${totalDeduction}px)`;
  } else {
    return `calc(80vh - ${totalDeduction}px)`;
  }
});

watch(
  () => viewOriginal.value,
  (newVal) => {
    currenViewOriginal.value = newVal;
  },
  { immediate: true },
);

watch(
  () => info?.value?.category,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      delete formError.value['category'];
    }
  },
  { immediate: true },
);

// 监听 descriptionTip 的显示状态变化
watch(
  () => info.value[currentTranslateKey.value]?.status,
  () => {
    // 等待DOM更新后重新计算高度
    nextTick(() => {
      // 触发 computed 重新计算
    });
  },
  { immediate: true },
);

// Add window resize handler
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

const handleSelectOpen = () => {
  selectOpen.value = !selectOpen.value;
};
// 兼容从vue2 tranSummary 跳过来的没有分类数据
const getCategoryData = async () => {
  try {
    const res = await getCategory({
      language_os: oSLanguageId.value,
    });
    currentCategoryData.value = res?.data || [];
    categoryNames.value = currentCategoryData.value.map((item) => item.category);
    // console.log('currentCategoryData.value-gsy:', currentCategoryData.value);
    // console.log('categoryNames:', categoryNames.value);
  } catch (error) {
    console.log(error);
  }
};
const cancelDialog = (flag = false) => {
  // console.log('edit-template-dailog-cancelDialog-fromType:', fromType.value);
  dialogStatus.value = false;
  initScrollInfo();
  emit('cancelDialog', fromType.value, flag);
};

// 点击"delete"按钮
const handleDelete = () => {
  conformDailogRef.value.show();
};

// 确认删除
const handleDeleteConfirm = async () => {
  // 如果正在提交中，直接返回
  if (deleteLoading.value) {
    return;
  }
  deleteLoading.value = true;
  try {
    let res = await deleteCustomtemplates(info.value.id);
    dialogStatus.value = false;
    emit('deleteSuccess', 'personal');
  } catch (error) {
    handleApiError(error);
  } finally {
    deleteLoading.value = false;
  }
};

const goBack = () => {
  nextFlag.value = false;
};

/**
 * 判断是否为SSO登陆的邮箱
 * 1.是，则默认邮箱显示空
 * 2.否，则显示邮箱
 */
const isMatchedSSO = (email) => {
  const regex = /^(google-|apple-).*@plaud\.ai$/;
  return regex.test(email);
};

const show = (data, overlayTransparent = false) => {
  // console.log('show-gsy:', data, overlayTransparent);
  fromType.value = data?.from || '';
  const commonOperate = () => {
    bgTransparent.value = overlayTransparent;
    dialogStatus.value = true;
    // 在设置完内容后，等待 DOM 更新完成再调整高度
    // nextTick(() => {
    //   autoResizeTextarea(false);
    // });
  };
  if (data.custom_status === 'create') {
    info.value = data || {};
    info.value.author_email = isMatchedSSO(props.email) ? '' : props.email || '';
    if (Object.keys(data).includes('orignal')) {
      // 编辑community模版
      let { description, category, authorEmail, title, content, iconColor, iconName } =
        data[currentTranslateKey.value];
      info.value.description = description;
      info.value.category = category;
      info.value.author_email = authorEmail;
      info.value.title = title;
      info.value.content = content;
      info.value.iconColor = iconColor;
      info.value.iconName = iconName;
    }
    commonOperate();
    // 直接从 storage 获取值
    const storedEmail = storage.get('author_email', props.userid) || '';
    if (storedEmail) {
      info.value.author_email = storedEmail;
    }
  } else {
    info.value = data || {};
    info.value.id = data?.id || '';
    info.value.template_id = data?.template_id || '';
    if (data.custom_type === 'community') {
      info.value.content = markdownSpeakerEditFormat(data.markdown);
    } else if (data.custom_type === 'system') {
      info.value.title = data.name;
      info.value.content = markdownSpeakerEditFormat(data.pre_markdown);
    }
    commonOperate();
  }
};

const handleSwiperSlideClick = (item) => {
  console.log('handleSwiperSlideClick-gsy:', item);
};

const initScrollInfo = () => {
  if (contentBoxRef.value) {
    contentBoxRef.value.scrollTop = 0;
    contentScrollTop.value = 0;
  }
};

const translateToOriginal = (original) => {
  currenViewOriginal.value = original;
};

const handleSwiperSlideChange = (e) => {
  contentScrollTop.value = 0; // 直接设置值为0
  currenViewOriginal.value = viewOriginal.value;
  nextTick(() => {
    if (contentBoxRef.value) {
      contentBoxRef.value.scrollTop = 0;
    }
  });
  // console.log('handleSwiperSlideChange-gsy:', e);
};

const getDefineStr = (str) => {
  if (!str) return str;
  return str.replace(/\n/g, '<br/>');
};

const changeIpt = (key) => {
  if (info?.value?.[key]?.length > 0) {
    delete formError.value[key];
  }
  if (key == 'title') {
    // 加了maxlength 不会走这里
    // if (info.value.title.length > titleMaxlength.value) {
    //   info.value.title = info.value.title.slice(0, titleMaxlength.value);
    //   formError.value = {
    //     name: {
    //       status: true,
    //       msg: t('Folder_toolong'),
    //     },
    //   };
    // } else {
    //   delete formError.value[key];
    // }
  } else if (key == 'content') {
    // 高度自适应
    // autoResizeTextarea(true);
    // 加了maxlength 不会走这里
    // if (info.value.content.length > contentMaxLength.value) {
    //   info.value.content = info.value.content.slice(0, contentMaxLength.value);
    //   formError.value = {
    //     content: {
    //       status: true,
    //       msg: t('Folder_toolong'),
    //     },
    //   };
    // } else {
    //   delete formError.value[key];
    // }
  } else {
    // delete formError.value[key];
    // }
  }
};
const autoResizeTextarea = (flag = false) => {
  nextTick(() => {
    const textarea = contentTextarea.value;
    const contentBox = contentBoxRef.value;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = textarea.scrollHeight + 'px';
      // 让父容器滚动到底部
      if (flag && contentBox) {
        contentBox.scrollTop = contentBox.scrollHeight;
      }
    }
  });
};
const validateForm = () => {
  formError.value = {};
  let hasError = false;

  if (!info.value.title) {
    formError.value.title = {
      status: true,
      msg: t('Custom_template_edit_name_error'),
    };
    hasError = true;
  }

  if (!info.value.content) {
    formError.value.content = {
      status: true,
      msg: t('Custom_template_edit_prompt_error'),
    };
    hasError = true;
  }

  if (info?.value?.title?.length > titleMaxlength.value) {
    formError.value = {
      title: {
        status: true,
        msg: t('Folder_toolong'),
      },
    };
    return false;
  }

  if (info?.value?.content?.length > contentMaxLength.value) {
    formError.value = {
      content: {
        status: true,
        msg: t('Folder_toolong'),
      },
    };
    return false;
  }

  return !hasError;
};

const handleApiError = (error) => {
  isLoading.value = false;

  if (error.status === -1) {
    console.log('no permission');
  } else if (error.status === -2) {
    toast(t('Custom_template_toast_maxlimit'));
  }
};

const saveDefine = async () => {
  // console.log('saveDefine-gsy-info.value:', info.value);
  // 表单验证
  if (!validateForm()) {
    return;
  }

  // 如果正在提交中，直接返回
  if (saveDefineLoading.value) {
    return;
  }

  // 重置错误状态
  formError.value = {};
  isLoading.value = true;

  try {
    saveDefineLoading.value = true;
    const payload = {
      r: Math.random(),
      name: info.value.title,
      content: info.value.content,
    };

    // 根据是否有 ID 决定是创建还是更新
    let res = {};
    if (info.value.custom_status === 'saveAsTemplate' && info.value.id) {
      res = await editCustomtemplates(info.value.id, payload);
    } else {
      res = await createCustomtemplates(payload);
    }
    let saveObj = {
      ...res.data,
      title: res.data?.name || '',
      custom_status: 'saveAsTemplate',
    };
    // console.log('saveObj-gsy:', saveObj);
    // 保存成功后的处理
    isLoading.value = false;
    nextFlag.value = false;
    if (enableShareWidget.value) {
      // console.log('edit-template-dailog-cancelDialog-fromType:', fromType.value);
      if (fromType.value === 'tranSummary') {
        dialogStatus.value = false;
        initScrollInfo();
      } else {
        cancelDialog(true);
      }
    } else {
      cancelDialog(true);
    }
    emit('saveSuccess', saveObj);
  } catch (error) {
    handleApiError(error);
  } finally {
    saveDefineLoading.value = false;
  }
};
// 创建模版的next
const handleNext = async () => {
  // 表单验证
  if (!validateForm()) {
    return;
  }
  nextFlag.value = true;
};

const validateNextForm = () => {
  formError.value = {};
  let hasError = false;

  if (!info?.value?.description) {
    formError.value.description = {
      status: true,
      msg: t('community_template_edit_description_error'),
    };
    hasError = true;
  }

  if (!info.value.category) {
    formError.value.category = {
      status: true,
      msg: t('community_template_edit_category_error'),
    };
    hasError = true;
  }

  if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(info.value.author_email)) {
    formError.value.author_email = {
      status: true,
      msg: t('community_template_edit_email_error'),
    };
    hasError = true;
  }

  if (info?.value?.description?.length > descriptionMaxlength.value) {
    formError.value = {
      title: {
        status: true,
        msg: t('Folder_toolong'),
      },
    };
    return false;
  }
  return !hasError;
};

// 创建/编辑社区模版的submit
const handleSubmit = async () => {
  // 如果正在提交中，直接返回
  if (submitLoading.value) {
    return;
  }

  // 表单验证
  if (!validateNextForm()) {
    return;
  }
  // 重置错误状态
  formError.value = {};
  isLoading.value = true;

  try {
    submitLoading.value = true;
    let iconInfo = iconPickerRef.value.getIconInfo();
    let { title, description, content, author_email, category } = info.value;
    const payload = {
      title,
      description,
      content,
      author_email,
      category,
      icon: findIconToCode(iconInfo?.name),
      icon_color: findColorToKey(iconInfo?.color),
    };

    // 根据是否有 ID 决定是创建还是更新
    let res = {};
    isEditCommunity.value = Object.keys(info.value).includes('orignal') || false;
    if (isEditCommunity.value) {
      // 编辑community模版
      res = await editCommunityTemplates({
        ...payload,
        template_id: info.value[currentTranslateKey.value].templateId,
      });
    } else {
      // 新建community模版
      res = await createCommunityTemplates(payload);
    }
    // console.log('res-gsy-createCommunityTemplates:', res);
    let saveObj = {
      ...res.data,
      title: res.data?.name || '',
      custom_status: 'saveAsTemplate',
    };
    // console.log('saveObj-gsy:', saveObj);
    // 保存成功后的处理
    isLoading.value = false;
    nextFlag.value = false;
    communitySuccessDailogRef.value.toggleDialog(true);
    emit('saveCommunitySuccess', saveObj);
    cancelDialog(true);
  } catch (error) {
    handleApiError(error);
  } finally {
    submitLoading.value = false;
    storage.set('author_email', info.value?.author_email, props.userid, 'NoExpiration');
  }
};

onMounted(() => {
  getCategoryData();
  initScrollInfo();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});

defineExpose({
  show,
});
</script>

<style lang="less">
.preview-swiper {
  .swiper-button-next {
    position: absolute !important;
    right: -90px !important;
  }

  .swiper-button-prev {
    position: absolute !important;
    left: -90px !important;
  }
}

.top-box {
  transition: all 0.3s ease-in-out;
}
</style>
