<!--确认框 -->
<template>
  <DialogRoot v-model:open="open">
    <DialogPortal>
      <DialogOverlay
        class="bg-black4 data-[state=open]:animate-overlayShow fixed inset-0 z-[99999]"
        @pointerdown.stop
      />
      <DialogContent
        class="z-[999999] dialog-custom-content data-[state=open]:animate-contentShow fixed top-[50%] left-[50%] max-h-[85vh] w-[400px] translate-x-[-50%] translate-y-[-50%] rounded-[13px] bg-white p-4 shadow1 focus:outline-none flex flex-col"
        :class="className"
      >
        <DialogTitle class="text-[18px] leading-[28px] font-semibold break-words mr-[46px]">
          {{ title }}
        </DialogTitle>
        <div class="flex flex-col justify-between">
          <div class="text-[14px] leading-[20px] text-[#475467] mb-6 break-words">
            {{ content }}
          </div>
          <div class="flex justify-between h-[44px]">
            <div
              class="cursor-pointer h-full flex-1 mr-4 bg-[#E4E7EC] rounded-lg text-[16px] text-[#1F1F1F] flex flex-row justify-center items-center mt-auto"
              @click="toggleDialog(false)"
            >
              {{ t('Name_speaker_dialog_cancel') }}
            </div>
            <div
              class="cursor-pointer h-full flex-1 bg-[#FF3B30] rounded-lg text-[16px] text-white flex flex-row justify-center items-center mt-auto"
              @click="handleConfirm"
            >
              {{ t('Name_speaker_dialog_confirm') }}
            </div>
          </div>
        </div>
        <DialogClose
          v-if="autoClose"
          class="dialog-close text-grass11 absolute top-[10px] right-[10px] inline-flex w-[18px] h-[18px] appearance-none items-center justify-center rounded-full border-0 focus:outline-none"
          aria-label="Close"
        >
          <svg-icon name="close" class="w-[18px] h-[18px] border-0"></svg-icon>
        </DialogClose>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;

// import DialogTip from '@/components/dialog-tip';
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from 'radix-vue';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  content: {
    type: String,
    default: '',
  },
});

let open = ref(false);
let autoClose = ref(true);
const emit = defineEmits(['handleConfirm', 'cancel']);

const toggleDialog = (flag) => {
  open.value = flag;
};

const show = () => {
  open.value = true;
};

const handleConfirm = () => {
  open.value = false;
  emit('handleConfirm');
};

defineExpose({
  toggleDialog,
  show,
});

onMounted(() => {});
</script>

<style scoped lang="less"></style>
