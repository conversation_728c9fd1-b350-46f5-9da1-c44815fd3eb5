<!-- 模版 -->
<template>
  <div class="bg-white">
    <div class="flex items-center justify-between min-w-[900px] max-w-[1450px] mx-auto px-10">
      <div class="flex items-center">
        <svg-icon
          name="benefit"
          class="w-5 h-5 text-[#cacfd8] cursor-pointer"
          @click="handleBack"
        ></svg-icon>
        <svg-icon name="drill_in" class="w-2 h-3 mx-3"></svg-icon>
        <span class="font-bold text-black text-[14px]">{{ name }}</span>
      </div>
      <button
        class="flex items-center bg-blue-100 rounded-full px-3 py-1"
        data-pld="community-template-submit-click"
        @click="handleSubmit"
      >
        <svg-icon name="plus" class="w-5 h-5 mr-[2px]"></svg-icon>
        <span class="text-blue-500 font-semibold">{{ t('submit_text') }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;

const backToHome = inject('backToHome');
const emit = defineEmits(['handleSubmit']);

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
});
const handleBack = () => {
  backToHome();
};

const handleSubmit = () => {
  emit('handleSubmit');
};

onMounted(() => {});
</script>

<style scoped lang="less"></style>
