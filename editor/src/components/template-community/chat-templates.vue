<!-- chat templates -->
<template>
  <div class="chat-templates-wrapper" v-for="oneItem in currentData" :key="oneItem.id">
    <TitleBlock
      class="px-10"
      :data="oneItem.category"
      :showIcon="true"
      data-pld="community-template-category-click"
      @click="goCategoryDetail(oneItem)"
    />
    <MySwiper
      class="weekly-swiper"
      :data="oneItem.data.slice(0, 8)"
      :options="options"
      :showNavigation="true"
      :needSwiperWrapper="true"
    >
      <template #default="{ item, index }">
        <!-- class="my-swiper-item ml-[20px] h-[160px] relative min-w-[240px] max-w-[320px] rounded-[14px] mb-6 mt-4 bg-white" -->
        <div
          class="my-swiper-item ml-[20px] h-[160px] relative max-w-[320px] rounded-[14px] mb-6 mt-4 bg-white"
          :class="{ 'mr-[20px]': index === item?.data?.length - 1 }"
          data-pld="community-template-category-detail-click"
          @click="handleSwiperSlideClick(oneItem.data, index)"
        >
          <CardBox
            :data="item.custom_type === 'community' ? item[translateKey] : item"
            :index="index"
          ></CardBox>
        </div>
      </template>
    </MySwiper>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import TitleBlock from './title-block.vue';
import CardBox from './card-box.vue';
import MySwiper from '@/components/swiper/index.vue';
import { getChatTemplates } from '@/apis/templateCommunity';
import { convertToHexColor, findIconCodeToName } from '@/utils/material-icons-collection.js';

const props = defineProps({});

const oSLanguageId = inject('oSLanguageId');
const viewOriginal = inject('viewOriginal');
const translateKey = inject('translateKey');

const options = ref({
  // autoplay: {
  //   delay: 3000,
  //   pauseOnMouseEnter: true,
  // },
  slidesPerView: 4.2,
  slidesPerGroup: 4,
});

let currentData = ref([]);

const emit = defineEmits(['showPreview', 'getCategoryData']);

const processTemplateData = (list) => {
  const { category, category_id, icon, data, community_templates } = list;

  // 处理系统模板数据
  const processSystemTemplate = (item) => ({
    ...item,
    id: item.name,
    custom_type: 'system',
    isLocked: item.is_locked === 1 || false,
  });

  // 处理社区模板数据
  const processCommunityTemplate = (item) => {
    const {
      latest_published_version,
      translated_published_version,
      author_name,
      usage_count,
      id,
      template_id,
    } = item;
    const baseTemplateData = {
      custom_type: 'community',
      template_id: id,
      category,
      category_id,
      category_icon: icon,
      authorName: author_name,
      usageCount: usage_count,
    };

    return {
      custom_type: 'community',
      orignal: {
        ...baseTemplateData,
        iconColor: convertToHexColor(latest_published_version.icon_color),
        iconName: findIconCodeToName(latest_published_version.icon),
        title: latest_published_version.title,
        description: latest_published_version.description,
        content: latest_published_version.content || '',
        id: latest_published_version.id || '',
        isLocked: item.is_locked === 1 || false,
      },
      translated: {
        ...baseTemplateData,
        iconColor: convertToHexColor(translated_published_version.icon_color),
        iconName: findIconCodeToName(translated_published_version.icon),
        title: translated_published_version.title,
        description: translated_published_version.description,
        content: translated_published_version.content || '',
        id: translated_published_version.id || '',
        isLocked: item.is_locked === 1 || false,
      },
    };
  };

  return {
    category,
    category_id,
    icon,

    data: [
      ...data.map(processSystemTemplate),
      ...community_templates.map(processCommunityTemplate),
    ],
  };
};

const getChatTemplatesData = async () => {
  try {
    const res = await getChatTemplates({
      language_os: oSLanguageId.value,
      has_recently_use: true,
      has_custom: true,
    });
    currentData.value = res.templates.map((list) => processTemplateData(list));

    let categoryData = res.templates.map((list) => {
      let { category, category_id, icon } = list;
      return {
        category,
        category_id,
        icon,
      };
    });
    emit('getCategoryData', categoryData);
    // console.log('chat-templates-categoryData-gsy:', categoryData);
    // console.log('chat-templates-currentData.value-gsy:', currentData.value);
  } catch (error) {
    console.log(error);
  }
};

const handleSwiperSlideClick = (data, index) => {
  // console.log('handleSwiperSlideClick:', data, index);
  emit('showPreview', data, index);
};

// 根据类别索引跳转二级分类详情
const goCategoryDetailByIndex = (index) => {
  let item = currentData.value[index];
  emit('goCategoryDetail', item);
};

const goCategoryDetail = (item) => {
  // console.log('goCategoryDetail:', item);
  emit('goCategoryDetail', item);
};

onMounted(() => {
  getChatTemplatesData();
});

defineExpose({
  goCategoryDetailByIndex,
});
</script>

<style lang="less">
.weekly-swiper {
  .swiper-button-next {
    right: -5px !important;
  }
  .swiper-button-prev {
    left: 0px !important;
  }
}
</style>
