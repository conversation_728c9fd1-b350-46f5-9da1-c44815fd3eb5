<!-- 模版社区-二级页面 -->
<template>
  <div class="ctemplates-list-wrapper pb-8 pt-[52px] overflow-y-auto">
    <SubTopInfo
      class="subtop-info-fixed py-3 z-[100] mx-auto bg-white"
      :name="data?.category"
      @handleSubmit="handleSubmit"
      :class="leftSmallStatus ? '!left-[65px]' : '!left-[242px]'"
    ></SubTopInfo>
    <div
      class="h-[200px] px-10 w-full absolute top-[52px] left-0 right-0"
      :style="{
        background: `linear-gradient(180deg, ${categoryFileDetailColorList[data.category_id]} 0%, rgba(255, 238, 215, 0) 100%)`,
      }"
    ></div>
    <div class="min-w-[900px] max-w-[1450px] mx-auto relative">
      <div class="main flex flex-col relative">
        <!-- :style="{
            background: `linear-gradient(180deg, ${categoryFileDetailColorList[data.category_id]} 0%, rgba(255, 238, 215, 0) 100%)`,
          }" -->
        <div class="h-[200px] px-10 w-full absolute top-0 left-0 right-0">
          <div class="flex items-center text-[24px] leading-8 mt-6">
            <img :src="data?.icon" class="w-16 h-16 mr-[6px]" v-if="data?.icon" />
            <img src="/static/template_icon_blank.png" class="w-16 h-16 mr-[6px]" v-else />
            <span class="font-bold text-[#1F1F1F]">{{ data?.category }}</span>
          </div>
        </div>
        <div
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5 mx-10 mt-[128px]"
        >
          <CardBox
            class="!h-[160px] cursor-pointer"
            :data="item.custom_type === 'community' ? item[translateKey] : item"
            :index="index"
            v-for="(item, index) in data.data"
            :key="item.id"
            data-pld="community-template-detail"
            @click="goTemplateDetail(data.data, index)"
          ></CardBox>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch } from 'vue';
import { i18n } from '@/locales/i18n';
const { t, locale } = i18n.global;
import SubTopInfo from './subtop-info.vue';
import CardBox from './card-box.vue';
import { categoryFileDetailColorList } from '@/utils/material-icons-collection';

const props = defineProps({
  leftSmallStatus: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    required: true,
  },
});
const translateKey = inject('translateKey');
const validateAuthUser = inject('validateAuthUser');
const emit = defineEmits(['backtoHome', 'showPreview', 'handleSubmit']);

const goTemplateDetail = (data, index) => {
  // console.log('goTemplateDetail-data-gsy:', data, index);
  emit('showPreview', data, index);
};

const handleSubmit = () => {
  if (!validateAuthUser()) return;
  emit('handleSubmit', 'submit');
};

onMounted(() => {});
</script>

<style scoped lang="less">
.subtop-info-fixed {
  position: fixed;
  top: 0;
  left: 242px;
  right: 0;
  background: #fff; /* 保证内容不透明 */
}
</style>
