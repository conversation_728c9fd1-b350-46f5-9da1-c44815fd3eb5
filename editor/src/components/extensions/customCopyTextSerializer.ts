import { Extension } from '../../core';
import { Plugin, PluginKey } from '../../pm/state';

// 创建自定义的复制文本序列化器，优先级更高
export const CustomCopyTextSerializer = Extension.create({
  name: 'customCopyTextSerializer',
  priority: 1000, // 高优先级，确保在其他扩展之前执行

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('customCopyTextSerializer'),
        props: {
          clipboardTextSerializer: (slice) => {
            // 首先获取 Markdown 格式
            const markdownText = this.editor.storage.markdown.serializer.serialize(slice.content);

            // 清理 Markdown 符号但保留序号和层级
            let cleanedText = markdownText
              // 清理标题符号，保留层级
              .replace(/^(#{1,6})\s+/gm, (match, hashes) => {
                // return '  '.repeat(hashes.length - 1);
                return ''.repeat(hashes.length - 1);
              })
              // // 清理无序列表符号，保留缩进
              // .replace(/^(\s*)[-*+]\s+/gm, '$1')
              // // 保留有序列表的序号和点号
              // .replace(/^(\s*)(\d+)\.\s+/gm, '$1$2. ')
              // 清理引用符号，保留缩进
              .replace(/^(\s*)>\s+/gm, '$1')
              // 清理代码块符号
              .replace(/^```.*$/gm, '')
              .replace(/```$/gm, '')
              // 清理行内代码符号
              .replace(/`([^`]+)`/g, '$1')
              // 清理粗体和斜体符号
              .replace(/\*\*([^*]+)\*\*/g, '$1')
              .replace(/\*([^*]+)\*/g, '$1')
              .replace(/__([^_]+)__/g, '$1')
              .replace(/_([^_]+)_/g, '$1')
              // 清理删除线符号
              .replace(/~~([^~]+)~~/g, '$1')
              // 清理链接，只保留文本
              .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
              // 处理 HTML mark 标签
              .replace(/<\/?(strong|b|em|i|u|s|strike|del|mark)>/gi, '')
              //删除任务[ ] [x]
              .replace(/\[( |x)\]/gi, '')
              // 清理多余的空行
              .replace(/\n\s*\n/g, '\n')
              //删除分割线
              .replace(/^\s*---\s*$(\r?\n)?/gm, '\n')
              // 清理首尾空白
              .trim();

            return cleanedText;
          },
        },
      }),
    ];
  },
});
