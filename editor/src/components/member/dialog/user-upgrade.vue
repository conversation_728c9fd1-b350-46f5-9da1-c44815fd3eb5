<template>
  <MemberModal
    :visible="dialogStatus"
    :links="[{ url: toFAQUrl, text: t('member_dict_faq') }, { url: toUserAgreementUrl, text: t('member_dict_user_agreement') }]"
    width="480px"
    :confirmButtonText="t('member_dict_got_it')"
    @close="handlerVisible(false)"
    @confirm="handlerVisible(false)"
    :title="!proUser && infoFlag > 1 ? t('member_pro_upgrade_modal_title'):''"
    backgroundDecorator
  >
    <template v-slot:title  v-if="infoFlag==1">
      <MemberModalTimeUnit :recordingTime="getTimeName(getTimeStr(sendInfo.duration, false))" :remainingTime="getTimeName(getTimeStr(sendInfo.left, false, false))" />
    </template>
    <template v-slot:content>
      <!-- pro用户的提示语 -->
      <p v-if="proUser">{{t('member_transcription_alert_text')}}</p>
      <MemberModalUpgradeUnit v-else
                              :title="t('member_pro_transcription_modal_upgrade_title')"
                              :subtitle="t('member_pro_transcription_modal_upgrade_subtitle')"
                              :features="[
                { theme: 'orange', active: infoFlag==1 || infoFlag==4, icon: 'icon-icon_benefit_transcription', text: t('member_pro_transcription_modal_upgrade_transcription') },
                { theme: 'purple', active: infoFlag==2, icon: 'icon-icon_benefit_template', text: t('Guide_step4_column_2_pro') },
                { theme: 'purple', active: infoFlag==3, icon: 'icon-icon_benefit_customize', text: t('member_pro_transcription_modal_upgrade_prompt') },
                { theme: 'purple', active: infoFlag==5, icon: 'icon-ai', text: t('member_pro_transcription_modal_upgrade_askai') },
              ]"
                              :desc="t('member_pro_upgrade_modal_upgrade_notice')"
      />
    </template>
  </MemberModal>
</template>

<script setup>
import {ref} from 'vue';
import MemberModal from '../../member-modal/MemberModal.vue';
import MemberModalTimeUnit from '../../member-modal/MemberModalTimeUnit.vue';
import MemberModalUpgradeUnit from '../../member-modal/MemberModalUpgradeUnit.vue';
import {getTimeName,getTimeStr} from '@/utils/time-format'
import {toFAQUrl,toUserAgreementUrl} from '@/utils/url'
import { i18n } from '@/locales/i18n';
const { t } = i18n.global;

const emit = defineEmits(['close']);
const toUserAgreementUrl = ref(toUserAgreementUrl);
const toFAQUrl = ref(toFAQUrl);
const dialogStatus = ref(false);
const infoFlag = ref(0);//1:时长不够 2:没有系统模版权限 3:没有自定义模版权限 4:升级pro
const sendInfo = ref({});
const proUser = ref(false);
const handlerVisible = (status,info={})=>{
    dialogStatus.value = status;
    if(status){
        // let memberType = this.$root.userStateInfo.membership_type;
        proUser.value = ['backer', 'pro','unlimited'].includes('1pro');
        infoFlag.value = info.flag;
        sendInfo.value = info;
    }
    else{
        infoFlag.value = 0;
        sendInfo.value = {};
        emit('close');
    }
}
defineExpose({
    handlerVisible
})
</script>
