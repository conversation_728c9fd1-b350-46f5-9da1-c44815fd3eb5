<template>
  <MemberModal
    :visible="dialogStatus"
    :closeButton="true"
    width="480px"
    :confirmButtonText="t('member_dict_learn_more')"
    @close="handlerVisible(false)"
    @confirm="goto()"
    :title="t('member_starter_unlock_modal_title')"
  >
    <template v-slot:content>
      <MemberModalUpgradeUnit
        :subtitle="t('member_starter_unlock_modal_subtitle')"
        :features="[
                { theme: 'orange', icon: 'icon-icon_benefit_transcription', text: t('member_starter_unlock_modal_transcription') },
                { theme: 'purple', icon: 'icon-icon_benefit_template', text: t('member_starter_unlock_modal_template') },
                { theme: 'purple', icon: 'icon-icon_benefit_autopilot', text: t('member_starter_unlock_modal_autopilot') },
                { theme: 'orange', icon: 'icon-icon_benefit_speaker_labels', text: t('member_starter_unlock_modal_speaker') },
                { theme: 'orange', icon: 'icon-icon_benefit_trim', text: t('member_starter_unlock_modal_trim') },
                { theme: 'orange', icon: 'icon-icon_benefit_import', text: t('member_starter_unlock_modal_import') },
              ]"
      />
    </template>
  </MemberModal>
</template>

<script setup>
import {ref} from 'vue'
import MemberModal from '../../member-modal/MemberModal.vue';
import MemberModalUpgradeUnit from '../../member-modal/MemberModalUpgradeUnit.vue';
import { i18n } from '@/locales/i18n';
const { t } = i18n.global;

const dialogStatus = ref(false);
const handlerVisible = (status)=>{
    dialogStatus.value = status;
}
const goto = ()=>{
    window.open('https://www.plaud.ai/');
}
defineExpose({
    handlerVisible
})
</script>

