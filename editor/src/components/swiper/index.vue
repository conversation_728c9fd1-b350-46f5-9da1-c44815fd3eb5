<!-- swiper -->
<template>
  <div class="swiper-box text-black3 w-full text-white cursor-pointer">
    <!-- <div class="swiper-wrapper relative group overflow-hidden"> -->
    <div :class="`swiper-wrapper relative group ${isOverflowHidden ? 'overflow-hidden' : ''} `">
      <div
        :class="`swiper-button-prev-${uniqueId} swiper-button-prev ${showNavigation ? 'visible' : 'invisible'} absolute top-1/2 -translate-y-1/2 left-6 z-20 transition-[visibility] duration-300 focus:outline-none`"
      ></div>
      <div
        v-if="needSwiperWrapper"
        ref="gradientOverlayLeft"
        class="z-10 w-[40px] h-full absolute left-0 bottom-0 bg-white transition-opacity duration-300 opacity-0"
      ></div>
      <div
        v-if="needSwiperWrapper"
        ref="gradientOverlayRight"
        class="z-10 w-[80px] h-full absolute right-0 bottom-0 bg-[linear-gradient(270deg,_#FFF_0%,_rgba(255,255,255,0)_100%)] transition-opacity duration-300"
      ></div>
      <div
        :class="`swiper-button-next-${uniqueId} swiper-button-next absolute top-1/2 -translate-y-1/2 right-6 z-30 ${showNavigation ? 'visible' : 'invisible'} transition-[visibility] duration-300 focus:outline-none`"
      ></div>
      <swiper-container
        :class="`my-swiper ${needSwiperWrapper ? 'pl-[20px]' : ''}`"
        :autoplay="swiperOptions.autoplay"
        :centeredSlides="swiperOptions.centeredSlides"
        :slidesPerGroup="swiperOptions.slidesPerGroup"
        :slidesPerView="swiperOptions.slidesPerView"
        :spaceBetween="swiperOptions.spaceBetween"
        :pagination="swiperOptions.pagination"
        :navigation="swiperOptions.navigation"
        :initialSlide="swiperOptions?.initialSlide"
        :effect="swiperOptions.effect"
        :fadeEffect="swiperOptions.fadeEffect"
        :speed="swiperOptions.speed"
        @swiperslidechange="handleSlideChange"
      >
        <swiper-slide v-for="(item, index) in data" :key="item.id" class="h-full">
          <slot :item="item" :index="index" class="h-full"></slot>
        </swiper-slide>
      </swiper-container>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, onBeforeMount, watch } from 'vue';
import { i18n } from '../../locales/i18n';
const { t } = i18n.global;
import { register } from 'swiper/element/bundle';
register();

const props = defineProps({
  options: {
    type: Object,
    default: () => ({}),
  },
  data: {
    type: Array,
    default: () => [],
  },
  needSwiperWrapper: {
    type: Boolean,
    default: false,
  },
  showNavigation: {
    type: Boolean,
    default: false,
  },
  isOverflowHidden: {
    type: Boolean,
    default: true,
  },
});

// 生成唯一ID
const uniqueId = ref(Math.random().toString(36).substr(2, 9));

const swiperOptions = ref({
  autoplay: false,
  slidesPerView: 1,
  slidesPerGroup: 1,
  spaceBetween: 0,
  centeredSlides: false,
  initialSlide: 0,
  // pagination: {
  //   hideOnClick: false,
  //   clickable: true,
  // },
  navigation: {
    nextEl: `.swiper-button-next-${uniqueId.value}`,
    prevEl: `.swiper-button-prev-${uniqueId.value}`,
    hideOnClick: false,
    disabledClass: 'swiper-button-disabled',
  },
});

const emit = defineEmits(['slideChange']);

watch(
  () => props.options,
  (newValue, oldValue) => {
    swiperOptions.value = {
      ...swiperOptions.value,
      ...newValue,
    };
  },
  { deep: true, immediate: true },
);

const gradientOverlayLeft = ref(null);
const gradientOverlayRight = ref(null);

const handleSlideChange = (e) => {
  emit('slideChange', e.detail[0]);
  // console.log('e.detail[0]:', e.detail[0]);
  if (!props.needSwiperWrapper) return;
  const isLastSlide = e.detail[0].isEnd;
  const isFirstSlide = e.detail[0].isBeginning;
  toggleGradient(gradientOverlayLeft.value, !isLastSlide);
  toggleGradient(gradientOverlayRight.value, isLastSlide);
};

// 切换渐变
const toggleGradient = (domRef, showFlag) => {
  if (domRef) {
    domRef.style.opacity = showFlag ? '0' : '1';
  }
};
</script>

<style lang="less" scoped>
@import '@/assets/styles/swiper.less';

.swiper-box {
}
</style>
