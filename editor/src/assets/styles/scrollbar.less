.scrollbar-wrapper {
  :deep(.ps) {
    height: 100%;
    position: relative;
  }
  :deep(.ps__rail-y) {
    right: 0;
    outline: none;
  }
  // :deep(.ps__rail-x) {
  //   display: none;
  // }

  :deep(.ps__thumb-x) {
    height: 6px !important;      /* 控制横向滚动条的厚度 */
    background: #d1d5db !important; /* 滚动条颜色 */
    border-radius: 3px !important;
    transition: background 0.2s;
  }
  :deep(.ps__thumb-x:hover) {
    background: #b0b8c1 !important;
    height: 6px !important; // 强制 hover 时高度不变
  }
  :deep(.ps__rail-x) {
    height: 6px !important;      /* 轨道高度要和thumb一致 */
    background: transparent !important;
  }
  :deep(.ps__thumb-y:hover) {
    background: #b0b8c1 !important;
    width: 6px !important;
  }

}