.swiper-button-next,
.swiper-button-prev {
  /* background-color: red !important; */
  width: 80px !important;
  height: 80px !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  margin-top: 0 !important;
  transform: translateY(-50%) !important;
  transition: visibility 0.3s ease-in-out !important;
}

.group:hover .swiper-button-next,
.group:hover .swiper-button-prev {
  visibility: visible !important;
}

.swiper-button-next::after,
.swiper-button-prev::after {
  display: none !important;
}

.swiper-button-next {
  background-image: url(../../assets/images/chevron_right.svg) !important;
  right: 20px !important;
}

.swiper-button-prev {
  background-image: url(../../assets/images/chevron_left.svg) !important;
  left: 20px !important;
}

.swiper-button-disabled {
  opacity: 0.35 !important;
  cursor: auto !important;
  pointer-events: none !important;
}
