<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_9005_24470)">
<rect x="30" y="26" width="32" height="32" rx="16" fill="white" shape-rendering="crispEdges"/>
<rect x="30.5" y="26.5" width="31" height="31" rx="15.5" stroke="#E4E7EC" shape-rendering="crispEdges"/>
<path d="M50.0073 42.031C50.0073 41.8683 49.9487 41.7185 49.825 41.6014L44.6623 36.5493C44.5516 36.4386 44.4084 36.38 44.2391 36.38C43.9006 36.38 43.6401 36.6339 43.6401 36.9725C43.6401 37.1352 43.7118 37.285 43.8159 37.3956L48.5555 42.031L43.8159 46.6665C43.7118 46.7771 43.6401 46.9204 43.6401 47.0896C43.6401 47.4282 43.9006 47.6821 44.2391 47.6821C44.4084 47.6821 44.5516 47.6235 44.6623 47.5063L49.825 42.4607C49.9487 42.337 50.0073 42.1938 50.0073 42.031Z" fill="#1F1F1F"/>
</g>
<defs>
<filter id="filter0_d_9005_24470" x="0" y="0" width="92" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9005_24470"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9005_24470" result="shape"/>
</filter>
</defs>
</svg>
