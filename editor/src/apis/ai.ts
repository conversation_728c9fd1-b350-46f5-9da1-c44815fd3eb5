import api from '../utils/api';
import { ASK_PREFIX } from './index';

const urls = {
  global_ask: '/global_ask',
  question: '/ask',
  add_todos: '/action/extract_todos',
  improve_writing: '/action/write_improve',
  rewrite_format: '/action/rewrite_format',
  make_key_metrics: '/action/extract_key_data',
  more_details: '/action/more_datails',
  extract_conclusions: '/action/extract_conclusions',
};

export async function generate(type: keyof typeof urls, data: Record<string, any>) {
  const url = `${ASK_PREFIX}${urls[type]}`;
  if (!url) {
    throw new Error(`Unsupported type: ${type}`);
  }
  return await api.post(url, data, {
    headers: {
      accept: 'text/event-stream',
      // 'X-Encrypt-Response': 0
    },
  });
}

// ask ai 追问的问题列表
export async function getQuestionList(data: Record<string, any>) {
  const url = `${ASK_PREFIX}/recommend_questions`;
  return await api.post(url, data, {
    headers: {
      accept: 'text/event-stream',
      // 'X-Encrypt-Response': 0
    }})
}

export async function getHistory(data) {
  const url = `${ASK_PREFIX}/ask_history`;
  return await api.post(url,data);
}
