import api from '../utils/api';

export async function getDailyRecommend(data: Record<string, any>) {
  const url = '/summary/community/templates/daily_recommend';
  return await api.post(url, data);
}

export async function getWeeklyRecommend(data: Record<string, any>) {
  const url = '/summary/community/templates/weekly_recommend';
  return await api.post(url, data);
}
export async function getMineTemplates(data: Record<string, any>) {
  const url = '/summary/community/templates/mine';
  return await api.post(url, data);
}

export async function getCategory(data: Record<string, any>) {
  const url = '/summary/community/templates/categorys';
  return await api.get(url, data);
}
export async function getChatTemplates(data: Record<string, any>) {
  const url = '/summary/chatllm/templates';
  return await api.get(url, data);
}

export async function editCustomtemplates(id: string, data: Record<string, any>) {
  const url = `/ai/customtemplates/${id}`;
  return await api.patch(url, data);
}
export async function deleteCustomtemplates(id: string) {
  const url = `/ai/customtemplates/${id}`;
  return await api.delete(url);
}

export async function createCustomtemplates(data: Record<string, any>) {
  const url = '/ai/customtemplates';
  return await api.post(url, data);
}

export async function getMineTemplatesList(data: Record<string, any>) {
  const url = '/ai/customtemplates';
  return await api.get(url, data);
}

export async function createCommunityTemplates(data: Record<string, any>) {
  const url = '/summary/community/templates/create';
  return await api.post(url, data);
}

export async function editCommunityTemplates(data: Record<string, any>) {
  const url = '/summary/community/template/edit';
  return await api.post(url, data);
}

export async function deleteCommunityTemplates(data: Record<string, any>) {
  const url = '/summary/community/templates/delete';
  return await api.post(url, data);
}