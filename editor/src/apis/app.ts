import api from '../utils/api';

export async function getDetail(data: Record<string, any>) {
  let url = '/file/list';
  url += '?support_mul_summ=true';
  return await api.post(url, data);
}

export async function saveContent(id, data: Record<string, any>) {
  const url = `/file/${id}`;
  data.support_mul_summ = true;
  return await api.patch(url, data);
}

export function getPlayUrl(id) {
  const url = `/file/temp-url/${id}`;
  return api.get(url);
}

export function getAudioTrimUrl(id) {
  const url = `/file/temp-url/${id}?is_opus=1`;
  return api.get(url);
}

export async function getStatus(data: Record<string, any>) {
  const url = '/ai/status';
  return await api.get(url, data);
}

export async function getSummary(id, data: Record<string, any>) {
  const url = `/ai/chatllm/${id}`;
  return await api.post(url, data);
}

export async function getTranscript(id, data: Record<string, any>) {
  const url = `ai/transsumm/${id}`;
  return await api.post(url, data);
}
