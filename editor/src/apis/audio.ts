import api from '../utils/api';
import axios from 'axios';

export function httpUploadFileInfo(params, progressCallBack: Function) {
  const CancelToken = axios.CancelToken;
  let cancelLoad;
  const ps = api.post('/file/upload', params, {
    headers: { 'Content-Type': 'multipart/form-data' },
    cancelToken: new CancelToken((c) => {
      cancelLoad = c;
    }),
    onUploadProgress: (progressEvent) => {
      let percent = parseInt(Math.floor((progressEvent.loaded / progressEvent.total) * 100));
      if (progressCallBack) {
        progressCallBack(percent);
      }
    },
  })
  return { ps, cancelLoad };
}
