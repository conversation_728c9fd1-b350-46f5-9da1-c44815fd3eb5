import api from '../utils/api';

export async function getStripeLink(data: Record<string, any>) {
  const url = '/membership/stripe/paymentLink';
  return await api.post(url, data);
}

export async function getStripePrices(data: Record<string, any>) {
  const url = '/membership/stripe/prices/';
  return await api.get(url, data);
}

export async function getStripeSessionStatus(data: Record<string, any>) {
  const url = '/membership/stripe/checkoutSession/'+data;
  return await api.get(url);
}
//充值列表
export async function getBuyList() {
  const url = '/user/me/history/transactions';
  return await api.get(url);
}
//转写列表
export async function getTransList() {
  const url = '/user/me/history/transcripts';
  return await api.get(url);
}
//升级，修改订阅
export async function upgradeSubscription(id,data: Record<string, any>) {
  const url = `/membership/stripe/subscriptions/${id}`;
  return await api.post(url, data);
}
//获取发票支付状态
export async function getStripeInvoicesStatus(id) {
  const url = `/membership/stripe/invoices/${id}`;
  return await api.get(url);
}
