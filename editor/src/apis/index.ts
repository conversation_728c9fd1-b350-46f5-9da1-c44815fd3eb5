export const SERVER_URL = {
  // 开发环境
  dev: 'https://api-dev.plaud.ai',
  // dev: 'https://api-yangxiaobo-1.plaud.ai',
  // dev: 'https://api-lihang-2.plaud.ai',
  // 预发布环境
  test: 'https://api-test.plaud.ai',
  // 生产环境
  pro: 'https://api.plaud.ai',
};

/**
 * 获取接口服务前缀
 */
export const getAPIPrefix = () => {
  const isProd = location.hostname.indexOf('plaud.ai') !== -1;
  let isApp = !!window.isApp;
  const isDev = false;
  // 请求前缀
  let API_PREFIX = '';
  if (isApp) {
    API_PREFIX = window.flutter_inappwebview.baseUrl || SERVER_URL.pro;
  } else {
    // 添加请求前缀
    API_PREFIX = SERVER_URL.pro;

    if (!isProd) {
      if (isDev) {
        API_PREFIX = SERVER_URL.dev;
      } else {
        API_PREFIX = SERVER_URL.test;
      }
    }
  }
  return API_PREFIX;
};

/**
 * ask接口服务器地址
 */
export const ASK_PREFIX = `${getAPIPrefix()}/ask`;
