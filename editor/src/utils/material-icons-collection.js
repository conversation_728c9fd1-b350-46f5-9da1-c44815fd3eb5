import { MATERIAL_ICONS } from '@/data/material_icons';

// 默认颜色
const defaultARGBColor = '0xFF424953'; // 深灰蓝色调
// 默认图标
const defaultIconName = 'account_circle_rounded';

// 预定义的颜色选项
export const colorOptions = {
  '0xFF475467': '#475467', // 深灰色
  '0xFF858C9B': '#858C9B', // 灰色
  '0xFF007AFF': '#007AFF', // 蓝色
  '0xFF32ADE6': '#32ADE6', // 浅蓝色
  '0xFF00C7BE': '#00C7BE', // 青色
  '0xFF34C759': '#34C759', // 绿色
  '0xFFFF3B30': '#FF3B30', // 红色
  '0xFFFF9500': '#FF9500', // 橙色
  '0xFFFFCC00': '#FFCC00', // 黄色
  '0xFF5856D6': '#5856D6', // 紫蓝色
  '0xFFAF52DE': '#AF52DE', // 紫色
  '0xFFA2845E': '#A2845E', // 棕色
};

// 预定义类别的颜色选项
export const categoryFileDetailColorList = {
  cat_recently: '#E8E9EC', // recently
  cat_general: '#E8E9EC', // general
  cat_meeting: '#FFEED7', // meeting
  cat_speech: '#D6F3DD', // speech
  cat_call: '#FFD3D0', // call
  cat_interview: '#C8F3F1', // interview
  cat_medical: '#CDE5FF', // medical
  cat_sales: '#FFF3C9', // sales
  cat_consulting: '#CDE5FF', // consulting
  cat_education: '#D8DBDF', // education
  cat_construction: '#FCF5DB', // construction
  cat_it: '#E5E7EA', // it
  cat_real_estate: '#D2EDFA', // real estate
  cat_legal: '#E5E7EA', // legal
  cat_financial: '#FCE5D3', // financial
  cat_custom: '#EEE7FB', // custom
};

// icon 集合
export const categoryIcons = {
  General: [
    'account_circle_rounded',
    'info_rounded',
    'visibility_rounded',
    'calendar_month_rounded',
    'schedule_rounded',
    'language_rounded',
    'lock_rounded',
    'verified_rounded',
    'manage_accounts_rounded',
    'task_alt_rounded',
    'event_rounded',
    'bookmark_rounded',
    'calendar_today_rounded',
    'lightbulb_rounded',
    'category_rounded',
    'lock_open_rounded',
    'build_rounded',
    'date_range_rounded',
    'supervisor_account_rounded',
    'event_available_rounded',
    'today_rounded',
    'pending_rounded',
    'preview_rounded',
    'stars_rounded',
    'celebration_rounded',
    'translate_rounded',
    'account_box_rounded',
    'how_to_reg_rounded',
    'edit_calendar_rounded',
    'label_rounded',
    'extension_rounded',
    'record_voice_over_rounded',
    'web_rounded',
    'rate_review_rounded',
    'hourglass_empty_rounded',
    'event_note_rounded',
    'published_with_changes_rounded',
    'support_rounded',
    'bookmarks_rounded',
    'all_inclusive_rounded',
    'interests_rounded',
    'supervised_user_circle_rounded',
    'collections_bookmark_rounded',
    'rule_rounded',
    'api_rounded',
    'build_circle_rounded',
    'wysiwyg_rounded',
    'hotel_class_rounded',
    'circle_notifications_rounded',
    'web_asset_rounded',
    'model_training_rounded',
    'bookmark_add_rounded',
    'pageview_rounded',
    'label_important_rounded',
    'approval_rounded',
    'perm_contact_calendar_rounded',
    'more_rounded',
    'bookmark_added_rounded',
    'offline_bolt_rounded',
    'anchor_rounded',
    'domain_verification_rounded',
    'gesture_rounded',
    'outbound_rounded',
    'lightbulb_circle_rounded',
    'batch_prediction_rounded',
  ],
  Business: [
    'shopping_cart_rounded',
    'payments_rounded',
    'shopping_bag_rounded',
    'credit_card_rounded',
    'attach_money_rounded',
    'receipt_long_rounded',
    'trending_up_rounded',
    'storefront_rounded',
    'sell_rounded',
    'account_balance_rounded',
    'work_rounded',
    'paid_rounded',
    'analytics_rounded',
    'account_balance_wallet_rounded',
    'query_stats_rounded',
    'savings_rounded',
    'store_rounded',
    'calculate_rounded',
    'bar_chart_rounded',
    'qr_code_scanner_rounded',
    'account_tree_rounded',
    'add_shopping_cart_rounded',
    'redeem_rounded',
    'receipt_rounded',
    'currency_exchange_rounded',
    'shopping_basket_rounded',
    'qr_code_2_rounded',
    'domain_rounded',
    'precision_manufacturing_rounded',
    'qr_code_rounded',
    'leaderboard_rounded',
    'timeline_rounded',
    'corporate_fare_rounded',
    'insert_chart_rounded',
    'wallet_rounded',
    'show_chart_rounded',
    'currency_rupee_rounded',
    'meeting_room_rounded',
    'work_history_rounded',
    'euro_rounded',
    'credit_score_rounded',
    'loyalty_rounded',
    'pie_chart_rounded',
    'copyright_rounded',
    'track_changes_rounded',
    'price_check_rounded',
    'schema_rounded',
    'euro_symbol_rounded',
    'add_business_rounded',
    'add_card_rounded',
    'card_membership_rounded',
    'price_change_rounded',
    'donut_large_rounded',
    'data_exploration_rounded',
    'bubble_chart_rounded',
    'donut_small_rounded',
    'contactless_rounded',
    'money_rounded',
    'stacked_line_chart_rounded',
    'stacked_bar_chart_rounded',
    'toll_rounded',
    'cases_rounded',
    'currency_yen_rounded',
    'area_chart_rounded',
    'currency_pound_rounded',
    'room_preferences_rounded',
    'add_chart_rounded',
    'shop_rounded',
    'domain_add_rounded',
    'scatter_plot_rounded',
    'card_travel_rounded',
    'legend_toggle_rounded',
    'mediation_rounded',
    'ssid_chart_rounded',
    'candlestick_chart_rounded',
    'waterfall_chart_rounded',
    'currency_ruble_rounded',
    'shop_two_rounded',
    'next_week_rounded',
    'troubleshoot_rounded',
    'multiline_chart_rounded',
  ],
  Communicate: [
    'mail_rounded',
    'call_rounded',
    'notifications_rounded',
    'send_rounded',
    'chat_rounded',
    'link_rounded',
    'forum_rounded',
    'inventory_2_rounded',
    'chat_bubble_rounded',
    'phone_in_talk_rounded',
    'notifications_active_rounded',
    'alternate_email_rounded',
    'sms_rounded',
    'hub_rounded',
    'comment_rounded',
    'person_search_rounded',
    'import_contacts_rounded',
    'contacts_rounded',
    'contact_mail_rounded',
    'forward_to_inbox_rounded',
    'reviews_rounded',
    'mark_email_unread_rounded',
    'lan_rounded',
    'hourglass_top_rounded',
    'contact_phone_rounded',
    'mode_comment_rounded',
    'inbox_rounded',
    'drafts_rounded',
    'outgoing_mail',
    'hourglass_bottom_rounded',
    'mark_email_read_rounded',
    'g_translate_rounded',
    'add_comment_rounded',
    'phone_enabled_rounded',
    'speaker_notes_rounded',
    'perm_phone_msg_rounded',
    'co_present_rounded',
    'cell_tower_rounded',
    'topic_rounded',
    'mark_chat_unread_rounded',
    'schedule_send_rounded',
    'satellite_alt_rounded',
    'dialpad_rounded',
    'call_made_rounded',
    'mark_unread_chat_alt_rounded',
    // 'three_p',
    '3p_rounded',
    'unarchive_rounded',
    'cancel_presentation_rounded',
    'move_to_inbox_rounded',
    'next_plan_rounded',
    'mark_as_unread_rounded',
    'attach_email_rounded',
    'phonelink_ring_rounded',
    'unsubscribe_rounded',
    'add_call',
    'markunread_mailbox_rounded',
    'phone_forwarded_rounded',
    'all_inbox_rounded',
    'voice_chat_rounded',
    'mail_lock_rounded',
    'voicemail_rounded',
    'attribution_rounded',
    'mark_chat_read_rounded',
    'duo_rounded',
    'upcoming_rounded',
    'swap_calls_rounded',
    'picture_in_picture_alt_rounded',
    'outbox_rounded',
    'spoke_rounded',
    'ring_volume_rounded',
    'quickreply_rounded',
    'phone_missed_rounded',
    'picture_in_picture_rounded',
    'comment_bank_rounded',
    'send_and_archive_rounded',
    'settings_bluetooth_rounded',
    'score_rounded',
    'pause_presentation_rounded',
    'speaker_phone_rounded',
    'call_merge_rounded',
    'mms_rounded',
    'rtt_rounded',
  ],
  Social: [
    'person_rounded',
    'group_rounded',
    'share_rounded',
    'groups_rounded',
    'thumb_up_rounded',
    'public_rounded',
    'person_add_rounded',
    'handshake_rounded',
    'support_agent_rounded',
    'face_rounded',
    'sentiment_satisfied_rounded',
    'rocket_launch_rounded',
    'group_add_rounded',
    'workspace_premium_rounded',
    'psychology_rounded',
    'emoji_objects_rounded',
    'travel_explore_rounded',
    'water_drop_rounded',
    'eco_rounded',
    'pets_rounded',
    'mood_rounded',
    'sunny',
    'health_and_safety_rounded',
    'sentiment_very_satisfied_rounded',
    'military_tech_rounded',
    'gavel_rounded',
    'recycling_rounded',
    'diamond_rounded',
    'monitor_heart_rounded',
    'emoji_people_rounded',
    'workspaces_rounded',
    'vaccines_rounded',
    'recommend_rounded',
    'compost_rounded',
    'forest_rounded',
    'waving_hand_rounded',
    'wc_rounded',
    'sentiment_neutral_rounded',
    'group_work_rounded',
    'medication_rounded',
    'front_hand_rounded',
    'cruelty_free_rounded',
    'man_rounded',
    'add_reaction_rounded',
    'medical_information_rounded',
    'rocket_rounded',
    'coronavirus_rounded',
    'female_rounded',
    'emoji_nature_rounded',
    'cookie_rounded',
    'bedtime_rounded',
    'connect_without_contact_rounded',
    'woman_rounded',
    'male_rounded',
    'solar_power_rounded',
    'masks_rounded',
    'hive_rounded',
    'boy_rounded',
    'whatshot_rounded',
    'wind_power_rounded',
    'emoji_food_beverage_rounded',
    'emoji_transportation_rounded',
    'elderly_rounded',
    'reduce_capacity_rounded',
    'pregnant_woman_rounded',
    'egg_rounded',
    'bloodtype_rounded',
    'group_remove_rounded',
    'medication_liquid_rounded',
    'co2_rounded',
    'follow_the_signs_rounded',
    'oil_barrel_rounded',
    'elderly_woman_rounded',
    'sanitizer_rounded',
    'clean_hands_rounded',
    'south_america_rounded',
    'social_distance_rounded',
    'sign_language_rounded',
    'flood_rounded',
    'emoji_symbols_rounded',
    'egg_alt_rounded',
    'cyclone_rounded',
    'girl_rounded',
    'tsunami_rounded',
    'tornado_rounded',
    'safety_divider_rounded',
    'landslide_rounded',
    'volcano_rounded',
    '6_ft_apart_rounded',
  ],
};

// 根据iconName 获取编码
export const findIconToCode = (iconName) => {
  return MATERIAL_ICONS[iconName];
};

// 根据编码获取iconName
export const findIconCodeToName = (iconCode) => {
  // 确保输入的编码格式正确
  if (!iconCode || !iconCode.startsWith('0x')) {
    return defaultIconName;
  }
  // 遍历 MATERIAL_ICONS 查找匹配的编码
  for (const [iconName, code] of Object.entries(MATERIAL_ICONS)) {
    if (code === iconCode) {
      return iconName;
    }
  }
  // 如果没有找到匹配的编码，返回默认图标
  return defaultIconName;
};

// 根据#十六进制的颜色，返回颜色对应的key
export const findColorToKey = (color) => {
  return Object.keys(colorOptions).find((key) => colorOptions[key] === color);
};

// 将颜色argb 转为hex
export const argbToHex = (colorString) => {
  // 去掉 '0x'
  const hex = colorString.slice(2);
  // 分割 ARGB -> AA, RR, GG, BB
  const a = hex.slice(0, 2);
  const r = hex.slice(2, 4);
  const g = hex.slice(4, 6);
  const b = hex.slice(6, 8);

  // 如果透明度是 FF（不透明），返回 #RRGGBB；否则返回 #RRGGBBAA
  return a === 'FF' ? `#${r}${g}${b}` : `#${r}${g}${b}${a}`;
};

// 将数值转为十六进制字符串
export const convertToHexColor = (colorString) => {
  try {
    // 处理 null 或空字符串
    if (!colorString || colorString.trim() === '') {
      return argbToHex(defaultARGBColor);
    }
    colorString = colorString.trim();
    // 如果字符串以 "0x" 开头
    if (colorString.startsWith('0x')) {
      return argbToHex(colorString);
    }
    // 如果是有效的十六进制字符串（例如 "FF424953" 或 "424953"）
    if (/^[0-9a-fA-F]{6,8}$/.test(colorString)) {
      return argbToHex('0x' + colorString);
    }
    // 如果是纯数字字符串
    if (!isNaN(colorString)) {
      return argbToHex(colorString);
    }
    // 解析失败，返回默认颜色
    return argbToHex(defaultARGBColor);
  } catch (e) {
    return argbToHex(defaultARGBColor);
  }
};

// 将#16进制颜色转成透明为0.3的颜色
export const convertToTransparentColor = (color) => {
  return color.replace('#', '').replace(/^(.{6})/, '$133');
};

// 获取页面详情背景色
export const getFileDetailColor = (data, translateKey) => {
  return data.custom_type === 'community'
    ? `#${convertToTransparentColor(data[translateKey].iconColor)}`
    : categoryFileDetailColorList[data.category_id];
};

// 根据colorOptions的颜色，返回随机颜色
export const getRandomColor = () => {
  const values = Object.values(colorOptions);
  const randomIndex = Math.floor(Math.random() * values.length);
  return values[randomIndex];
};

// 根据categoryIcons中随机选择分类，然后从选中的分类中随机选择图标
export const getRandomIconName = () => {
  const keys = Object.keys(categoryIcons);
  const randomIndex = Math.floor(Math.random() * keys.length);
  const randomCategory = keys[randomIndex];
  const randomIcon =
    categoryIcons[randomCategory][Math.floor(Math.random() * categoryIcons[randomCategory].length)];
  return randomIcon;
};

export const categoryLocalizations = {
  General: 'Device_manage_general_title',
  Business: 'business_text',
  Communicate: 'communicate_text',
  Social: 'Me_about_social',
};
// 获取所有分类的多语言名称key
export const getCategoryLocalizations = (categoryname) => {
  return categoryLocalizations[categoryname];
};

/**
 * Material Icons 显示控制
 * 解决字体加载时的文字闪现问题
 */
export const initMaterialIconsDisplay = () => {
  let isIconsReady = false;
  let loadTimer = null;
  let fallbackTimer = null;

  // 清除所有定时器
  const clearTimers = () => {
    if (loadTimer) {
      clearTimeout(loadTimer);
      loadTimer = null;
    }
    if (fallbackTimer) {
      clearTimeout(fallbackTimer);
      fallbackTimer = null;
    }
  };

  // 显示图标（只执行一次）
  const showMaterialIcons = () => {
    if (isIconsReady) return; // 防止重复执行
    isIconsReady = true;

    // 清除所有定时器
    clearTimers();

    // 添加class来激活图标显示
    document.body.classList.add('fonts-ready');

    // 给现有的图标元素添加loaded class
    document.querySelectorAll('.material-symbols-rounded').forEach((el) => {
      el.classList.add('loaded');
    });

    console.log('Material Icons ready to display');
  };

  // 页面加载完成后显示
  const handlePageLoad = () => {
    loadTimer = setTimeout(showMaterialIcons, 500);
  };

  // 根据页面状态设置显示策略
  if (document.readyState === 'complete') {
    // 页面已完全加载
    handlePageLoad();
  } else {
    // 等待页面加载完成
    window.addEventListener('load', handlePageLoad, { once: true });
  }

  // 兜底策略：2秒后强制显示
  fallbackTimer = setTimeout(() => {
    if (!isIconsReady) {
      console.log('Material Icons fallback display triggered');
      showMaterialIcons();
    }
  }, 2000);

  // 页面卸载时清理定时器
  window.addEventListener('beforeunload', clearTimers, { once: true });

  // 返回清理函数，供外部调用
  return {
    showNow: showMaterialIcons,
    cleanup: clearTimers,
  };
};
