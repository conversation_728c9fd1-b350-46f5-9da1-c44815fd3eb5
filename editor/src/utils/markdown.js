// 同步处理管道函数
export const executeInOrder = (...funcs) => {
  if (funcs.length === 1) {
    return funcs[0];
  }

  return funcs.reduce((prevFunc, nextFunc) => {
    return (...args) => {
      try {
        const result = prevFunc(...args);
        return nextFunc(result);
      } catch (error) {
        console.error('executeInOrder error', error);
        throw error;
      }
    };
  });
};

// 字符串检测：开头空格数
export const getLeadingSpacesCount = (str) => {
  const match = str.match(/^(\s*)/);
  if (match) {
    return match[0].length;
  }
  return 0;
};

// 语种探测：检测文本是否包含中文或日文字符
export const SIMPLIFIED_CHINESE_REGEX = /[\u4e00-\u9fff]/u; // 检测简体中文
export const TRADITIONAL_CHINESE_REGEX =
  /[\u3400-\u4DBF\uF900-\uFAFF\u{20000}-\u{2A6DF}\u{2A700}-\u{2B73F}\u{2B740}-\u{2B81F}\u{2B820}-\u{2CEAF}\u{2CEB0}-\u{2EBEF}]/u; // 检测繁体中文
export const JAPANESE_REGEX =
  /[\u3040-\u309F\u30A0-\u30FF\u31F0-\u31FF\u{1B000}-\u{1B0FF}\u{1B100}-\u{1B12F}\u{1B130}-\u{1B16F}]/u; // 检测日文
export const detectCJKLanguage = (
  text,
  zhRegex = SIMPLIFIED_CHINESE_REGEX,
  zhHKRegex = TRADITIONAL_CHINESE_REGEX,
  jaRegex = JAPANESE_REGEX,
) => {
  if (zhRegex.test(text)) {
    return 'Simplified Chinese';
  } else if (zhHKRegex.test(text)) {
    return 'Traditional Chinese';
  } else if (jaRegex.test(text)) {
    return 'Japanese';
  } else {
    return 'IsNotCJK';
  }
};

// Markdown格式化函数：处理speaker
export const SPEAKER_REGEX = /(\d+\.\s*\[[^\]]+\]):/g; // 检测speaker
export const markdownSpeakerEditFormat = (text, speakerRegex = SPEAKER_REGEX) => {
  try {
    // 检查是否包含中文或日文字符
    const isCJK = detectCJKLanguage(text) !== 'IsNotCJK';

    if (isCJK) {
      // 替换类似 "1. [我是谁]:" 的内容为 "1. [我是谁] :"
      const newText = text.replace(speakerRegex, '$1 :');
      return newText;
    }

    // 如果文本不是中文或日文，返回原文本
    return text;
  } catch (error) {
    console.log('markdownSpeakerEditFormat error', error);
  }
};

// Mind格式化函数：消除粗体
export const DETECT_FORMAT_6 = /\*\*(.*?)\*\*/g;
export const mindTextBoldLineFormat = (line, regex = DETECT_FORMAT_6) => {
  // 处理特殊行：以**xxx**作为子单元，如果是特殊行，就消除粗体格式
  return line.replace(regex, '$1');
};

// Mind格式化函数：处理融合关键字的特殊行
export const DETECT_FORMAT_7 = /(`{1,6})(.*?)\2/g;
export const mindTextKeywordsmixLineFormat = (line, regex = DETECT_FORMAT_7) => {
  // 处理特殊行：以``xxx``作为子单元的混合特殊行，就消除关键字格式
  return line.replace(regex, '$2');
};

// Mind格式化函数：处理关键字特殊行
export const DETECT_FORMAT_8_1 = /^([\s]*(`{1,3})[^`]*\2[\s]*)+$/;
export const DETECT_FORMAT_8_2 = /^(`{1,3})[\s]*".*"[\s]*\1$/;
export const mindTextKeywordsLineDetect1 = (
  line,
  regex1 = DETECT_FORMAT_8_1,
  regex2 = DETECT_FORMAT_8_2,
) => {
  return regex1.test(line) && regex2.test(line);
};
export const mindTextKeywordsLineDetect2 = (
  line,
  regex1 = DETECT_FORMAT_8_1,
  regex2 = DETECT_FORMAT_8_2,
) => {
  return regex1.test(line) && !regex2.test(line);
};
export const mindTextKeywordsLineFormat1 = (line) => {
  return '- ' + line.replace(/`/g, '');
};
export const mindTextKeywordsLineFormat2 = (line) => {
  // 处理特殊行：类```xxx``` ``xxx``格式的句类，则在关键字组前加上"关键字: "
  const keywords = line.match(/(`{1,3})[^`]*\1/g).map((keyword) => {
    return keyword.replace(/`+/g, "'");
  });

  line = `${[...keywords].join(', ')}`;

  return line;
};
/* export const mindTextKeywordsLineFormat2 = (line) => {
  // 处理特殊行：类```xxx``` ``xxx``格式的句类，则在关键字组前加上"关键字: "
  const keywords = line.match(/(`{1,3})[^`]*\1/g).map((keyword) => {
    return keyword.replace(/`/g, "");
  });
  // 判断line的语种，识别出是中文，英文还是日文
  const language = detectCJKLanguage(line);
  switch (language) {
    case "Simplified Chinese":
    case "Traditional Chinese":
      line = `- 关键字: ${keywords.join(", ")}`;
      break;
    case "Japanese":
      line = `- キーワード: ${keywords.join(", ")}`;
      break;
    default:
      line = `- Keyword: ${[...keywords].join(", ")}`;
  }

  return line;
}; */

// Mind格式化函数：数字序号开头特殊处理
export const DETECT_REGEX_1 = /^\s*\d+\.\s+/;
export const mindTextNumberLineDetect = (line, regex = DETECT_REGEX_1) => {
  return regex.test(line);
};
export const mindTextNumberLineFormat = (line) => {
  const emptyNode = getLeadingSpacesCount(line);
  line = line.trim();
  switch (
    emptyNode // 根据MD开头空格格式化规则分级处理
  ) {
    case 0:
      return '#### ' + line;
    case 1:
      return '#### ' + line;
    case 2:
      return '##### ' + line;
    case 3:
      return '##### ' + line;
    case 4:
      return '###### ' + line;
    case 5:
      return '###### ' + line;
    default:
      return line;
  }
};

// Mind格式化函数：* 序号开头特殊处理
export const DETECT_REGEX_2 = /^\s*\* /;
export const FORMAT_REGEX_2 = /^\s*\* /g;
export const mindTextAsteriskLineDetect = (line, regex = DETECT_REGEX_2) => {
  return regex.test(line);
};
export const mindTextAsteriskLineFormat = (line, regex = FORMAT_REGEX_2) => {
  const emptyNode = getLeadingSpacesCount(line);
  line = line.trim();
  line = line.replace(/^\* /, ''); // 去掉开头的"* "
  switch (
    emptyNode // 根据MD开头空格格式化规则分级处理
  ) {
    case 0:
      return '#### ' + line;
    case 1:
      return '#### ' + line;
    case 2:
      return '##### ' + line;
    case 3:
      return '##### ' + line;
    case 4:
      return '###### ' + line;
    case 5:
      return '###### ' + line;
    default:
      return '- ' + line;
  }
};

// Mind格式化函数：引用特殊处理
export const DETECT_REGEX_3 = /^\s*>\s*|^\s*-\s*>\s*/;
export const FORMAT_REGEX_3 = /^\s*>\s*|^\s*-\s*>\s*/;
export const mindTextQuoteLineDetect = (line, regex = DETECT_REGEX_3) => {
  return regex.test(line);
};
export const mindTextQuoteLineFormat = (line, regex = FORMAT_REGEX_3) => {
  return '- ' + line.replace(regex, '');
};

// Mind格式化函数：类似 [] 或 -[] 特殊处理
export const DETECT_REGEX_4 = /^\s*-\s*\[ \]\s*|^\s*\[ \]\s*/;
export const FORMAT_REGEX_4 = /^\s*-\s*\[ \]\s*|^\s*\[ \]\s*/;
export const mindTextCheckboxLineDetect = (line, regex = DETECT_REGEX_4) => {
  return regex.test(line);
};
export const mindTextCheckboxLineFormat = (line, regex = FORMAT_REGEX_4) => {
  return '- [ ] ' + line.replace(regex, '');
};

// Mind格式化函数：处理正文行文本
export const DETECT_REGEX_5 = /^\s*#+ |^\s*\*+ |^\s*- |^\s*---|^\s*`.*`|^\s*``.*``|^\s*```.*```.*/;
export const mindTextStandardLineDetect = (line, regex = DETECT_REGEX_5) => {
  return !regex.test(line);
};
export const mindTextStandardLineFormat = (line) => {
  line = line.trim();
  return '- ' + line;
};

// Mind格式化函数：段落- 开头特殊处理
export const DETECT_REGEX_6 = /^\s*- /;
export const mindTextParagraphLineDetect = (line, regex = DETECT_REGEX_6) => {
  return regex.test(line);
};
export const mindTextParagraphLineFormat = (line) => {
  const emptyNode = getLeadingSpacesCount(line);
  line = line.trim();
  line = line.replace(/^- /, ''); // 去掉开头的"- "
  switch (
    emptyNode // 根据MD开头空格格式化规则分级处理
  ) {
    case 0:
      return '#### ' + line;
    case 1:
      return '#### ' + line;
    case 2:
      return '##### ' + line;
    case 3:
      return '##### ' + line;
    case 4:
      return '###### ' + line;
    case 5:
      return '###### ' + line;
    default:
      return line;
  }
};

// Mind格式化函数：关键字段落特殊处理：结论
export const DETECT_REGEX_7 = /^\s*> (\*{0,2})结论\1.*|^\s*> (\*{0,2})Conclusion\1.*/gm;
export const mindTextSpecialKeyLineDetect = (line, regex = DETECT_REGEX_7) => {
  return regex.test(line);
};
export const mindTextSpecialKeyLineFormat = (line) => {
  const emptyNode = getLeadingSpacesCount(line);
  line = line.trim();
  line = line.replace(/^> /, ''); // 去掉开头的"> "
  switch (
    emptyNode // 根据MD开头空格格式化规则分级处理
  ) {
    case 0:
      return '#### ' + line;
    case 1:
      return '#### ' + line;
    case 2:
      return '##### ' + line;
    case 3:
      return '##### ' + line;
    case 4:
      return '###### ' + line;
    case 5:
      return '###### ' + line;
    default:
      return line;
  }
};

// Mind格式化函数：处理行文本
export const mindTextLineFormat = (paragraph) => {
  // 分割文本
  const lines = paragraph.split('\n');

  // 处理段落文本
  const newLines = lines.map((line) => {
    // 处理特殊行/粗体：以**xxx**作为子单元，如果是特殊行，就消除粗体格式
    line = mindTextBoldLineFormat(line);

    // 处理特殊行：以```xxx```作为子单元或以``xx``或以`xx`作为子单元，如果是特殊行，就分割并在每一行前面加上 "-"，消除关键字格式，创建关键字标题
    if (mindTextKeywordsLineDetect1(line)) {
      return mindTextKeywordsLineFormat1(line);
    }

    // 处理特殊行：以```xxx```作为子单元或以``xx``或以`xx`作为子单元，如果是特殊行，就分割并在每一行前面加上 "-"，消除关键字格式，创建关键字标题
    if (mindTextKeywordsLineDetect2(line)) {
      return mindTextKeywordsLineFormat2(line);
    }

    // 处理特殊行：以``xxx``作为子单元的混合特殊行，就消除关键字格式
    line = mindTextKeywordsmixLineFormat(line);

    // 处理行开头：检查行是否以数字序号开头，如果是，就在前面加上 # 处理
    if (mindTextNumberLineDetect(line)) {
      return mindTextNumberLineFormat(line);
    }

    // 处理行开头：检查行是否以段落分段- 开头，如果是，就在前面加上 # 处理
    if (mindTextParagraphLineDetect(line)) {
      return mindTextParagraphLineFormat(line);
    }

    // 处理行开头：检查行是否以*序号开头，如果是，就在前面加上 # 处理
    if (mindTextAsteriskLineDetect(line)) {
      return mindTextAsteriskLineFormat(line);
    }

    // 处理行开头：检查行是否以特殊总结类文本开头，如果是，就在前面加上 # 处理
    if (mindTextSpecialKeyLineDetect(line)) {
      return mindTextSpecialKeyLineFormat(line);
    }

    // 处理行开头：检查行是否以引用开头，如果是，就在文本前加上"引用:"
    if (mindTextQuoteLineDetect(line)) {
      return mindTextQuoteLineFormat(line);
    }

    // 处理行开头：检查行是否以类似 - [ ]  或者  [ ] 开头，如果是就把内容替换为- [ ]
    if (mindTextCheckboxLineDetect(line)) {
      return mindTextCheckboxLineFormat(line);
    }

    // 处理行开头：检查行是否以特殊格式开头，如果行不以特殊格式开头，就在前面加上 "-"
    if (mindTextStandardLineDetect(line)) {
      return mindTextStandardLineFormat(line);
    }

    // 处理特殊行/前后空格
    line = line.trim();

    return line;
  });

  // 将处理后的行重新组合成一个字符串
  const newParagraph = newLines
    .filter((line) => {
      return !/^[#-\s\n\r]*$/gm.test(line);
    })
    .join('\n');
  return newParagraph;
};

export const mindFormat = executeInOrder(mindTextLineFormat);
export const markdownFormat = executeInOrder(markdownSpeakerEditFormat);
