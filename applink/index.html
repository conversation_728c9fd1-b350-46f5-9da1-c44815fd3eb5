<!--
 * @Author: meta-kk <EMAIL>
 * @Date: 2024-08-14 16:46:03
 * @LastEditors: meta-kk <EMAIL>
 * @LastEditTime: 2025-04-23 12:13:46
 * @FilePath: /plaud-web-dist/download/index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <link rel="stylesheet" href="./reset.css" />
    <title>PLAUD App Link</title>
    <style type="text/css">
      html,
      body {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
      .main {
        position: relative;
        width: 100%;
        height: 100%;
      }
      .bg {
        width: 100%;
        height: 100vh;
        background-size: cover;
        background-position: center;
      }
      .slogan {
        width: 216px;
        height: 68px;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 40px;
        left: 0;
        margin: auto;
      }
      .slogan img,
      .slogan source {
        width: 100%;
        height: auto;
      }
      .slogan .plaud {
        width: 34px;
        height: 34px;
        position: absolute;
        z-index: 100;
        right: 0;
        bottom: 0;
      }
      .download {
        position: fixed;
        width: 314px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        box-sizing: border-box;
        bottom: 40px;
        left: 0;
        right: 0;
        margin: auto;
        color: #fff;
        background-color: #f14348;
        border-radius: 36px;
        font-family: "SF Pro SC", "SF Pro Text", "SF Pro Icons", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
        transition: transform 0.1s ease;
      }
      .download:active {
        transform: scale(0.99);
      }
      .cover {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>
  <body>
    <div class="main cover">
      <div class="bg">
        <picture>
          <source srcset="./static/images/background.webp" type="image/webp">
          <img class="bg" src="./static/images/background.png" alt="Background Image">
        </picture>
      </div>
      <div class="slogan">
        <picture>
          <source srcset="./static/images/slogan.webp" type="image/webp" />
          <img src="./static/images/slogan.png" alt="Slogan Image" />
        </picture>
        <img class="plaud" src="./static/images/plaud.gif" alt="Plaud Gif">
      </div>
      <div class="download" onclick="handleDownloadClick()">PLAUD APP Download Guide</div>
    </div>
    <script>
      /**
       * 检测用户设备类型
       * @returns {string} 返回设备类型: "android", "apple", 或 "unknown"
       */
      function detectDevice() {
        const userAgent = navigator.userAgent;

        if (/Android/i.test(userAgent)) {
          return "android";
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
          return "apple";
        } else {
          return "unknown";
        }
      }
      
      /**
       * 获取URL中的route参数
       * @returns {string} 路由参数值，如果不存在则返回空字符串
       */
      function getRouteParameter() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('route') || '';
      }

      /**
       * 获取URL中的所有查询参数
       * @returns {string} 所有查询参数的字符串形式
       */
      function getAllQueryParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.toString();
      }

      /**
       * 构造应用深链接URL并尝试打开应用
       * @param {string} route - 路由参数
       * @param {string} allParams - 所有查询参数
       */
      function directToAppRoute(route, allParams) {
        // 构造PLAUD应用的深链接URL
        let appUrl = 'plaud://plaud.ai';
        
        if (route) {
          // 如果有路由参数，添加router参数
          appUrl += `?router=${route}`;
          // 如果还有其他参数，一并添加
          if (allParams) {
            const params = new URLSearchParams(allParams);
            params.delete('route'); // 删除route参数，因为已经处理了
            const remainingParams = params.toString();
            if (remainingParams) {
              appUrl += `&${remainingParams}`;
            }
          }
        } else if (allParams) {
          // 如果没有路由但有其他参数，直接添加
          appUrl += `?${allParams}`;
        }
        
        // 尝试通过深链接打开应用
        window.location.href = appUrl;
      }
      
      /**
       * 根据设备类型执行相应的跳转逻辑
       * @param {string} device - 设备类型
       */
      function directToApp(device) {
        const allParams = getAllQueryParameters();
        const routeParam = getRouteParameter();
        
        switch (device) {
          case "android":
            // Android设备：先尝试打开应用，6秒后跳转到Google Play
            directToAppRoute(routeParam, allParams);
            setTimeout(() => {
              window.location.href =
              "https://play.google.com/store/apps/details?id=ai.plaud.android.plaud";
            }, 6000);
            break;
          case "apple":
            // iOS设备：先尝试打开应用，6秒后跳转到App Store
            directToAppRoute(routeParam, allParams);
            setTimeout(() => {
              window.location.href =
              "https://apps.apple.com/app/plaud-ai-voice-recorder/id6450364080";
            }, 6000);
            break;
          default:
            // 未知设备：直接跳转到默认页面
            directToDefaultPage(6000);
        }
      }

      /**
       * 跳转到默认支持页面
       * @param {number} time - 延迟时间（毫秒），默认为0
       */
      function directToDefaultPage(time = 0) {
        setTimeout(() => {
          window.location.href = "https://www.plaud.ai/pages/support";
        }, time);
      }

      /**
       * 根据用户的语言环境设置按钮文本
       * 支持中文简体、繁体、日语和英语
       */
      function setButtonText() {
        const lang = navigator.language || navigator.userLanguage;
        const btn = document.querySelector(".download");
        switch (lang) {
          case "zh":
          case "zh-CN":
            btn.textContent = "PLAUD APP 下载指南";
            break;
          case "zh-TW":
          case "zh-HK":
            btn.textContent = "PLAUD APP 下載指南";
            break;
          case "ja":
            btn.textContent = "PLAUDアプリダウンロードガイド";
            break;
          default:
            btn.textContent = "PLAUD APP Download Guide";
        }
      }

      /**
       * 处理下载按钮点击事件
       * 点击后短暂延迟跳转到默认页面
       */
      function handleDownloadClick() {
        directToDefaultPage(100);
      }

      /**
       * 主函数：页面加载时执行的核心逻辑
       * 1. 检测设备类型
       * 2. 尝试打开应用或跳转到应用商店
       * 3. 设置按钮文本
       * 4. 错误处理：如果出现异常，直接跳转到默认页面
       */
      function main() {
        try {
          const device = detectDevice();
          directToApp(device);
          setButtonText();
        } catch (error) {
          // 捕获任何异常，确保页面不会卡死
          directToDefaultPage();
        }
      }

      // 页面加载完成后立即执行主函数
      main();
    </script>
  </body>
</html>
