{"name": "buydetail", "publicPath": "/", "cdn": false, "cdnDeploy": false, "sentry": false, "project": "web", "version": "1.0.0", "description": "", "main": "src/index.tsx", "private": true, "author": "nebulon", "license": "UNLICENSED", "scripts": {"start": "nebulon start", "build": "nebulon build", "format": "nebulon format", "lint": "nebulon lint", "analyse": "nebulon analyse", "copy": "nebulon copy", "test": "nebulon test", "scan": "nebulon scan"}, "husky": {"hooks": {"pre-commit": "git add . && lint-staged"}}, "lint-staged": {"./src/**/*.{js,jsx,ts,tsx,vue}": ["prettier --write", "eslint", "git add ."]}, "eslintConfig": {"extends": ["./node_modules/nebulon/scripts/lint.js"]}, "prettier": "nebulon/scripts/prettier.js", "babel": {"extends": "nebulon/scripts/babel.js"}, "browserslist": "cover 99.5%", "devDependencies": {"nebulon": "latest", "vue-loader": "15.10.1", "vue-template-compiler": "2.7.14"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@flowjs/flow.js": "^2.14.1", "axios": "^0.17.1", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "firebase": "^9.2.0", "github-markdown-css": "^5.2.0", "html-docx-js": "^0.3.1", "html2canvas": "^1.0.0", "jquery": "^3.7.0", "js-audio-recorder": "^1.0.7", "js-cookie": "^3.0.5", "jsmind": "^0.5.5", "lottie-web": "^5.12.2", "markmap-common": "^0.15.3", "markmap-lib": "^0.15.4", "markmap-toolbar": "^0.15.4", "markmap-view": "^0.15.4", "mavon-editor": "^2.10.4", "perfect-scrollbar": "^1.5.5", "uuid": "^10.0.0", "vant": "^2.13.2", "vue": "2.7.14", "vue-clipboard2": "^0.3.3", "vue-i18n": "^6.1.3", "vue-markdown": "^2.2.4", "vue-perfect-scrollbar": "^0.2.1", "vue-router": "^3.0.1", "vuex": "^3.0.1", "wavesurfer.js": "^6.6.4"}, "host": {"ip": "", "port": 10000, "env": {}}}