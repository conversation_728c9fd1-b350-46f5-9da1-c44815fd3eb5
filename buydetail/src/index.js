import 'web/src/common/common';
import 'web/src/common/date-utils';
import 'web/src/common/dates';
import Vue from 'vue';
import BuyDetailPage from 'web/src/forApp/BuyDetailPage';

import RequestMixins from 'web/src/util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from 'web/src/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from 'web/src/components/common/message';
Vue.prototype.setMessage = Message;

import 'web/src/util/Directive';
import i18n from 'web/src/language/h5/index';
Vue.config.productionTip = false;

import 'web/src/styles/buydetail.scss';
new Vue({
  el: '#app',
  i18n,
  template: '<BuyDetailPage/>',
  components: { BuyDetailPage }
});
