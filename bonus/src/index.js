import 'web/src/common/common';
import 'web/src/common/date-utils';
import 'web/src/common/dates';
import Vue from 'vue';
import BonusApp from 'web/src/BonusApp';

import RequestMixins from 'web/src/util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from 'web/src/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from 'web/src/components/common/message';
Vue.prototype.setMessage = Message;

import 'web/src/util/Directive';
import i18n from 'web/src/language/bonus/index';
Vue.config.productionTip = false;

import 'web/src/styles/bonus.scss';
new Vue({
  el: '#app',
  i18n,
  template: '<BonusApp/>',
  components: { BonusApp },
  data: function () {
    return {
      BUS: new Vue(),
    };
  },
  mounted() {
    let language = SysTool.GetQueryString('language');
    if (language) {
      language = language.toLowerCase();
      sessionStorage.setItem('infolang', language);
      this.$i18n.locale = language;
    }
  },
});
