/**
   * 固定回调，在这里重写webpack的配置
   * @param {*} config webpack的配置,可在此基础上进行修改
   * @param {*} env 环境变了，development｜production
   * @return {*} 必须返回修改后的config
*/
const pkg = require('./package.json');

module.exports = function (config, env) {
  if(env === 'development'){
    config.devServer.proxy.push({
      context: ['/api'],
      target: 'http://localhost:3000',
      pathRewrite: { '^/api': '' },
    })
  }

  if(env === 'production'){
    config.plugins[3].options.filename = `${pkg.name}.html`
  }
  config.resolve.alias.vue = 'vue/dist/vue.js';

  return config;
}
