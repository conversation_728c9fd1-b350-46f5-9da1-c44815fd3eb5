import 'web/src/common/common';
import 'web/src/common/date-utils';
import 'web/src/common/dates';
import Vue from 'vue';
import CommonApp from 'web/src/CommonApp';

import i18n from 'web/src/language/index';
Vue.config.productionTip = false;

import RequestMixins from 'web/src/util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from 'web/src//util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from 'web/src/components/common/message';
Vue.prototype.setMessage = Message;

import VueClipBoard from 'vue-clipboard2';
Vue.use(VueClipBoard);

import router from 'web/src/router/common';
import 'web/src/styles/commonapp.scss';

new Vue({
  el: '#app',
  i18n,
  router,
  template: '<CommonApp/>',
  components: { CommonApp },
});
