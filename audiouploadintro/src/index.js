import 'web/src/common/common';
import 'web/src/common/date-utils';
import 'web/src/common/dates';
import Vue from 'vue';
import AudioUploadIntroPage from 'web/src/forApp/AudioUploadIntroPage';

import RequestMixins from 'web/src/util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from 'web/src/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from 'web/src/components/common/message';
Vue.prototype.setMessage = Message;

import FirebaseMixins from 'web/src/util/FirebaseMixins';
Vue.mixin(FirebaseMixins);

import 'web/src/util/Directive';
import i18n from 'web/src/language/h5/index';
Vue.config.productionTip = false;

new Vue({
  el: '#app',
  i18n,
  template: '<AudioUploadIntroPage/>',
  components: { AudioUploadIntroPage },
});
