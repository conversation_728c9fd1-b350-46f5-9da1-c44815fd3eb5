<!DOCTYPE html>
<html>
  <head>
    <title>PLAUD NOTE</title>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
      <meta name="keywords" content="plaud, audio, transcript">
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-5N1K2J3M5R"></script>
      <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-5N1K2J3M5R');
      </script>
      <style type="text/css">
        .skeleton {
          position: fixed;
          left: 0;
          top: 0;
          width: 100%;
          height: auto;
          padding: 0 24px;
          margin: 24px 0 0 0;
          overflow-y: auto;
          box-sizing: border-box;
          display: block;
        }
        @keyframes skeleton-loading {
          0% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0 50%;
          }
        }
        .skeleton-part-common {
          background: linear-gradient(90deg, rgba(0, 0, 0, 0.06) 25%, rgba(0, 0, 0, 0.15) 37%, rgba(0, 0, 0, 0.06) 63%);
          background-size: 400% 100%;
          border-radius: 2px;
          animation-name: skeleton-loading;
          animation-duration: 1.4s;
          animation-timing-function: ease;
          animation-iteration-count: infinite;
        }
        .skeleton-part-1 {
          padding: 16px 24px;
          margin-bottom: 24px;
          background-color: #eee;
          border-radius: 16px;
        }
        .skeleton-part-1-1 {
          height: 20px;
          margin: 10px 0;
        }
        .skeleton-part-1-2 {
          height: 42px;
          margin: 10px 0;
        }
        .skeleton-part-2 {
          height: 662px;
          padding: 16px 24px;
          margin-bottom: 24px;
          background-color: #eee;
          border-radius: 16px;
        }
        .skeleton-part-2-1 {
          height: 30px;
          margin: 10px 0;
        }
        .skeleton-part-2-2 {
          height: 42px;
          margin: 10px 0;
        }
        .skeleton-part-2-3 {
          height: 106px;
          margin: 28px 40px;
        }
      </style>
    <% htmlWebpackPlugin.options.styles.forEach(function(item){ %>
      <link rel="stylesheet" href=<%=item %>></script>
    <% }) %>
    <!-- <script>
      // prettier-ignore
      var __ = '{}'
      window.__ENV__ = JSON.parse(__);
    </script> -->
    <% htmlWebpackPlugin.options.scripts.forEach(function(item){ %>
      <script src=<%=item %>></script>
    <% }) %>
  </head>

  <body class="root">
    <div id="app" class="app"></div>
    <div id="skeleton" class="skeleton">
      <div class="skeleton-part-1">
         <div class="skeleton-part-1-1 skeleton-part-common"></div>
         <div class="skeleton-part-1-2 skeleton-part-common"></div>
      </div>
      <div class="skeleton-part-2">
       <div class="skeleton-part-2-1 skeleton-part-common"></div>
       <div class="skeleton-part-2-2 skeleton-part-common"></div>
       <div class="skeleton-part-2-3 skeleton-part-common"></div>
       <div class="skeleton-part-2-2 skeleton-part-common"></div>
       <div class="skeleton-part-2-3 skeleton-part-common"></div>
       <div class="skeleton-part-2-2 skeleton-part-common"></div>
       <div class="skeleton-part-2-3 skeleton-part-common"></div>
      </div>
   </div>
  </body>
</html>
