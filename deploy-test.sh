#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查命令是否成功执行
check_command() {
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ $1 成功"
    else
        print_message $RED "✗ $1 失败"
        exit 1
    fi
}

# 备份文件
backup_file() {
    local file=$1
    if [ -f "$file" ]; then
        cp "$file" "$file.backup"
        print_message $BLUE "已备份 $file 到 $file.backup"
    fi
}

# 恢复文件
restore_files() {
    print_message $YELLOW "正在恢复备份文件..."

    if [ -f "editor/package.json.backup" ]; then
        mv "editor/package.json.backup" "editor/package.json"
        print_message $GREEN "已恢复 editor/package.json"
    fi

    if [ -f "web/package.json.backup" ]; then
        mv "web/package.json.backup" "web/package.json"
        print_message $GREEN "已恢复 web/package.json"
    fi

    if [ -f "web/src/components/common/vue3-wrapper.vue.backup" ]; then
        mv "web/src/components/common/vue3-wrapper.vue.backup" "web/src/components/common/vue3-wrapper.vue"
        print_message $GREEN "已恢复 web/src/components/common/vue3-wrapper.vue"
    fi
}

# 信号处理函数，用于在脚本被中断时恢复文件
cleanup() {
    print_message $YELLOW "\n脚本被中断，正在恢复原始文件..."
    restore_files
    exit 1
}

# 设置信号处理
trap cleanup INT TERM

# 显示图形化标题
echo
print_message $BLUE "
██████╗ ██╗      █████╗ ██╗   ██╗██████╗
██╔══██╗██║     ██╔══██╗██║   ██║██╔══██╗
██████╔╝██║     ███████║██║   ██║██║  ██║
██╔═══╝ ██║     ██╔══██║██║   ██║██║  ██║
██║     ███████╗██║  ██║╚██████╔╝██████╔╝
╚═╝     ╚══════╝╚═╝  ╚═╝ ╚═════╝ ╚═════╝
"
print_message $BLUE "PLAUD.AI大前端 测试环境部署脚本"
echo

# 获取测试环境编号
while true; do
    read -p "请输入需要部署的测试环境编号: " env_number

    # 检查输入是否为数字
    if [[ "$env_number" =~ ^[0-9]+$ ]]; then
        break
    else
        print_message $RED "请输入有效的数字！"
    fi
done

print_message $GREEN "测试环境编号: $env_number"
echo

# 备份原始文件
print_message $BLUE "正在备份原始文件..."
backup_file "editor/package.json"
backup_file "web/package.json"
backup_file "web/src/components/common/vue3-wrapper.vue"

# 修改 editor/package.json 的 name 字段
print_message $BLUE "正在修改 editor/package.json..."
if [ -f "editor/package.json" ]; then
    # 使用 sed 替换 name 字段
    sed -i.tmp "s/\"name\": \"editor\"/\"name\": \"editor$env_number\"/" editor/package.json
    rm -f editor/package.json.tmp
    check_command "修改 editor/package.json name 字段"
else
    print_message $RED "错误: editor/package.json 文件不存在"
    exit 1
fi

# 修改 web/package.json 的 name 字段并删除 publicPath 字段
print_message $BLUE "正在修改 web/package.json..."
if [ -f "web/package.json" ]; then
    # 替换 name 字段
    sed -i.tmp "s/\"name\": \"web\"/\"name\": \"web$env_number\"/" web/package.json
    # 删除 publicPath 字段（包括整行）
    sed -i.tmp '/\"publicPath\":/d' web/package.json
    rm -f web/package.json.tmp
    check_command "修改 web/package.json name 字段并删除 publicPath 字段"
else
    print_message $RED "错误: web/package.json 文件不存在"
    restore_files
    exit 1
fi

# 修改 vue3-wrapper.vue 中的 import 参数
print_message $BLUE "正在修改 vue3-wrapper.vue..."
if [ -f "web/src/components/common/vue3-wrapper.vue" ]; then
    # 替换 import 语句中的 editor 为 editor + 环境编号
    sed -i.tmp "s/import(\`editor\/vue\`)/import(\`editor$env_number\/vue\`)/" web/src/components/common/vue3-wrapper.vue
    sed -i.tmp "s/import(\`editor\/app\`)/import(\`editor$env_number\/app\`)/" web/src/components/common/vue3-wrapper.vue
    sed -i.tmp "s/import(\`editor\/svg-icon\`)/import(\`editor$env_number\/svg-icon\`)/" web/src/components/common/vue3-wrapper.vue
    rm -f web/src/components/common/vue3-wrapper.vue.tmp
    check_command "修改 vue3-wrapper.vue import 参数"
else
    print_message $RED "错误: web/src/components/common/vue3-wrapper.vue 文件不存在"
    restore_files
    exit 1
fi

echo
print_message $GREEN "所有配置文件修改完成！"
print_message $BLUE "修改摘要:"
print_message $YELLOW "  - editor/package.json: name -> editor$env_number"
print_message $YELLOW "  - web/package.json: name -> web$env_number, 删除了 publicPath 字段"
print_message $YELLOW "  - vue3-wrapper.vue: import 参数从 editor 改为 editor$env_number"
echo

# 开始构建过程
print_message $BLUE "=== 开始构建过程 ==="
echo

# 清理旧的构建产物
print_message $BLUE "正在清理旧的构建产物..."

if [ -d "editor/dist" ]; then
    print_message $YELLOW "删除 editor/dist 目录..."
    rm -rf editor/dist
    check_command "删除 editor/dist 目录"
else
    print_message $BLUE "editor/dist 目录不存在，跳过删除"
fi

if [ -d "web/dist" ]; then
    print_message $YELLOW "删除 web/dist 目录..."
    rm -rf web/dist
    check_command "删除 web/dist 目录"
else
    print_message $BLUE "web/dist 目录不存在，跳过删除"
fi

echo

# 构建 editor
print_message $BLUE "正在构建 editor..."
cd editor
npm run build
check_command "editor 构建"
cd ..

echo

# 构建 web
print_message $BLUE "正在构建 web..."
cd web
npm run build
check_command "web 构建"
cd ..

echo
print_message $GREEN "=== 构建完成！ ==="

# 拷贝构建产物到 dist_for_test 目录
print_message $BLUE "正在拷贝构建产物到 dist_for_test 目录..."

# 创建 dist_for_test 目录
if [ -d "dist_for_test" ]; then
    print_message $YELLOW "清理现有的 dist_for_test 目录..."
    rm -rf dist_for_test
fi

mkdir -p dist_for_test
check_command "创建 dist_for_test 目录"

# 拷贝 web/dist 的内容
if [ -d "web/dist" ]; then
    print_message $BLUE "拷贝 web/dist 内容..."
    cp -r web/dist/* dist_for_test/
    check_command "拷贝 web/dist 内容"
else
    print_message $RED "警告: web/dist 目录不存在"
fi

# 拷贝 editor/dist 的内容
if [ -d "editor/dist" ]; then
    print_message $BLUE "拷贝 editor/dist 内容..."
    cp -r editor/dist/* dist_for_test/
    check_command "拷贝 editor/dist 内容"
else
    print_message $RED "警告: editor/dist 目录不存在"
fi

print_message $GREEN "构建产物已拷贝到 dist_for_test 目录"
print_message $BLUE "测试环境 $env_number 部署完成"

# 询问是否恢复原始文件
echo
print_message $BLUE "注意: dist_for_test 目录将会保留，不会被删除"
read -p "是否要恢复原始配置文件？(y/n): " restore_choice
if [[ "$restore_choice" =~ ^[Yy]$ ]]; then
    restore_files
    print_message $GREEN "原始配置文件已恢复"
    print_message $BLUE "dist_for_test 目录已保留，包含测试环境 $env_number 的构建产物"
else
    print_message $YELLOW "保留修改后的配置文件"
    print_message $BLUE "如需手动恢复配置文件，可运行以下命令:"
    print_message $YELLOW "  mv editor/package.json.backup editor/package.json"
    print_message $YELLOW "  mv web/package.json.backup web/package.json"
    print_message $YELLOW "  mv web/src/components/common/vue3-wrapper.vue.backup web/src/components/common/vue3-wrapper.vue"
    print_message $BLUE "dist_for_test 目录已保留，包含测试环境 $env_number 的构建产物"
fi

echo

# 询问是否继续发布到测试环境
print_message $BLUE "=== 测试环境发布 ==="
read -p "是否继续发布到测试环境中？(y/n): " deploy_choice

if [[ "$deploy_choice" =~ ^[Yy]$ ]]; then
    print_message $GREEN "开始发布到测试环境..."

    # 检查上级目录是否有 plaud-web-dist 目录
    DEPLOY_DIR="../plaud-web-dist"

    if [ ! -d "$DEPLOY_DIR" ]; then
        print_message $YELLOW "未找到 plaud-web-dist 目录"
        while true; do
            read -p "请输入上线产物文件夹的完整路径: " custom_deploy_dir

            # 检查输入的目录是否存在
            if [ -d "$custom_deploy_dir" ]; then
                DEPLOY_DIR="$custom_deploy_dir"
                print_message $GREEN "使用目录: $DEPLOY_DIR"
                break
            else
                print_message $RED "目录不存在，请重新输入有效的目录路径"
            fi
        done
    else
        print_message $GREEN "找到上线产物目录: $DEPLOY_DIR"
    fi

    # 拷贝 dist_for_test 内容到上线产物文件夹
    print_message $BLUE "正在拷贝构建产物到上线目录..."
    if [ -d "dist_for_test" ]; then
        # 确保目标目录存在
        mkdir -p "$DEPLOY_DIR"

        # 拷贝所有内容，覆盖现有文件
        cp -rf dist_for_test/* "$DEPLOY_DIR/"
        check_command "拷贝构建产物到上线目录"

        print_message $GREEN "构建产物已拷贝到: $DEPLOY_DIR"
    else
        print_message $RED "错误: dist_for_test 目录不存在"
        exit 1
    fi

    # 切换到上线产物目录执行 git 操作
    print_message $BLUE "正在执行 Git 操作..."
    cd "$DEPLOY_DIR"

    # 检查是否是 git 仓库
    if [ ! -d ".git" ]; then
        print_message $RED "错误: $DEPLOY_DIR 不是一个 Git 仓库"
        print_message $YELLOW "请先在该目录初始化 Git 仓库或选择正确的目录"
        exit 1
    fi

    # 切换到 test 分支
    # print_message $BLUE "切换到 test 分支..."
    # git checkout test
    # if [ $? -ne 0 ]; then
    #     print_message $YELLOW "test 分支不存在，尝试创建新分支..."
    #     git checkout -b test
    #     check_command "创建并切换到 test 分支"
    # else
    #     check_command "切换到 test 分支"
    # fi

    # 拉取最新代码
    # print_message $BLUE "拉取最新代码..."
    # git pull origin test
    # if [ $? -ne 0 ]; then
    #     print_message $YELLOW "拉取失败，可能是新分支或网络问题，继续执行..."
    # else
    #     check_command "拉取最新代码"
    # fi

    # 执行 git add .
    print_message $BLUE "执行: git add ."
    git add .
    check_command "git add"

    # 执行 git commit
    COMMIT_MESSAGE="【测试发布】发布测试环境$env_number"
    print_message $BLUE "执行: git commit -m \"$COMMIT_MESSAGE\""
    git commit -m "$COMMIT_MESSAGE"
    check_command "git commit"

    # 执行 git push
    print_message $BLUE "执行: git push"
    git push
    check_command "git push"

    # 返回原目录
    cd - > /dev/null

    print_message $GREEN "=== 测试环境发布完成！ ==="
    print_message $BLUE "测试环境 $env_number 已成功发布到: $DEPLOY_DIR"

    # 显示访问地址
    ACCESS_URL="https://plaud-web-dist-test.pages.dev/web$env_number"
    echo
    print_message $GREEN "🌐 测试环境访问地址:"
    print_message $YELLOW "   $ACCESS_URL"
    echo
    print_message $BLUE "请等待几分钟让部署生效，然后访问上述地址进行测试"
else
    print_message $YELLOW "跳过测试环境发布"
fi

echo
print_message $GREEN "脚本执行完成！"