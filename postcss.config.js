const { rem } = require('./node_modules/nebulon/scripts/utils');
const postcssPresetEnv = require('postcss-preset-env');
const plugins = [
  postcssPresetEnv({ stage: 0 }),
  require('@tailwindcss/nesting'),
  require('tailwindcss'),
];
if (rem) {
  plugins.push(
    require('postcss-pxtorem')({
      rootValue: typeof rem === 'boolean' ? 100 : rem,
      unitPrecision: 5,
      propList: ['*'],
      selectorBlackList: [],
      replace: true,
      mediaQuery: false,
      minPixelValue: 0,
      exclude: /node_modules/i,
    }),
  );
}

module.exports = {
  plugins,
};
