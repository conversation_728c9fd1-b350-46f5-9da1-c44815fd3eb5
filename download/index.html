<!--
 * @Author: meta-kk <EMAIL>
 * @Date: 2024-08-14 16:46:03
 * @LastEditors: meta-kk <EMAIL>
 * @LastEditTime: 2024-08-14 16:56:28
 * @FilePath: /plaud-web-dist/download/index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <link rel="stylesheet" href="./reset.css" />
    <title>PLAUD APP Download</title>
    <style type="text/css">
      html,
      body {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
      .main {
        position: relative;
        width: 100%;
        height: 100%;
      }
      .bg {
        width: 100%;
        height: 100vh;
        background-size: cover;
        background-position: center;
      }
      .slogan {
        width: 216px;
        height: 68px;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 40px;
        left: 0;
        margin: auto;
      }
      .slogan img,
      .slogan source {
        width: 100%;
        height: auto;
      }
      .slogan .plaud {
        width: 34px;
        height: 34px;
        position: absolute;
        z-index: 100;
        right: 0;
        bottom: 0;
      }
      .download {
        position: fixed;
        width: 314px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        box-sizing: border-box;
        bottom: 40px;
        left: 0;
        right: 0;
        margin: auto;
        color: #fff;
        background-color: #f14348;
        border-radius: 36px;
        font-family: "SF Pro SC", "SF Pro Text", "SF Pro Icons", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
        transition: transform 0.1s ease;
      }
      .download:active {
        transform: scale(0.99);
      }
      .cover {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>
  <body>
    <div class="main cover">
      <div class="bg">
        <picture>
          <source srcset="./static/images/background.webp" type="image/webp">
          <img class="bg" src="./static/images/background.png" alt="Background Image">
        </picture>
      </div>
      <div class="slogan">
        <picture>
          <source srcset="./static/images/slogan.webp" type="image/webp" />
          <img src="./static/images/slogan.png" alt="Slogan Image" />
        </picture>
        <img class="plaud" src="./static/images/plaud.gif" alt="Plaud Gif">
      </div>
      <div class="download" onclick="handleDownloadClick()">PLAUD APP Download Guide</div>
    </div>
    <script>
      function detectDevice() {
        const userAgent = navigator.userAgent;

        if (/Android/i.test(userAgent)) {
          return "android";
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
          return "apple";
        } else {
          return "unknown";
        }
      }
      function directToStore(device) {
        switch (device) {
          case "android":
            window.location.href =
              "https://play.google.com/store/apps/details?id=ai.plaud.android.plaud";
            break;
          case "apple":
            window.location.href =
              "https://apps.apple.com/app/plaud-ai-voice-recorder/id6450364080";
            break;
          default:
            directToDefaultPage();
        }
      }
      function directToDefaultPage(time = 0) {
        setTimeout(() => {
          window.location.href = "https://www.plaud.ai/pages/support";
        }, time);
      }
      function setButtonText() {
        const lang = navigator.language || navigator.userLanguage;
        const btn = document.querySelector(".download");
        switch (lang) {
          case "zh":
          case "zh-CN":
            btn.textContent = "PLAUD APP 下载指南";
            break;
          case "zh-TW":
          case "zh-HK":
            btn.textContent = "PLAUD APP 下載指南";
            break;
          case "ja":
            btn.textContent = "PLAUDアプリダウンロードガイド";
            break;
          default:
            btn.textContent = "PLAUD APP Download Guide";
        }
      }
      function handleDownloadClick() {
        directToDefaultPage(100);
      }
      function main() {
        try {
          const device = detectDevice();
          directToStore(device);
          setButtonText();
        } catch (error) {
          directToDefaultPage();
        }
      }

      main();
    </script>
  </body>
</html>
