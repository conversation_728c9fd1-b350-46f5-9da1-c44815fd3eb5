// Vue 3 compatible vue-resize implementation
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';

// Simple resize observer implementation
function createResizeObserver(callback) {
  if (typeof ResizeObserver !== 'undefined') {
    return new ResizeObserver(callback);
  }
  
  // Fallback for older browsers
  return {
    observe: (element) => {
      const handleResize = () => callback([{ target: element }]);
      window.addEventListener('resize', handleResize);
      element._resizeHandler = handleResize;
    },
    unobserve: (element) => {
      if (element._resizeHandler) {
        window.removeEventListener('resize', element._resizeHandler);
        delete element._resizeHandler;
      }
    },
    disconnect: () => {}
  };
}

// Vue 3 compatible resize directive
const ResizeDirective = {
  mounted(el, binding) {
    const callback = binding.value;
    if (typeof callback === 'function') {
      const observer = createResizeObserver((entries) => {
        callback(entries[0]);
      });
      observer.observe(el);
      el._resizeObserver = observer;
    }
  },
  unmounted(el) {
    if (el._resizeObserver) {
      el._resizeObserver.disconnect();
      delete el._resizeObserver;
    }
  }
};

// Vue 3 compatible resize component
const ResizeObserver = defineComponent({
  name: 'ResizeObserver',
  emits: ['notify'],
  setup(props, { emit, slots }) {
    const elementRef = ref(null);
    
    onMounted(() => {
      if (elementRef.value) {
        const observer = createResizeObserver((entries) => {
          emit('notify', entries[0]);
        });
        observer.observe(elementRef.value);
        elementRef.value._resizeObserver = observer;
      }
    });
    
    onUnmounted(() => {
      if (elementRef.value && elementRef.value._resizeObserver) {
        elementRef.value._resizeObserver.disconnect();
      }
    });
    
    return () => {
      return slots.default?.({ ref: elementRef });
    };
  }
});

// Export for compatibility
export { ResizeDirective as directive };
export { ResizeObserver as ResizeObserver };
export default {
  directive: ResizeDirective,
  ResizeObserver
};

// Install function for Vue 3
export function install(app) {
  app.directive('resize', ResizeDirective);
  app.component('ResizeObserver', ResizeObserver);
}
