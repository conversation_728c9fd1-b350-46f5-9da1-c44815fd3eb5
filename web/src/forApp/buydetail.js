import Vue from 'vue';
import BuyDetailPage from './BuyDetailPage';

import RequestMixins from '@/util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from '@/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from '@/components/common/message';
// Vue.prototype.setMessage = Message; // This will be handled in main.js for Vue 3

import '@/util/Directive';
import i18n from '@/language/h5/index';
Vue.config.productionTip = false;

import styles from '@/styles/buydetail.scss';
new Vue({
  el: '#app',
  i18n,
  template: '<BuyDetailPage/>',
  components: { BuyDetailPage }
});
