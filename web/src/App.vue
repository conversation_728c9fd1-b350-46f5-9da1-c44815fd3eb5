<template>
  <div>
    <h1>Vue 3 SFC Test</h1>
    <p>{{ message }}</p>
    <button @click="updateMessage">{{ buttonText }}</button>
  </div>
</template>

<script>
import { ref } from 'vue';

export default {
  name: 'App',
  setup() {
    const message = ref('Vue 3 Single File Component is working!');
    const buttonText = ref('Click me (SFC version)');

    const updateMessage = () => {
      message.value = 'SFC Vue 3 reactive system works!';
      buttonText.value = 'Clicked (SFC)!';
    };

    return {
      message,
      buttonText,
      updateMessage
    };
  }
};
</script>
