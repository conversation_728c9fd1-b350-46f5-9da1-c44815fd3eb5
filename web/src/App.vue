<template>
  <div>
    <h1>Vue 3 Test</h1>
    <p>{{ message }}</p>
    <button @click="updateMessage">{{ buttonText }}</button>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';

export default {
  name: 'App',
  setup() {
    const message = ref('Vue 3 is working!');
    const buttonText = ref('Click me');

    const updateMessage = () => {
      message.value = 'Button clicked! Vue 3 reactive system works!';
      buttonText.value = 'Clicked!';
    };

    onMounted(() => {
      console.log('Vue 3 App mounted successfully');
    });

    return {
      message,
      buttonText,
      updateMessage
    };
  }
};
</script>
