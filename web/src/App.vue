<template>
  <router-view></router-view>
</template>

<script>
import { changeZELang } from '@/util/zendesk';
import eventBus from '@/util/event-bus';

export default {
  name: 'App',
  data() {
    return {
      BUS: eventBus,
      userInfo: {},
      userStateInfo: {},
      myAnalytics: null,
    };
  },
  created() {
    this.initLang();
  },
  methods: {
    initLang() {
      let webLang = window.localStorage.getItem('pweblang') || this.$i18n.locale || 'en_US';
      document.documentElement.setAttribute('lang', webLang);
    },
  },
};
</script>
