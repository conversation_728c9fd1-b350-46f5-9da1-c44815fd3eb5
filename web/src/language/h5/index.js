import Vue from 'vue';
import VueI18n from 'vue-i18n';

Vue.use(VueI18n);

const i18n = {
  globalInjection: true,
  locale: window.sessionStorage.getItem('infolang') || 'en',
  messages: {
    en: require('./en'),
    'zh-hans': require('./zh-hans'),
    'zh-hant': require('./zh-hant'),
    ja: require('./ja'),
    fr: require('./fr'),
    de: require('./de'),
    it: require('./it'),
    pt: require('./pt'),
    es: require('./es'),
    ko: require('./ko'),
  },
};

export default i18n;
