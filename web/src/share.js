import Vue from 'vue';
import ShareApp from './ShareApp';

import RequestMixins from './util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from '@/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from './components/common/message';
// Vue.prototype.setMessage = Message; // This will be handled in main.js for Vue 3
import Confirm from './components/common/confirm';
// Vue.prototype.setConfirm = Confirm; // This will be handled in main.js for Vue 3

import './util/Directive';
import i18n from './language/index';
Vue.config.productionTip = false;

import styles from '@/styles/share.scss';

import mavonEditor from 'mavon-editor';
import 'mavon-editor/dist/css/index.css';
Vue.use(mavonEditor);

new Vue({
  el: '#app',
  i18n,
  template: '<ShareApp/>',
  components: { ShareApp },
  data: function () {
    return {
      BUS: new Vue(),
    };
  },
});
