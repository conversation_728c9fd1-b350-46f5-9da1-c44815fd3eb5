<template>
  <div
    class="community-templates-new mx-auto flex-1 bg-[#FFFFFF] border border-[#E5E7EB] rounded-[6px] mr-[12px] mb-[12px] h-full"
  >
    <Vue3Wrapper
      :userInfo="$root.userStateInfo"
      :userBaseInfo="$root.userInfo"
      :onChange="onChange"
      :pageParams="pageParams"
      pageType="templateCommunity"
    />
  </div>
</template>

<script>
import SvgIcon from '../common/svg-icon/index.vue';
export default {
  name: 'CommunityTemplates',
  emits: ['member-check'],
  components: {
    Vue3Wrapper: () => import('@/components/common/vue3-wrapper.vue'),
    SvgIcon,
  },
  props: {
    collapsed: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileInfo: {},
      pageParams: {},
    };
  },
  deactivated() {},
  activated() {},
  methods: {
    onChange(data) {
      switch (data) {
        case 'template_locked':
          // 弹出会员弹窗，提示用户升级会员
          this.$emit('member-check', 0);
          break;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.community-templates-new {
  height: 100%;
  :deep(.top-info-fixed) {
    top: 47px;
  }
  :deep(.translate-wrapper) {
    padding-bottom: 60px;
  }
  :deep(.ctemplates-list-wrapper),
  :deep(.subtop-info-fixed) {
    left: 221px !important;
    top: 47px !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }
}
</style>
