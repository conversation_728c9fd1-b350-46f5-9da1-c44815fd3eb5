<template>
  <div>
    <header class="flex items-center justify-between px-6 h-[46px]">
      <!-- Conditional Icons -->
      <div class="flex items-center space-x-2">
        <template v-if="isDetailPage">
          <iconButton
            v-if="collapsed"
            name="expand"
            class="cursor-pointer w-[18px] h-[18px]"
            @click="$emit('toggle')"
          />

          <iconButton
            name="back"
            class="cursor-pointer w-6 h-6"
            @click="handleBack"
            :class="!collapsed ? 'ml-[-30px]' : ''"
          />
        </template>
      </div>

      <!-- <div class="flex-grow flex justify-center"> -->
      <!-- <input
          type="text"
          placeholder="Search title, keyword or question"
          class="w-full max-w-md px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        /> -->
      <!-- </div> -->
      <div class="flex items-center gap-2 h-[46px] min-w-0">
        <div
          class="flex items-center flex-shrink-0"
          @click="$emit('toFree')"
          v-if="hasFreeShow || userInfo.membership_type !== 'unlimited'"
        >
          <button
            v-if="userInfo.id"
            class="flex items-center h-[32px] px-[10px] border-[1px] border-[#00D0FF] rounded-[6px] bg-[#DEF9FF] text-[14px] font-normal leading-[22px] text-[#060606] hover:opacity-90 whitespace-nowrap"
          >
            <SvgIcon name="member-ai" class="w-[18px] h-[18px] mr-2 flex-shrink-0" />{{
              hasFreeShow ? $t('subscribe_annual_button_free_trial') : $t('home_top_upgrade')
            }}
          </button>
        </div>
        <!-- <a href="#" class="text-blue-500 hover:underline">Contact Support</a> -->
        <iconButton
          name="contact"
          class="cursor-pointer transition-transform flex-shrink-0"
          @click="handleContact"
          :tip="$t('home_topbar_contact_help')"
        />
        <!-- <img src="user-avatar.jpg" alt="User Avatar" class="w-8 h-8 rounded-full" /> -->
        <el-dropdown trigger="hover" placement="bottom-end">
          <div class="uNameIcon leftBig cursor-pointer flex-shrink-0">
            <img :src="userInfo.avatar" v-if="userInfo.avatar" />
            <span class="noimg" v-else>{{
              userInfo.nickname ? userInfo.nickname.substring(0, 1).toUpperCase() : ''
            }}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu
              class="user-menu w-[240px] bg-white rounded-lg shadow-lg text-sm text-[#1F1F1F]"
            >
              <!-- <div class="user-menu w-[240px] bg-white rounded-lg shadow-lg text-sm text-gray-800"> -->
              <div v-if="userInfo.nickname" class="pb-2" @click="handleSettings('general')">
                <div class="menu-item">
                  <div class="font-[#060606] font-semibold truncate leading-6">
                    {{ userInfo.nickname || '' }}
                  </div>
                  <div
                    v-if="userInfo.email && !userInfo.email.startsWith('apple-')"
                    class="text-[12px] text-[#8F959E] truncate"
                  >
                    {{ userInfo.email }}
                  </div>
                </div>
              </div>

              <div v-if="userInfo.nickname" class="h-[1px] bg-[#E5E7EB] mx-4"></div>

              <div class="pt-2">
                <div class="menu-item truncate" @click="handleSettings('general')">
                  {{ $t('setting_btn') }}
                </div>
                <div
                  href=""
                  target="_blank"
                  class="menu-item flex items-center justify-between"
                  @click="handleContact"
                >
                  <span class="truncate">{{ $t('help_center_btn') }}</span>
                  <SvgIcon name="external-link" class="w-5 h-5 text-gray-400 flex-shrink-0" />
                </div>
              </div>

              <div class="menu-item truncate" @click="handleDownload">
                {{ $t('download_btn') }}
              </div>
              <div class="menu-item flex items-center justify-between">
                {{ $t('new_version') }}
                <!-- #34C759 -->
                <el-switch v-model="newVersion" active-color="#34C759" inactive-color="#E9E9EB" />
              </div>

              <div class="menu-item truncate" @click="handleSignOut">{{ $t('sign_out_btn') }}</div>

              <!-- </div> -->
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <DownloadDialog v-if="showDownloadDialog" ref="downloadDialog" />
  </div>
</template>
<script>
import SvgIcon from '../common/svg-icon/index.vue';
import IconButton from '../common/icon-button/index.vue';
import { ElDropdown, ElDropdownMenu } from 'element-plus';
import Cookies from 'js-cookie';
import DownloadDialog from './downloadDialog.vue';

const toHelpUrl = 'https://support.plaud.ai/';
export default {
  name: 'Topbar',
  components: {
    SvgIcon,
    IconButton,
    'el-dropdown': ElDropdown,
    'el-dropdown-menu': ElDropdownMenu,
    DownloadDialog,
  },
  props: {
    hasFreeShow: {
      type: Boolean,
      default: false,
    },
    userInfo: {
      type: Object,
      default: () => ({}),
    },
    isDetailPage: {
      type: Boolean,
      default: false,
    },
    collapsed: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showDownloadDialog: false, // 控制 DownloadDialog 的渲染
      newVersion: true,
    };
  },
  mounted() {
    this.newVersion = window.version == 2;
  },
  methods: {
    handleContact() {
      window.open(toHelpUrl);
    },
    handleDownload() {
      this.showDownloadDialog = true;
      // 使用 $nextTick 确保 DOM 更新后再访问 $refs
      this.$nextTick(() => {
        this.$refs.downloadDialog.handlerVisible(true);
      });
    },
    handleSignOut() {
      this.setConfirm({
        title: this.$t('Me_account_logout_title'),
        msg: this.getTranslateStr('Me_account_logout_content'),
        okname: this.$t('Me_account_logout_button_right'),
        ok: () => {
          this.setGALogEvent('web_logout');
          localStorage.removeItem('tokenstr');
          Cookies.remove('noteemail');
          Cookies.remove('notepwd');
          this.$router.push('/login');
          console.log('handleSignOut');
        },
      });
    },
    handleSettings(key) {
      this.$emit('settings', key);
    },
    handleBack() {
      const currentPath = window.location.pathname;
      // 匹配 /web 后面跟数字的路径，比如 /web8/
      const basePathMatch = currentPath.match(/^\/web\d+\//);
      const basePath = basePathMatch ? basePathMatch[0] : '/';

      // 判断当前是否在 new 相关页面
      if (currentPath.includes('/new')) {
        // 如果当前路径已经是 /new 或 /new/，则不跳转
        if (currentPath === basePath + 'new' || currentPath === basePath + 'new/') {
          return;
        }
        // 否则跳转到 /new/
        this.$router.push(basePath + 'new/');
      } else {
        // 如果当前已经在首页，则不跳转
        if (currentPath === basePath) {
          return;
        }
        this.$router.push(basePath);
      }
    },
  },
  watch: {
    newVersion(newValue, oldValue) {
      if (newValue === false) {
        localStorage.setItem('version', 1);
        window.location.href = '/';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.uNameIcon {
  width: 36px;
  height: 36px;
  background: #a9b5c7;
  border-radius: 50%;
  flex-shrink: 0;
  font-weight: bold;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  &.leftBig {
    width: 40px;
    height: 40px;
    border: 1px solid #d0d5dd;
    box-sizing: border-box;
    padding: 2px;
    background: transparent;
    .noimg {
      background: #a9b5c7;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      width: 100%;
      height: 100%;
    }
  }
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.menu-item {
  @apply px-4 py-2 cursor-pointer hover:bg-gray-100;
}
</style>
