<template>
  <div
    class="file-detail flex-1 bg-[#FFFFFF] border border-[#E5E7EB] rounded-[6px] mr-[12px] mb-[12px]"
    :class="collapsed ? 'ml-[12px]' : ''"
  >
    <Vue3Wrapper
      page-type="filedetail"
      :userInfo="$root.userStateInfo"
      :userBaseInfo="$root.userInfo"
      :data="fileInfo"
      :generateStatus="generateStatus"
      :generateCode="generateCode"
      :onChange="onFileChange"
      v-if="fileInfo.id != ''"
    />
  </div>
</template>

<script>
// 这里可以根据需要引入 props 或路由参数
export default {
  name: 'FileDetail',
  components: {
    Vue3Wrapper: () => import('../common/vue3-wrapper.vue'),
  },
  props: {
    collapsed: {
      type: Boolean,
      default: false,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    generateStatus: {
      type: Number,
      default: 0,
    },
    generateCode: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      fileInfo: {},
    };
  },
  deactivated() {
    this.fileInfo.id = '';
    this.onChange('closedetail');
  },
  activated() {
    this.setFileId();
  },
  methods: {
    setFileId() {
      const fileId = this.$route.params.id;
      this.fileInfo.id = fileId;
    },
    onFileChange(type, data = {}) {
      console.log(type, 'type');
      // console.log('onChange-data-0616:', type, data);
      this.onChange?.(type, data);
    },
  },
};
</script>

<style scoped>
.file-detail {
  padding: 34px 34px 0px 55px;
}
</style>
