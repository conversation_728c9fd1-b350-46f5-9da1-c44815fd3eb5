<template>
  <nav class="mx-3 mt-[10px]">
    <ul>
      <li
        v-for="item in categoryList"
        :key="item.key"
        class="w-[196px] cursor-pointer h-[34px] pt-2 pr-1 py-[6px] pl-3 rounded-md transition-all flex items-center font-normal"
        :class="[
          activeKey === item.key ? 'bg-[rgba(31,35,41,0.08)]' : 'hover:bg-[rgba(31,35,41,0.08)]',
          'transition-all duration-150',
        ]"
        @click="handleCategoryClick(item)"
      >
        <a class="flex items-center text-lg font-medium">
          <SvgIcon :name="item.icon" class="w-[18px] h-[18px] mr-2" color="#000" />
          <span class="text-[14px] font-normal text-[#060606]">{{ item.label }}</span>
        </a>
      </li>
    </ul>
  </nav>
</template>

<script>
import SvgIcon from '../../common/svg-icon/index.vue';
import TabMixins from '@/util/TabMixins';
export default {
  mixins: [TabMixins],
  components: {
    SvgIcon,
  },
  data() {
    return {
      activeKey: '',
    };
  },
  computed: {
    categoryList() {
      return [{ key: 'trash', label: this.$t('side_trash_btn'), icon: 'new-trash' }];
    },
  },
  methods: {
    handleCategoryClick(item) {
      this.activeKey = item.key;
      let prefix = '/';
      if (this.$router.currentRoute.path.includes('/new')) {
        prefix = '/new/';
      }
      // 你的跳转或其他逻辑
      this.$router.push({
        path: `${prefix}fileList?categoryId=${item.key}`,
      });
    },
  },
};
</script>
