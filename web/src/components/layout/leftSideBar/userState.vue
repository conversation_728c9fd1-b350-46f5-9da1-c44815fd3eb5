<template>
  <div class="my-6 mx-5">
    <div class="flex justify-between items-center cursor-pointer" @click="toMemberDetail()">
      <div class="flex items-center">
        <SvgIcon name="member-ai" class="w-[18px] h-[18px] mr-2" v-if="userInfo.membership_type!='starter'"/>
        <span class="name-txt">{{memberName}}</span>
      </div>
      <IconButton name="arrow-left" class="transition-transform"/>
    </div>
    <div class="mb-2" v-if="!isUnlimitedUser">
      <div class="bar-wrap">
        <div class="process" :style="{width: getPerNum(userInfo.seconds_left, userInfo.seconds_total) + '%'}"></div>
      </div>
      <div class="time-txt">
        {{
          getTranslateStr(
            'Me_membercard_subtitle_lefttime_left',
            '20240327',
            getMinNum(userInfo.seconds_left),
          )
        }}
      </div>
    </div>
    <div class="mb-2 mt-2" v-else>
      <div class="time-txt">
        {{
          getTranslateStr(
            'Web_me_membercard_exspire',
            '%s',
            getDateStr(userInfo.expire_time * 1000, 'yyyy-MM-dd'),
          )
        }}
      </div>
    </div>
    <div v-if="userInfo.membership_id_traffic">
      <div class="name-txt">{{ packageInfo.traffic }}</div>
      <div class="bar-wrap">
        <div class="process" :style="{width: getPerNum(userInfo.seconds_left_traffic, userInfo.seconds_total_traffic) + '%'}"></div>
      </div>
      <div class="time-txt">
        {{
          getTranslateStr(
            'Me_membercard_quota_lefttime_left',
            '20240327',
            getMinNum(userInfo.seconds_left_traffic) + ' ',
          )
        }}
      </div>
    </div>
<!--    <div class="upgrade-btn mt-2" v-if="!isUnlimitedUser" @click="toMemberDetail()">Upgrade</div>-->
  </div>
</template>

<script>
import SvgIcon from '@/components/common/svg-icon/index.vue';
import IconButton from '@/components/common/icon-button/index.vue';
export default {
  props: {
    userInfo: {
      type: Object,
      default: {},
    },
  },
  components:{
    SvgIcon,
    IconButton
  },
  data() {
    return {};
  },
  computed: {
    packageInfo() {
      return {
        backer: this.$t('Me_membercard_backer'),
        pro: this.$t('Me_membercard_pro'),
        traffic: this.$t('Me_membercard_quota'),
        free: this.$t('Me_membercard_free'),
        starter: this.$t('Me_membercard_starter'),
        unlimited: this.$t('Me_membercard_unlimited'),
      };
    },
    isUnlimitedUser() {
      return this.userInfo.membership_type == 'unlimited';
    },
    memberName() {
      let { membership_id } = this.userInfo;
      if (membership_id) {
        return this.packageInfo[this.userInfo.membership_type];
      }
      return this.packageInfo.traffic;
    }
  },
  methods: {
    toMemberDetail(){
      this.$emit('feature-click', 'upgrade');
    }
  },
};
</script>
<style scoped>
.name-txt{
  @apply text-[#060606] font-normal text-sm;
}
.time-txt{
@apply text-[#646A73] font-normal text-xs;
}
.bar-wrap{
  @apply h-[3px] rounded-[3px] bg-[#E1E4E7] my-2;
  .process{
    @apply h-[100%] rounded-[3px] bg-[#646A73];
  }
}
.upgrade-btn{
  @apply h-[32px] flex justify-center items-center rounded-[6px] border border-[#E5E7EB] cursor-pointer hover:bg-[#E5E7EB] text-xs font-normal text-[#060606];
}
</style>
