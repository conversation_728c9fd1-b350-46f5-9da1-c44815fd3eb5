<template>
  <nav class="mx-3 mt-[30px]">
    <ul class="space-y-2">
      <li
        v-for="item in menuList"
        :key="item.key"
        class="w-[196px] cursor-pointer h-[34px] pt-2 pr-1 py-[6px] pl-3 rounded-md transition-all flex items-center font-normal"
        :class="[
          activeKey === item.key ? 'bg-[rgba(31,35,41,0.08)]' : 'hover:bg-[rgba(31,35,41,0.08)] ',
          'transition-all duration-150',
        ]"
        @click="handleMenuClick(item.key)"
      >
        <a class="flex items-center w-[177px] text-lg font-medium">
          <SvgIcon :name="item.icon" class="w-[18px] h-[18px] mr-2 text-gray-500 flex-shrink-0" />
          <span class="text-[14px] font-normal text-[#060606] truncate">{{ item.label }}</span>
          <SvgIcon
            v-if="item.memberIcon"
            :name="item.memberIcon"
            class="w-[27px] h-[12px] ml-1 text-gray-500 flex-shrink-0"
            :class="item.key"
          />
        </a>
      </li>
    </ul>
  </nav>
</template>

<script>
import SvgIcon from '../../common/svg-icon/index.vue';
import IconButton from '../../common//icon-button/index.vue';
import TabMixins from '@/util/TabMixins';

export default {
  name: 'menuList', // 组件名称，可根据需要修改
  mixins: [TabMixins],
  components: {
    SvgIcon,
    IconButton,
  },
  data() {
    return {
      activeKey: '',
    };
  },
  computed: {
    menuList() {
      return [
        { key: 'home', label: this.$t('home_btn'), icon: 'homelayout' },
        {
          key: 'communityTemplates',
          label: this.$t('side_templates_btn'),
          icon: 'templates',
          memberIcon: 'badge_beta',
        },
      ];
    },
  },

  methods: {
    handleMenuClick(key) {
      this.activeKey = key;
      let prefix = '/';
      if (this.$router.currentRoute.path.includes('/new')) {
        prefix = '/new/';
      }
      if (key === 'home') {
        this.$router.push({
          path: `${prefix}`,
        });
        return;
      } else {
        this.$router.push({
          path: `${prefix}${key}`,
        });
      }
    },
  },
};
</script>
