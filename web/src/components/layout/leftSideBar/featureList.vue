<template>
  <nav class="mx-3 mt-[10px]">
    <ul class="space-y-2">
      <li
        v-for="item in featureList"
        :key="item.key"
        class="w-[196px] cursor-pointer h-[34px] pt-2 pr-1 py-[6px] pl-3 rounded-md transition-all flex items-center font-normal hover:bg-[rgba(31,35,41,0.08)]"
        @click="handleMenuClick(item.key)"
      >
        <a class="flex items-center text-lg font-medium">
          <SvgIcon :name="item.icon" class="w-[18px] h-[18px] mr-2 text-gray-500" />
          <span class="text-[14px] font-normal text-[#060606]">{{ item.label }}</span>
        </a>
      </li>
    </ul>
  </nav>
</template>

<script>
import SvgIcon from '../../common/svg-icon/index.vue';
import IconButton from '../../common//icon-button/index.vue';

export default {
  name: 'FeatureList', // 组件名称，可根据需要修改
  //   mixins: [TabMixins],
  components: {
    SvgIcon,
    IconButton,
  },
  data() {
    return {
      activeKey: '',
    };
  },
  computed: {
    featureList() {
      return [
        { key: 'glossary', label: this.$t('side_glossary_btn'), icon: 'glossary' },
        { key: 'integration', label: this.$t('integrations_title'), icon: 'integration' },
      ];
    },
  },

  methods: {
    handleMenuClick(key) {
      this.activeKey = key;
      this.$emit('feature-click', key);
    },
  },
};
</script>
