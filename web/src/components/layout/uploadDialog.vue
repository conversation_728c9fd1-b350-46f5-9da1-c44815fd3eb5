<template>
  <div v-if="dialogStatus">
    <ModalDialog
      :title="dialogTitle"
      width="656px"
      confirmName="OK"
      @confirm="confirm"
      @close="close"
      customClass="p-4"
      :class="{ 'opacity-0 pointer-events-none': !showParentWin }"
    >
      <div
        class="importAudioBox mt-[14px]"
        @dragover.prevent="isHover = true"
        @dragleave.prevent="isHover = false"
        @drop.prevent="handleDrop"
        v-show="showParentWin"
      >
        <div
          v-if="!hasUp"
          class="audioDragBox file-btn bg-[#F7F7F7]"
          :class="{ small: hasUp, active: isHover }"
        >
          <template>
            <div class="descFlex" style="margin-top: 68px">
              <SvgIcon name="uploadDialog" class="w-12 h-12" />
              <div class="dragTxt text-[14px] font-normal">
                <!-- <span class="blue">{{ $t('Import_audio_web_click') }}</span> -->
                <!-- {{ $t('Import_audio_web_drag') }} -->
                {{ $t('home_import_audio_title_tip') }}
              </div>
            </div>
            <div class="descTxt">{{ $t('home_list_upload_dialog_tip') }}</div>
          </template>
          <input type="file" class="my-file" multiple :accept="acceptFiles" @change="uploadFile" />
        </div>
        <vue-perfect-scrollbar v-else class="upFileListBox max-h-[296px] overflow-y-auto">
          <div class="fileItem" v-for="(fileInfo, index) in upFileList" :key="index">
            <!-- <div class="leftIcon iconfont icon-icon_file_type_audio"></div> -->
            <SvgIcon name="voice" class="w-12 h-12 mr-3" />
            <div class="rightFile">
              <div class="nameBox">
                <div class="name">{{ fileInfo.filename }}</div>
                <!-- <div
                  class="remove iconfont icon-icon_delete_upload_file"
                  @click="removeFile(index, fileInfo)"
                ></div> -->
              </div>
              <div class="info mt-1">
                <span v-if="fileInfo.status === 0" class="text-[#646A73] text-[12px]">{{
                  $t('Import_audio_web_status1')
                }}</span
                ><!-- 等待上传 -->
                <span v-if="fileInfo.status === 1" class="text-[#646A73] text-[12px]"
                  >{{ $t('Import_audio_web_status2') }}{{ fileInfo.percent }}%</span
                ><!-- 上传中 -->
                <template v-else-if="fileInfo.status === 2">
                  <SvgIcon name="success" class="w-4 h-4 mr-1" />
                  <span class="text-[#646A73] text-[12px]"
                    >{{ getSizeStr(fileInfo.msize) }}
                  </span> </template
                ><!-- 上传完成 -->
                <span v-else-if="fileInfo.status === 3" class="text-[#646A73] text-[12px]">{{
                  $t('Import_audio_web_status4')
                }}</span
                ><!-- 等待转码 -->
                <span v-else-if="fileInfo.status === 4" class="text-[#646A73] text-[12px]"
                  >{{ $t('Import_audio_web_status5') }}{{ fileInfo.percent }}%</span
                ><!-- 转码中 -->
                <template v-else-if="fileInfo.status === -1">
                  <SvgIcon name="warn" class="w-4 h-4 mr-1" />
                  <span class="text-[#FF5449] text-[12px]">{{
                    $t('Import_audio_web_status6')
                  }}</span> </template
                ><!-- 大于5h -->
                <template v-else-if="fileInfo.status === -2">
                  <SvgIcon name="warn" class="w-4 h-4 mr-1" />
                  <span
                    class="text-[#FF5449] text-[12px] cursor-pointer"
                    @click="setExistFileInfo(fileInfo, index)"
                    >{{ $t('Import_audio_web_status7') }}</span
                  >
                  <SvgIcon name="existRed" class="w-4 h-4 mr-1 cursor-pointer" />
                </template>
                <!-- 文件存在 -->
                <template v-else-if="fileInfo.status === -3">
                  <span class="error hand">{{ $t('Import_audio_web_status8') }}</span>
                  <div class="iconBox">
                    <span class="iconfont icon-a-Info2"></span>
                  </div>
                </template>
                <!-- 上传失败 -->
                <template v-else-if="fileInfo.status === -4">
                  <SvgIcon name="warn" class="w-4 h-4 mr-1" />
                  <span class="text-[#FF5449] text-[12px]">{{
                    getTranslateStr('Import_audio_web_status9', '%s', limitSize + 'M')
                  }}</span></template
                ><!-- 文件超过500M -->
                <template v-else-if="fileInfo.status === -5">
                  <SvgIcon name="warn" class="w-4 h-4 mr-1" />
                  <span class="text-[#FF5449] text-[12px]"
                    >$t('Import_audio_web_status10')
                  </span></template
                ><!-- 转码失败 -->
                <template v-else-if="fileInfo.status === -6">
                  <SvgIcon name="warn" class="w-4 h-4 mr-1" />
                  <span class="text-[#FF5449] text-[12px]"
                    >{{ $t('Import_audio_web_status11') }}
                  </span></template
                ><!-- 格式不支持 -->
                <span v-else-if="fileInfo.status === -7">
                  <SvgIcon name="warn" class="w-4 h-4 mr-1" />
                  <span class="text-[#FF5449] text-[12px]">{{
                    $t('Import_audio_web_status12')
                  }}</span>
                </span>
                <!-- 小于1S -->
                <template v-else-if="fileInfo.status === -8 || fileInfo.status === -9">
                  <SvgIcon name="warn" class="w-4 h-4 mr-1" />
                  <span class="text-[#FF5449] text-[12px]">{{
                    $t('Import_audio_web_status13')
                  }}</span>
                  <!-- <div class="retryBtn" @click="doRetry(fileInfo)">
                    {{ $t('Import_audio_web_status13_button') }}
                  </div> -->
                  <!-- 网络错误 -->
                </template>
              </div>
              <div
                class="w-[292px] bg-gray-100 rounded-full h-[2px] mt-1"
                v-if="fileInfo.status === 1"
              >
                <div
                  class="h-[2px] rounded-full transition-all duration-300 bg-black"
                  :style="{ width: fileInfo.percent + '%' }"
                ></div>
              </div>
            </div>
            <div class="flex items-center justify-start w-[92px] ml-[63px]">
              <div
                v-if="fileInfo.status === -8 || fileInfo.status === -9"
                class="text-[14px] text-[#000] cursor-pointer"
                @click="doRetry(fileInfo)"
              >
                {{ $t('home_import_audio_try_again') }}
              </div>
              <div
                v-else
                class="text-[14px] text-[#000] cursor-pointer"
                @click="removeFile(index, fileInfo)"
              >
                {{ $t('cancel_btn') }}
              </div>
            </div>
          </div>
        </vue-perfect-scrollbar>

        <div v-if="hasUp" class="flex justify-center mt-8">
          <!-- 新增：隐藏的input -->
          <input
            ref="hiddenFileInput"
            class="my-file"
            type="file"
            multiple
            :accept="acceptFiles"
            @change="uploadFile"
            style="display: none"
          />
          <button
            class="flex items-center justify-center w-[180px] h-12 bg-black text-white rounded-[8px] text-[14px] font-normal hover:bg-[#323135] transition"
            @click="handleImport"
          >
            <SvgIcon name="uploadWhite" class="w-5 h-5 mr-2" />
            {{ $t('import_btn') }}
          </button>
        </div>
      </div>
    </ModalDialog>
    <!--   不是会员   -->
    <NoMember ref="NoMember" />
  </div>
</template>

<script>
import { convertWithFFmpegWasm, abort, loadFFmpegWasm, errorMsgRegex } from '@/util/convert';
import { v4 as uuidv4 } from 'uuid';
import MyDialog from '../../components/common/MyDialog.vue';
import NoMember from '../page/common/membertip/NoMember.vue';
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
import 'vue3-perfect-scrollbar/style.css';
import SvgIcon from '../common/svg-icon/index.vue';
export default {
  name: 'UploadDialog',
  components: {
    ModalDialog: () => import('./common/ModalDialog.vue'),
    MyDialog,
    PerfectScrollbar,
    NoMember,
    SvgIcon,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dialogStatus: false,
      isHover: false,
      upFileList: [],
      authUser: false,
      dialogPopStatus: false,
      dialogClose: false, //关闭上传弹框
      ajaxCancel: null,
      limitSize: 500,
      acceptFiles: 'audio/*,video/*,.rmvb,.rm,.divx,.ts,.m2ts,.3gp,.f4v,.asr',
      finshedFileList: [],
    };
  },
  computed: {
    hasUp() {
      return this.upFileList.length > 0;
    },
    showParentWin() {
      return !this.dialogClose && !this.dialogPopStatus;
    },
    dialogTitle() {
      return this.upFileList.length > 0
        ? this.title + ' ( ' + this.upFileList.length + ' ) '
        : this.title;
    },
  },
  mounted() {
    this.reqGetInfo('/others/web-config').then((data) => {
      if (data.upload_limit_size) {
        this.limitSize = data.upload_limit_size;
      }
    });
    //等待上传、正在上传、转码中、正在转码这四种情况会出现拦截
    window.onbeforeunload = (e) => {
      if (this.upFileList.some((item) => item.status >= 0 && item.status != 2)) {
        return '';
      }
    };
  },
  beforeDestroy() {
    this.doDiscard(false);
  },
  methods: {
    handlerVisible(status) {
      this.dialogStatus = status;
      if (!status) {
        this.dialogClose = false;
        this.dialogPopStatus = false;
        this.isHover = false;
        this.upFileList = [];
        this.finshedFileList = [];
        this.ajaxCancel = null;
      }
      if (status) {
        let memberType = this.$root.userStateInfo.membership_type;
        this.authUser = ['backer', 'pro', 'starter', 'unlimited'].includes(memberType);
        if (!this.authUser) {
          this.$nextTick(() => {
            this.$refs.NoMember.handlerVisible(true);
          });
        } else {
          loadFFmpegWasm();
        }
      }
    },
    handleImport() {
      this.$el.querySelector('input[type="file"]').click();
    },
    uploadFile(e) {
      let files = e.target.files;
      this.setUploadFiles(files);
      e.target.value = '';
    },
    handleDrop(event) {
      this.isHover = false;
      const files = event.dataTransfer.files;
      this.setUploadFiles(files);
    },
    async setUploadFiles(files) {
      let list = [],
        totalSize = this.upFileList.reduce((total, next) => total + next.size, 0),
        maxList = [],
        createTime = new Date().getTime();
      for (let i = 0; i < files.length; i++) {
        let file = files[i],
          fileInfo = { status: 3, percent: 0 },
          filename = file.name; //-1超过5h，-2文件名存在,-3上传失败,-4超过500M，-5转码失败,-6格式不支持,-7小于1S,-8上传网络错误,-9转码网络错误,0等待上传，1正在上传 ，2上传成功,3等待转码，4转码中
        let index = filename.lastIndexOf('.'),
          filefix = filename.substring(index + 1).toLowerCase();

        //限制为音频或者视频格式文件
        if (
          file.type.includes('audio') ||
          file.type.includes('video') ||
          this.acceptFiles.includes(filefix)
        ) {
          fileInfo.filename = filename.substring(0, index);
          fileInfo.size = file.size;
          fileInfo.msize = file.size;
          fileInfo.blob = file;
          fileInfo.createTime = file.lastModified;
          fileInfo.filefix = filefix;
          if (fileInfo.filefix == 'asr') {
            // asr直接上传
            fileInfo.status = 0;
            let duration = (fileInfo.size / (80 * 1)) * 20;
            if (duration > 60 * 60 * 5 * 1000) {
              // 大于5小时
              fileInfo.status = -1;
            }
            fileInfo.duration = duration;
          }
          list.push(fileInfo);
        }
      }
      //将文件从小到大排序
      list.sort((a, b) => a.size - b.size);

      //单文件5小时，总共是500M
      list.forEach((fileInfo) => {
        totalSize += fileInfo.size;
        if (totalSize > 1024 * 1024 * this.limitSize) {
          //总共不能超过500M
          maxList.push(fileInfo.filename);
        } else {
          if (fileInfo.size > 1024 * 1024 * this.limitSize) {
            //理论上，貌似不会进入这个逻辑
            fileInfo.status = -4;
          }
          if (
            fileInfo.status > -1 &&
            (this.upFileList.some((item) => item.filename === fileInfo.filename) ||
              this.$parent.infoAllList.some(
                (item) => item.is_trash === false && item.filename === fileInfo.filename,
              ))
          ) {
            fileInfo.status = -2;
          }
        }
      });
      if (maxList.length > 0) {
        list.splice(list.length - maxList.length, maxList.length);
        this.setMaxFileInfo(maxList);
      }
      this.upFileList.push(...list);
      this.doNextFiles();
      this.doNextTransFiles();
    },
    async toUploadFile(fileInfo) {
      fileInfo.status = 1;

      let { upload_id, object_name, part_urls } = await this.reqPostInfo(
        '/file/get_upload_presigned_url',
        { filesize: fileInfo.size, file_type: fileInfo.filefix == 'asr' ? 'OPUS' : 'MP3' },
      );
      Object.assign(fileInfo, {
        upload_id,
        object_name,
        parturls: part_urls.map((url, index) => ({ url, index, status: 0, percent: 0 })),
      }); //status:0未上传，1上传中，2上传成功，3失败

      let CHUNK_SIZE = 1024 * 1024 * 5, //每段5M
        UPLOAD_SIZE = 3, //3段并发
        totalChunk = Math.ceil(fileInfo.size / CHUNK_SIZE);
      UPLOAD_SIZE = Math.min(UPLOAD_SIZE, totalChunk);
      let uploadChunk = async () => {
        if (fileInfo.parturls.filter((item) => item.status === 1).length < UPLOAD_SIZE) {
          //小于3个并发
          let partInfo = fileInfo.parturls.find((item) => item.status === 0 || item.status === 3);
          if (partInfo) {
            //进行分片上传
            let start = partInfo.index * CHUNK_SIZE;
            let end = Math.min(fileInfo.size, start + CHUNK_SIZE);
            partInfo.status = 1;
            let backInfo = this.reqUploadS3FilePostInfo(
              partInfo.url,
              fileInfo.blob.slice(start, end),
              (percent) => {
                partInfo.percent = (percent >= 100 ? 99 : percent) / totalChunk;
                fileInfo.percent = Math.floor(fileInfo.parturls.reduce((a, b) => a + b.percent, 0));
              },
            );
            partInfo.ajaxCancel = backInfo.cancelLoad;
            backInfo.ps.then(
              (response) => {
                let etag = response.headers.etag;
                etag = etag.replace(/\"/g, '');
                partInfo.Etag = etag;
                partInfo.status = 2;
                uploadChunk();
              },
              () => {
                partInfo.status = 3;
                uploadChunk();
              },
            );

            // fetch(partInfo.url, {
            //   method: 'PUT',
            //   body: fileInfo.blob.slice(start, end),
            // }).then(
            //   (response) => {
            //     let etag = response.headers.get('ETag');
            //     etag = etag.replace(/\"/g, '');
            //     partInfo.Etag = etag;
            //     partInfo.percent = 99 / totalChunk;
            //     fileInfo.percent = fileInfo.parturls.reduce((a, b) => a + b.percent, 0);
            //     partInfo.status = 2;
            //     uploadChunk();
            //   },
            //   () => {
            //     partInfo.status = 3;
            //     uploadChunk();
            //   },
            // );
          }
        }
        if (fileInfo.parturls.every((item) => item.status === 2)) {
          //全部分片上传完毕，进行文件合并
          let partsList = fileInfo.parturls.map((item) => ({
            Etag: item.Etag,
            PartNumber: item.index + 1,
          }));
          try {
            let mergeInfo = await this.reqPostInfo('/file/merge_multipart', {
              upload_id,
              object_name,
              parts: partsList,
            });
            if (mergeInfo) {
              //support_mul_summ设置为true，支持多总结，用于workflow场景
              let fileParams = {
                upload_id,
                object_name,
                scene: 101,
                is_tmp: 0,
                support_mul_summ: true,
              };
              if (fileInfo.filefix == 'asr') {
                //asr文件后台不获取duration
                fileParams.duration = parseInt(fileInfo.duration);
                fileParams.file_type = 'OPUS';
              } else {
                fileParams.file_type = 'MP3';
              }
              fileParams.filename = fileInfo.filename;
              fileParams.start_time = fileInfo.createTime;
              fileParams.session_id = Math.floor(fileInfo.createTime / 1000);
              fileParams.serial_number = uuidv4();
              //合并完成后，调用接口复制文件到目标目录以及业务入表
              this.reqPostInfo('/file/confirm_upload', fileParams).then(
                (data) => {
                  this.$emit('upload-file', Object.assign(data, { oldsize: fileInfo.size }));
                  fileInfo.status = 2;
                  delete fileInfo.blob;
                  fileInfo.size = 0;
                  fileInfo.percent = 100;
                  fileInfo.parturls = [];
                  this.doNextFiles();
                },
                (data) => {
                  if (!data) {
                    //网络错误
                    fileInfo.status = -8;
                  } else {
                    fileInfo.status = -3;
                    delete fileInfo.blob;
                    fileInfo.size = 0;
                  }
                  fileInfo.parturls = [];
                  this.doNextFiles();
                },
              );
            }
          } catch (e) {
            fileInfo.status = -3;
          }
        }
      };
      for (let i = 0; i < UPLOAD_SIZE; i++) {
        uploadChunk();
      }
    },
    doUploadFiles(fileInfo) {
      let formData = new FormData();
      formData.append(
        'file',
        fileInfo.blob,
        fileInfo.filename + (fileInfo.filefix == 'asr' ? '.opus' : '.mp3'),
      );
      if (fileInfo.filefix == 'asr') {
        //asr文件后台不获取duration
        formData.append('duration', parseInt(fileInfo.duration));
      }
      formData.append('filename', fileInfo.filename);
      formData.append('scene', 101);
      formData.append('start_time', fileInfo.createTime);
      //以下这两个参数app展示文件列表需要
      formData.append('session_id', Math.floor(fileInfo.createTime / 1000));
      formData.append('serial_number', uuidv4());

      fileInfo.status = 1;
      let backInfo = this.reqUploadFilePostInfo('/file/upload', formData, (percent) => {
        fileInfo.percent = percent >= 100 ? 99 : percent;
      });
      this.ajaxCancel = backInfo.cancelLoad;
      backInfo.ps.then(
        (data) => {
          this.$parent.selectLeftMenu('', 0, [
            Object.assign(data.data_file, { oldsize: fileInfo.size }),
          ]);
          fileInfo.status = 2;
          delete fileInfo.blob;
          fileInfo.size = 0;
          fileInfo.percent = 100;
          this.ajaxCancel = null;
          this.doNextFiles();
        },
        (data) => {
          if (!data) {
            //网络错误
            fileInfo.status = -8;
          } else {
            fileInfo.status = -3;
            delete fileInfo.blob;
            fileInfo.size = 0;
          }
          this.ajaxCancel = null;
          this.doNextFiles();
        },
      );
    },
    doNextFiles() {
      if (!this.upFileList.some((item) => item.status === 1)) {
        let idx = this.upFileList.findIndex((item) => item.status === 0);
        if (idx > -1) {
          // this.doUploadFiles(this.upFileList[idx]);
          this.toUploadFile(this.upFileList[idx]);
        }
      }
    },
    async doTransFiles(fileInfo) {
      fileInfo.status = 4;
      const arrayBuffer = await fileInfo.blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const blobInfo = await convertWithFFmpegWasm(
        uint8Array,
        fileInfo.blob.name,
        ({ type, data }) => {
          console.log(type, data);
          if (type === 'duration' && data != null) {
            fileInfo.duration = Math.floor(data);
            if (fileInfo.duration < 1) {
              //小于1S
              fileInfo.status = -7;
            } else if (fileInfo.duration > 60 * 60 * 5) {
              // 大于5小时
              fileInfo.status = -1;
            }
          } else if (type === 'progress') {
            fileInfo.percent = parseInt(Math.floor(data * 100));
          } else if (type === 'error') {
            if (data?.includes('fetch') || data?.includes('network')) {
              fileInfo.status = -9;
            } else if (data?.match(errorMsgRegex.mp3floatRegex)) {
              // 继续执行，不改变状态
            } else {
              fileInfo.status = -6;
            }
          }
        },
      );
      if (fileInfo.status > 0) {
        fileInfo.status = 0;
        fileInfo.percent = 0;
        fileInfo.blob = blobInfo;
        //上传分片根据size,这里需要重新取转码后的size
        fileInfo.size = blobInfo?.size || 0;
      } else if (fileInfo.status != -9) {
        delete fileInfo.blob;
        fileInfo.size = 0;
      }
      this.doNextFiles();
      this.doNextTransFiles();
    },
    doNextTransFiles() {
      if (!this.upFileList.some((item) => item.status === 4)) {
        let idx = this.upFileList.findIndex((item) => item.status === 3);
        if (idx > -1) {
          this.doTransFiles(this.upFileList[idx]);
        }
      }
    },
    uploadFinshed() {
      // if (this.finshedFileList.length > 0) {
      //   //为了保持插入的文件列表的顺序和上传列表的顺序保持一致
      //   this.finshedFileList.sort((a, b) => a.oldsize - b.oldsize);
      //   this.$parent.selectLeftMenu('', 0, this.finshedFileList);
      // }
      // this.handlerVisible(false);
    },
    setPopWindow(options) {
      let initOption = {
        width: '326px',
        msgStyle: { 'padding-left': '0px', 'padding-right': '0px' },
        redok: false,
        btnBoxStyle: { 'padding-bottom': '0px' },
        hasClose: false,
        cancel: () => {
          this.dialogPopStatus = false;
        },
      };
      this.setConfirm(Object.assign({}, initOption, options));
      this.dialogPopStatus = true;
    },
    setExistFileInfo(file, index) {
      this.setConfirm({
        title: this.$t('Import_audio_web_exist_title'),
        msg: this.getTranslateStr('Import_audio_web_exist_content'),
        okname: this.$t('Import_dialog_file_exists_continue'),
        cancelbtnStyle: {
          color: '#FF5449',
          background: '#fff',
          border: '1px solid #FF5449',
        },
        okbtnStyle: {
          color: 'white',
          background: '#060606',
        },
        ok: () => {
          file.filename = file.filename + this.getDateStr(new Date().getTime(), '-MM-dd HH:mm:ss');
          if (file.filefix == 'asr') {
            file.status = 0;
          } else {
            file.status = 3;
          }
          this.dialogPopStatus = false;
          this.doNextFiles();
          this.doNextTransFiles();
        },
        cancel: () => {
          this.dialogPopStatus = false;
          this.upFileList.splice(index, 1);
        },
      });
    },
    setMaxFileInfo(names) {
      let nameList = ['<ul class="tipFilesList">'];
      names.forEach((name) => {
        nameList.push(`<li>${name}</li>`);
      });
      nameList.push('</ul>');
      //文件超大500M
      this.setPopWindow({
        title: this.$t('Import_audio_web_limitsize_title'),
        msg:
          this.getTranslateStr('Import_audio_web_limitsize_content', '%s', this.limitSize + 'M') +
          nameList.join(''),
        backCancel: true,
        cancelname: this.$t('Import_audio_web_limitsize_btn'),
      });
    },
    setFileImportFail() {
      //文件上传失败
      this.setPopWindow({
        title: this.$t('Import_audio_web_failImport_title'),
        msg: this.getTranslateStr('Import_audio_web_failImport_content'),
        backCancel: true,
        cancelname: this.$t('Import_audio_web_failImport_btn'),
      });
    },
    doDiscard(reload = false) {
      if (this.ajaxCancel) {
        this.ajaxCancel();
        this.ajaxCancel = null;
      }
      this.upFileList.forEach((fileInfo) => {
        if (fileInfo.parturls) {
          fileInfo.parturls.forEach((partInfo) => {
            if (partInfo.ajaxCancel) {
              partInfo.ajaxCancel();
              partInfo.ajaxCancel = null;
            }
          });
        }
      });
      abort();
      if (reload) {
        // this.uploadFinshed();
        this.handlerVisible(false);
      }
    },
    removeFile(index, fileInfo) {
      this.upFileList.splice(index, 1);
      switch (fileInfo.status) {
        case 1: //上传中
          if (this.ajaxCancel) {
            this.ajaxCancel();
          }
          if (fileInfo.parturls) {
            fileInfo.parturls.forEach((partInfo) => {
              if (partInfo.ajaxCancel) {
                partInfo.ajaxCancel();
                partInfo.ajaxCancel = null;
              }
            });
          }
          this.doNextFiles();
          break;
        case 4:
          abort();
          this.doNextTransFiles();
          break;
      }
    },
    doRetry(fileInfo) {
      switch (fileInfo.status) {
        case -8: //上传
          fileInfo.status = 0;
          this.doNextFiles();
          break;
        case -9: //转码
          fileInfo.status = 3;
          this.doNextTransFiles();
          break;
      }
    },
    confirm() {
      this.$emit('confirm');
    },
    close() {
      console.log('close');
      //   this.$emit('close');
      if (this.upFileList.some((item) => item.status !== 2 && item.status > -1)) {
        this.dialogClose = true;
        this.setConfirm({
          title: this.$t(
            this.upFileList.length > 1
              ? 'Import_audio_web_close_title'
              : 'Import_audio_web_close_title_single',
          ),
          msg: this.getTranslateStr(
            this.upFileList.length > 1
              ? 'Import_audio_web_close_content'
              : 'Import_audio_web_close_content_single',
          ),
          okname: this.$t('Import_audio_web_close_discard'),
          cancelname: this.$t('Import_audio_web_close_continue'),
          cancelbtnStyle: {
            color: '#060606',
            background: '#fff',
            border: '1px solid #D5D6D7',
          },
          okbtnStyle: {
            color: 'white',
            background: '#FF5449',
          },
          ok: () => {
            this.doDiscard(true);
          },
          cancel: () => {
            this.dialogClose = false;
          },
        });
      } else {
        // this.uploadFinshed();
        this.handlerVisible(false);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.importAudioBox {
  .audioDragBox {
    height: 322px;
    border-radius: 10px;
    border: 1px dashed #d0d5dd;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    // padding: 24px 0px 16px 0px;
    padding: 24px 29px 16px;
    position: relative;
    overflow: hidden;
    &.active {
      border-color: #007aff;
    }
    &.small {
      height: 96px;
      padding: 0px 16px;
      align-items: center;
      flex-direction: row;
      justify-content: flex-start;
      margin-bottom: 20px;
      .uploadIcon {
        margin-bottom: 0px;
        margin-right: 12px;
      }
      .descFlex {
        justify-content: flex-start;
        align-items: flex-start;
      }
      .dragTxt {
        margin-bottom: 2px;
      }
      .descTxt {
        text-align: left;
        // word-break: keep-all;
        line-height: 1.3;
      }
    }
    .uploadIcon {
      width: 64px;
      height: 64px;
      border-radius: 16px;
      border: 2px solid #d0d5dd;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #858c9b;
      margin-bottom: 24px;
      box-sizing: border-box;
      .iconfont {
        font-size: 60px;
      }
    }
    .descFlex {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
    }
    .dragTxt {
      font-weight: 400 !important;
      font-size: 14px;
      margin-bottom: 10px;
      color: #1f1f1f;
      .blue {
        color: #007aff;
      }
    }
    .descTxt {
      font-weight: 400;
      font-size: 14px;
      color: #8f959e !important;
      text-align: center;
      line-height: 20px;
    }
  }
  .upFileListBox {
    max-height: 296px;
    overflow-y: auto;
    .fileItem:last-child {
      margin-bottom: 0px;
    }
    .fileItem {
      margin-bottom: 20px;
      height: 48px;
      background: #fff;
      border-radius: 10px;
      box-sizing: border-box;
      padding: 0;
      display: flex;
      align-items: center;
      .leftIcon {
        margin-right: 10px;
        color: #858c9b;
        font-size: 48px;
      }
      &:hover {
        .rightFile .nameBox .remove {
          display: block;
        }
      }
      .rightFile {
        flex: 1;
        width: 0;
        .nameBox {
          width: 100%;
          display: flex;
          align-items: center;
          line-height: 20px;
          margin-bottom: 0px;
          .name {
            font-weight: 400;
            font-size: 14px !important;
            color: #1f1f1f;
            flex: 1;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .remove {
            color: #858c9b;
            display: none;
            font-size: 18px;
            margin-left: 10px;
            cursor: pointer;
          }
        }
        .info {
          font-weight: 400;
          font-size: 16px;
          color: #98a2b3;
          display: flex;
          align-items: center;
          & > span {
            margin-right: 3px;
            &.error {
              color: #f14349;
            }
            &.success {
              color: #46cf6c;
            }
          }
          .iconBox {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
          }
          .retryBtn {
            background-color: #475467;
            color: #fff;
            font-size: 14px;
            border-radius: 20px;
            line-height: 16px;
            font-weight: 500;
            padding: 4px 12px;
            letter-spacing: -0.4px;
            cursor: pointer;
            margin-left: 5px;
          }
        }
        /* .fileBar and .process styles are no longer needed and will be removed */
      }
    }
  }
}
.my-file {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}
.uploadFileTipWin {
  .uploadTip {
    font-size: 14px;
    color: #858c9b;
    line-height: 20px;
    text-align: center;
    margin-bottom: 24px;
    word-break: keep-all;
  }
  .commonBtn {
    margin-top: 12px;
    &:first-child {
      margin-top: 0px;
    }
  }
  .btnCrmBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .commonBtn {
      flex: 1;
      margin-top: 0px;
      &:first-child {
        margin-right: 24px;
      }
    }
  }
}
// ../images/upload_nomember.png
.uploadFileTipTitle {
  height: 80px;
  background: #1c1c1c url('../../images/upload_nomember.png') no-repeat right;
  background-size: contain;
  border-top-left-radius: 13px;
  border-top-right-radius: 13px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0px 20px;
  box-sizing: border-box;
  margin: 0px -12px;
  margin-bottom: 24px;
  .t1 {
    font-weight: 500;
    font-size: 16px;
    color: #f5e7d8;
    margin-bottom: 5px;
  }
  .t2 {
    font-weight: 400;
    font-size: 14px;
    color: #858c9b;
  }
}
.tipWeight {
  color: #1f1f1f;
}
.commonBtnLoading {
  opacity: 0.6;
  .text {
    position: relative;
    .el-icon-loading {
      position: absolute;
      // top: 0.5px;
      left: -20px;
      top: 50%;
      margin-top: -10px;
    }
  }
  &:hover {
    background-color: rgb(31, 31, 31);
    opacity: 0.6 !important;
  }
}
.tipFilesList {
  background-color: #f2f4f7;
  border-radius: 8px;
  padding: 8px;
  margin-top: 10px;
  max-height: 165px;
  overflow-y: auto;
  li:last-child {
    margin-bottom: 0px;
  }
  li {
    padding-left: 10px;
    margin-bottom: 2px;
    color: #1f1f1f;
    font-size: 14px;
    list-style-type: disc;
    list-style-position: inside;
    text-align: left;
    height: 26px;
    line-height: 26px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
