<template>
  <div
    class="center-content flex-1 bg-[#FFFFFF] border border-[#E5E7EB] rounded-[6px] mr-[12px] mb-[12px]"
  >
    <div
      class="pl-[168px] pr-[176px] mt-[66px] flex flex-col mx-auto overflow-y-hidden"
      :style="{ height: 'calc(100vh - 124px)' }"
    >
      <!-- 顶部操作区 -->
      <FileListHeader
        :selectIds="selectIds"
        :currentFolderName="currentFolderName"
        :greetingText="greetingText"
        @merge="handleMerge"
        @move="handleMove"
        @moveToTrash="handleMoveToTrash"
        @import="handleImport"
        @record="handleRecord"
        @create-new-folder="handleCreateNewFolder"
        @restore="handleRestore"
        @delete="handleDelete"
        :folders="folders"
      />

      <!-- 文件列表表头 -->
      <div
        class="header-border relative flex items-center justify-between ml-[12px] mr-[20px] mt-2 pb-2 text-[#646A73] text-sm"
        @mouseenter="isAllHover = true"
        @mouseleave="isAllHover = false"
      >
        <div
          class="flex mr-[6px] items-center justify-center rounded cursor-pointer transition-opacity"
          :class="{
            'opacity-100': (isAllHover || selectIds.length) && infoList.length > 0,
            'opacity-0': !((isAllHover || selectIds.length) && infoList.length > 0),
          }"
          @click="toggleSelectAll"
        >
          <svgIcon
            v-if="selectIds.length === infoList.length"
            name="checkfile"
            class="w-4 h-4 text-blue-500"
          />
          <svgIcon
            v-else-if="selectIds.length > 0"
            name="checkFileAll"
            class="w-4 h-4 text-blue-500"
          />
          <svgIcon v-else name="check" class="w-4 h-4 text-blue-500" />
        </div>
        <div class="flex-1 flex items-center relative">
          <el-dropdown @command="handleFilterCommand" placement="bottom-end" :hide-on-click="false">
            <span
              class="flex items-center cursor-pointer select-none"
              :class="{
                'text-[#2E72E3]': activeFilters.source || activeFilters.category,
              }"
            >
              {{ filterDisplayText }}
              <IconButton name="down" class="w-4 h-4 ml-1" />
            </span>

            <el-dropdown-menu slot="dropdown" class="w-[248px] rounded-[6px]">
              <!-- All file -->
              <el-dropdown-item :command="{ type: 'reset' }" class="file-filter-item clear-item">
                <span
                  class="truncate text-right w-full"
                  :class="{
                    'text-[#2E72E3]': !activeFilters.source && !activeFilters.category,
                  }"
                  >Clear</span
                >
              </el-dropdown-item>

              <div class="my-1 mx-3 border-t border-gray-100"></div>
              <!-- Divider -->

              <!-- Comes from -->
              <div class="dropdown-header truncate">{{ $t('home_list_source_from_title') }}</div>
              <el-dropdown-item
                v-for="item in sourceFilters"
                :key="item.key"
                :command="{ type: 'source', key: item.key }"
                class="file-filter-item"
              >
                <span
                  class="pl-[15px] truncate flex-1"
                  :class="{ 'text-[#2E72E3]': activeFilters.source === item.key }"
                  >{{ item.label }}</span
                >
                <svgIcon
                  name="checkBlue"
                  class="w-4 h-4 flex-shrink-0"
                  v-if="activeFilters.source === item.key"
                />
              </el-dropdown-item>
              <template v-if="!tagId">
                <div class="my-1 mx-3 border-t border-gray-100"></div>
                <!-- Divider -->

                <!-- Category -->
                <div class="dropdown-header truncate">
                  {{ $t('home_list_source_category_title') }}
                </div>
                <el-dropdown-item
                  v-for="item in categoryFilters"
                  :key="item.key"
                  :command="{ type: 'category', key: item.key }"
                  class="file-filter-item"
                >
                  <span
                    class="pl-[15px] truncate flex-1"
                    :class="{ 'text-[#2E72E3]': activeFilters.category === item.key }"
                    >{{ item.label }}</span
                  >
                  <svgIcon
                    name="checkBlue"
                    class="w-4 h-4 flex-shrink-0"
                    v-if="activeFilters.category === item.key"
                  /> </el-dropdown-item
              ></template>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="flex items-center">
          <el-dropdown @command="handleSortCommand" placement="bottom-end" trigger="click">
            <span class="flex items-center cursor-pointer select-none">
              {{ sortOptions.find((opt) => opt.key === currentSort).label }}
              <IconButton name="down" class="w-4 h-4 ml-1" />
            </span>
            <el-dropdown-menu slot="dropdown" class="w-[180px] rounded-[6px]">
              <el-dropdown-item
                v-for="item in sortOptions"
                :key="item.key"
                :command="item.key"
                class="flex items-center px-4 h-10 cursor-pointer hover:bg-[#F7F7F7] relative text-[#000]"
                :class="{ 'bg-[#F3F3F3]': currentSort === item.key }"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <!-- 文件列表 -->
      <!-- 骨架图：当 isLoading 为 true 时显示 -->
      <div v-if="isLoading" class="fileList custom-scrollbar mt-2">
        <el-skeleton :rows="5" :animated="true" :count="6">
          <template #template>
            <li
              class="flex items-center pr-6 pl-[36px] py-[10px] bg-white rounded-[6px] transition relative"
            >
              <!-- 文件图标占位 -->
              <el-skeleton-item variant="image" class="w-12 h-12 mr-3 rounded-[6px]" />
              <!-- 文件名占位 -->
              <div class="flex-1 ml-3">
                <el-skeleton-item variant="text" class="w-3/4 h-5" />
              </div>
              <!-- 修改时间占位 -->
              <el-skeleton-item variant="text" class="w-40 h-5 text-center" />
            </li>
          </template>
        </el-skeleton>
      </div>

      <!-- 文件列表 -->
      <RecycleScroller
        v-else-if="infoList.length > 0"
        ref="fileBox"
        class="fileList custom-scrollbar flex-1 mb-1 min-h-0 pt-2"
        :items="infoList"
        :item-size="72"
        key-field="id"
        :prerender="30"
        v-slot="{ item: file }"
      >
        <li
          :ref="`file-card-${file.id}`"
          :key="file.id"
          class="group flex items-center pr-6 pl-[36px] py-[10px] rounded-[6px] transition relative cursor-pointer"
          :class="{ 'bg-[#F7F7F7]': isFileActive(file) }"
          draggable="true"
          :data-file-id="file.id"
          @mouseenter="changeHovered(file, true)"
          @mouseleave="changeHovered(file, false)"
          @click.stop="handleFileClick(file)"
        >
          <!-- 选择按钮 -->
          <div
            v-if="isFileActive(file)"
            class="absolute left-[14px] flex items-center justify-center w-4 h-4 rounded cursor-pointer transition"
            @click.stop="(e) => toggleSelect(e, file.id)"
          >
            <!-- {{ selectIds.includes(file.id) }} -->
            <!-- selectIds.includes(info.id) -->
            <svgIcon
              v-if="selectIds.includes(file.id)"
              name="checkfile"
              class="w-4 h-4 text-blue-500"
            />
            <svgIcon v-else name="check" class="w-4 h-4" />
          </div>
          <!-- 文件icon -->
          <svg-icon :name="file.is_trans ? 'doc' : 'voice'" class="w-12 h-12 text-gray-400" />
          <!-- 文件名 -->
          <div class="flex-1 ml-3 flex flex-col text-[14px] min-w-0">
            <div
              class="font-medium truncate"
              :class="file.isVirtual ? 'text-[#BBBFC4]' : 'text-gray-900'"
            >
              {{ file.filename }}
            </div>
            <div
              class="flex items-center mt-1 text-xs"
              :class="file.isVirtual ? 'text-[#2E72E3]' : 'text-gray-500'"
            >
              <div v-if="fileIsLoading(file.id) === 'fileFinshed'" class="flex items-center mr-2">
                <svgIcon name="new-success" class="w-[18px] h-[18px] mr-1" />{{
                  $t('Note_status_ai_generated')
                }}
              </div>
              <div v-if="fileIsLoading(file.id) === 'fileLoading'" class="flex items-center mr-2">
                {{ $t('home_list_file_generating') }}
              </div>
              <template v-if="!file.isVirtual && fileIsLoading(file.id) !== 'fileLoading'">
                {{
                  file.duration < 1000 ? '1s' : getTimeName(getTimeStr(file.duration, false))
                }}</template
              >
              <template v-if="file.isVirtual">
                {{ $t('home_list_file_merging') }}{{ mergePercent }}%
              </template>
            </div>
          </div>

          <!-- 右侧区域 - 使用flex布局而不是absolute -->
          <div class="flex items-center flex-shrink-0 ml-4">
            <!-- 非hover状态显示文件夹图标和时间 -->
            <template v-if="!isFileActive(file)">
              <div v-if="getFileFolderName(file).length !== 0" class="flex items-center mr-4">
                <svgIcon name="folder" class="w-4 h-4 mr-1" />
              </div>
              <div
                class="w-40 text-center"
                :class="file.isVirtual ? 'text-[#BBBFC4]' : 'text-gray-700'"
              >
                {{ getDateStr(file.start_time) }}
              </div>
            </template>

            <!-- hover状态显示操作区域 -->
            <template v-else>
              <div v-if="getFileFolderName(file).length !== 0" class="flex items-center mr-6">
                <svgIcon name="folder" class="w-4 h-4 mr-1" />
                <span class="truncate max-w-[120px]">{{ getFileFolderName(file) }}</span>
              </div>
              <FileActionMenu
                :file="file"
                :menuItems="menuItems"
                @menu-command="handleMenuCommand"
                @dropdown-visible-change="handleDropdownVisibleChange"
                :folders="folders"
                @move-to-folder="handleMoveToFolder"
                @create-new-folder="handleCreateNewFolder"
                @handle-transcript-click="handleFileIconClick"
              />
            </template>
          </div>
        </li>
      </RecycleScroller>

      <!-- 空状态 -->
      <div v-else class="w-full h-full flex justify-center items-start">
        <EmptyState
          class="mt-[-10vh]"
          :title="emptyStateContent.title"
          :subtitle="emptyStateContent.subtitle"
        />
      </div>
    </div>
    <PPCDialog v-if="showPPCDialog" :hasData="infoAllList.length != 0" :onClose="onPPCDialog" />
    <RenameDialog
      v-if="showRenameDialog"
      :title="$t('rename_btn')"
      :value="currentRenameFile.filename"
      @confirm="handleRenameConfirm"
      @close="handleRenameClose"
    />
    <uploadDialog
      ref="uploadDialog"
      :title="$t('home_import_audio_title')"
      @close="handleUploadDialogClose"
      @upload-file="handleUploadFile"
    />

    <!-- WEB2.0的首页才展示 -->
    <UnlockUser v-if="userInfo.id && !userInfo.membership_id" @click="$emit('member-check', -1)" />

    <!-- Ask AI 浮动按钮 -->
    <div class="fixed bottom-[34px] right-[46px] z-50 cursor-pointer" @click="toggleAIPanel">
      <div
        class="rounded-full p-[1.5px] transition-all duration-200"
        style="
          border-radius: 500px;
          background: linear-gradient(90deg, #30f175 0%, #00d0ff 50%, #ae57ff 100%);
          box-shadow: 0px 8px 20.6px 0px rgba(108, 206, 255, 0.2);
        "
      >
        <div
          class="rounded-full px-4 py-2 flex items-center space-x-2"
          style="
            border-radius: 500px;
            background: linear-gradient(180deg, #fff 84.65%, #d4e7ff 126.87%);
          "
        >
          <svgIcon name="ai" class="w-[18px] h-[18px]" />
          <span class="text-sm font-normal text-[#1C1B1E]">{{
            $t('Filedetail_editor_AskAI')
          }}</span>
        </div>
      </div>
    </div>

    <Vue3Wrapper
      page-type="newchat"
      :userInfo="userInfo"
      :userBaseInfo="userInfo"
      :onChange="handleChatChange"
      v-if="showAIPanel"
      :allFiles="infoList"
      :myFiles="infoAllList"
    />
  </div>
</template>

<script>
import SvgIcon from '../../common/svg-icon/index.vue';
import IconButton from '../../common/icon-button/index.vue';
import FileActionMenu from './FileActionMenu.vue';
import FileListHeader from './header.vue';
import EventBus from '@/util/eventBus';
import FileMergeMixins from '@/util/FileMergeMixins';
import EmptyState from './empty.vue';

export default {
  mixins: [FileMergeMixins],
  name: 'FileList',
  components: {
    SvgIcon,
    IconButton,
    FileActionMenu,
    FileListHeader,
    EmptyState,
    PPCDialog: () => import('../../page/common/PPCDialog.vue'),
    RenameDialog: () => import('../common/RenameDialog.vue'),
    uploadDialog: () => import('../uploadDialog.vue'),
    Vue3Wrapper: () => import('../../common/vue3-wrapper.vue'),
    UnlockUser: () => import('../UnlockUser.vue'),
  },
  props: {
    userInfo: {
      type: Object,
      default: () => ({}),
    },
    showRight: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectMenu: '',
      infoAllList: [],
      isLoading: false,
      selectIds: [],
      pageNumber: 0,
      pagesize: 99999,
      sort_by: 'start_time',
      is_desc: 1,
      isAllHover: false,
      dropDownVisible: false,
      isScrolling: false,
      scrollTimer: null,
      currentFilter: 'all',
      activeFilters: {
        source: null,
        category: null,
      },

      currentSort: 'edit_time', // 默认按更新时间
      currentFolderName: '',
      folders: [],
      PPCStatus: true,
      showPPCDialog: false,
      showRenameDialog: false,
      currentRenameFile: {},
      savedScrollTop: 0,
      // AI 助手相关
      showAIPanel: false,
      statusTimer: null,
      statusInfo: {},
    };
  },
  computed: {
    orderList() {
      return [
        { id: 'start_time', name: this.$t('Siderbar_sort_conditionone'), is_desc: 0 },
        { id: 'edit_time', name: this.$t('Siderbar_sort_conditiontwo'), is_desc: 0 },
      ];
    },
    sourceFilters() {
      return [
        { key: 'note', label: this.$t('home_list_source_from_note'), scene: 1 },
        { key: 'call', label: this.$t('home_list_source_from_call'), scene: 7 },
        { key: 'notepin', label: this.$t('home_list_source_from_pin'), scene: 880 },
        { key: 'web', label: this.$t('home_list_source_from_web'), scene: 101 },
        { key: 'desktop', label: this.$t('home_list_source_from_desktop'), scene: 102 },
      ];
    },
    categoryFilters() {
      return [
        { key: 'unorganized', label: this.$t('home_list_source_category_unorganized') },
        { key: 'organized', label: this.$t('home_list_source_category_organized') },
      ];
    },
    sortOptions() {
      return [
        { key: 'edit_time', label: this.$t('home_list_modified_title') },
        { key: 'start_time', label: this.$t('home_list_created_title') },
      ];
    },
    greetingText() {
      // 默认时区为本地
      let timeZone =
        this.userInfo && this.userInfo.timezone
          ? this.userInfo.timezone
          : Intl.DateTimeFormat().resolvedOptions().timeZone;
      // 获取用户时区当前小时
      let now = new Date();
      let hour;
      try {
        // 利用 toLocaleString 获取用户时区的小时
        hour = Number(now.toLocaleString('en-US', { hour: '2-digit', hour12: false, timeZone }));
      } catch (e) {
        // 如果时区无效，回退本地
        hour = now.getHours();
      }

      let period = 'Morning';
      if (hour >= 5 && hour < 12) {
        period = 'Morning';
      } else if (hour >= 12 && hour < 18) {
        period = 'Afternoon';
      } else {
        period = 'Evening';
      }
      const name = this.userInfo && this.userInfo.nickname ? this.userInfo.nickname : '';
      const greetMap = {
        Morning: this.$t('home_list_greeting_morning'),
        Afternoon: this.$t('home_list_greeting_afternoon'),
        Evening: this.$t('home_list_greeting_evening'),
      };
      return `${greetMap[period]} ${name ? ', ' + name : ''}`;
    },
    emptyStateContent() {
      if (this.activeFilters.source || this.activeFilters.category) {
        let title;
        if (this.activeFilters.source && !this.activeFilters.category) {
          const sourceLabel = this.sourceFilters.find(
            (f) => f.key === this.activeFilters.source,
          )?.label;
          title = this.getTranslateStr(
            'home_list_filter_empty_title',
            '%s',
            `${this.$t('home_list_source_from_title')} ${sourceLabel}`,
          );
          // title = `"${this.$t('home_list_source_from_title')} ${sourceLabel}"`;
        } else {
          title = this.getTranslateStr('General_nofiles', this.currentFilterName);
        }

        return {
          title: title,
          subtitle: this.$t('home_list_filter_empty_subtitle'),
        };
      }
      if (this.categoryId === 'trash') {
        return {
          title: this.$t('home_list_trash_empty_title'),
        };
      }
      if (this.tagId) {
        return {
          title: this.$t('home_list_folder_empty_title'),
          subtitle: this.$t('home_list_folder_empty_subtitle'),
        };
      }
      return {
        title: this.$t('home_list_empty_title'),
        subtitle: this.$t('home_list_empty_subtitle'),
      };
    },
    tagId() {
      return this.$route.query.tagId || null;
    },
    categoryId() {
      return this.$route.query.categoryId || null;
    },
    filteredByTagOrCategory() {
      // 先根据 tagId/categoryId 过滤
      if (!this.tagId && !this.categoryId) {
        // 非回收站场景，只返回未删除的文件
        return this.infoAllList.filter((file) => !file.is_trash);
      }

      if (this.tagId) {
        // 在文件夹中，同样只返回未删除的文件
        return this.infoAllList.filter(
          (file) =>
            !file.is_trash && file.filetag_id_list && file.filetag_id_list.includes(this.tagId),
        );
      }

      if (this.categoryId === 'trash') {
        // 回收站场景，返回已删除的文件
        return this.infoAllList.filter((file) => file.is_trash);
      }

      // 其他场景，只返回未删除的文件
      return this.infoAllList.filter((file) => !file.is_trash);
    },
    infoList() {
      let list = this.filteredByTagOrCategory;

      // Filter by source
      const sourceKey = this.activeFilters.source;
      if (sourceKey) {
        const sourceFilter = this.sourceFilters.find((f) => f.key === sourceKey);
        if (sourceFilter && typeof sourceFilter.scene !== 'undefined') {
          list = list.filter((file) => file.scene === sourceFilter.scene);
        }
      }

      // Filter by category
      const categoryKey = this.activeFilters.category;
      if (categoryKey) {
        const folderIds = new Set(this.folders.map((f) => f.id));
        const isOrganized = (file) =>
          file.filetag_id_list && file.filetag_id_list.some((tagId) => folderIds.has(tagId));

        if (categoryKey === 'organized') {
          list = list.filter(isOrganized);
        } else if (categoryKey === 'unorganized') {
          list = list.filter((file) => !isOrganized(file));
        }
      }

      return list;
    },
    filterDisplayText() {
      const filterValues = Object.values(this.activeFilters).filter(Boolean),
        count = filterValues.length;
      if (count === 0) {
        return this.$t('home_list_source_all');
      }
      const labels = [...this.sourceFilters, ...this.categoryFilters]
        .filter((item) => filterValues.includes(item.key))
        .map((item) => item.label);
      // return `${this.$t('filter_btn')} ( ${this.infoList.length} )`;
      return `${labels.join(',')} ( ${this.infoList.length} )`;
    },
    currentFilterName() {
      const sourceLabel = this.sourceFilters.find(
        (f) => f.key === this.activeFilters.source,
      )?.label;
      const categoryLabel = this.categoryFilters.find(
        (f) => f.key === this.activeFilters.category,
      )?.label;
      const labels = [sourceLabel, categoryLabel].filter(Boolean);
      if (labels.length === 0) return this.$t('home_list_source_all');
      return labels.join(' & ');
    },
    menuItems() {
      if (this.categoryId === 'trash') {
        return [
          { icon: 'restore', label: this.$t('restore_btn'), command: 'restore' },
          { icon: 'new-trash', label: this.$t('delete_btn'), command: 'delete' },
        ];
      }
      return [
        { icon: 'rename', label: this.$t('rename_btn'), command: 'rename' },
        { icon: 'move', label: this.$t('move_btn'), command: 'move' },
        { icon: 'new-trash', label: this.$t('move_trash_btn'), command: 'trash' },
      ];
    },
  },
  activated() {
    console.log('FileList activated');
    // Restore scroll position when component is activated
    if (this.savedScrollTop > 0 && this.$refs.fileBox) {
      this.$nextTick(() => {
        this.$refs.fileBox.scrollToPosition(this.savedScrollTop);
      });
    }

    // Manually add the native scroll event listener to the component's root element
    this.$nextTick(() => {
      if (this.$refs.fileBox?.$el) {
        this.$refs.fileBox.$el.addEventListener('scroll', this.handlePageScroll, true);
      }
    });
    EventBus.$on('change-folder', this.getFolders);
    EventBus.$on('remove-folder', this.removeFolder);
  },
  deactivated() {
    console.log('FileList deactivated');
    // Manually remove the native scroll event listener
    if (this.$refs.fileBox?.$el) {
      this.$refs.fileBox.$el.removeEventListener('scroll', this.handlePageScroll, true);
    }
    EventBus.$off('change-folder', this.getFolders);
    EventBus.$off('remove-folder', this.removeFolder);
  },
  mounted() {
    console.log('FileList mounted');
    let orderInfo = sessionStorage.getItem('plaud_order');
    try {
      if (orderInfo) {
        let { id } = JSON.parse(orderInfo);
        let myOrder = this.orderList.find((item) => item.id == id);
        if (myOrder) {
          // 一定是降序
          this.doBySort(2, myOrder);
        } else {
          orderInfo = null;
        }
      }
    } catch (e) {
      orderInfo = null;
    }
    this.getFileList(99999, true, true);

    // console.log('userInfo', this.userInfo);
    this.getFolders();
    this.getPPCStatus();
    this.getTaskStatusList();
    // Scroll listener is now manually handled in activated/deactivated.
    EventBus.$on('move-to-folder', this.handleMoveToFolder);
    EventBus.$on('move-to-trash', this.moverToTrash);
    // refresh-list-status
    EventBus.$on('refresh-list-status', this.handleListStatus);
  },
  beforeDestroy() {
    // Defensive cleanup
    if (this.$refs.fileBox?.$el) {
      this.$refs.fileBox.$el.removeEventListener('scroll', this.handlePageScroll, true);
    }
    EventBus.$off('change-folder', this.getFolders);
    EventBus.$off('remove-folder', this.removeFolder);
    EventBus.$off('move-to-folder', this.handleMoveToFolder);
    EventBus.$off('move-to-trash', this.moverToTrash);
    EventBus.$off('refresh-list-status', this.handleListStatus);
    // 清理 AI 面板的键盘监听
    document.removeEventListener('keydown', this.handleEscKey);
    if (this.statusTimer != null) {
      clearTimeout(this.statusTimer);
    }
  },
  methods: {
    handleRenameClose() {
      this.showRenameDialog = false;
      this.currentRenameFile = {};
    },
    handleUploadDialogClose() {
      if (this.$refs.uploadDialog) {
        this.$refs.uploadDialog.handlerVisible(false);
      }
    },
    handleUploadFile(newFile) {
      if (newFile) {
        this.infoAllList.unshift(newFile);
      }
    },
    doBySort(flag, info) {
      if (flag != 3) {
        if (this.sort_by != info.id || flag == 2) {
          this.sort_by = info.id;
          if (info.id == 'start_time') {
            this.setGALogEvent('web_sort_create');
          } else {
            this.setGALogEvent('web_sort_edit');
          }
        }
        sessionStorage.setItem('plaud_order', JSON.stringify(info));
        this.getFileList();
      } else {
        this.isFileSelect = true;
        this.setGALogEvent('web_mutiple_select');
        if (info.id) {
          // this.selectFile(info);
        }
      }
      document.body.click();
    },
    isFileActive(file) {
      return (
        (this.selectIds.includes(file.id) || file.isHovered || file.dropDownVisible) &&
        !file.isVirtual
      );
    },
    changeHovered(hoveredFile, isHovered) {
      if (this.isScrolling) return;

      if (isHovered) {
        // When mouse enters an item, iterate through the list to 'un-hover' any other item that might be hovered.
        // This handles cases where mouseleave events don't fire quickly enough during fast mouse movement.
        this.infoAllList.forEach((file) => {
          if (file.id !== hoveredFile.id && file.isHovered) {
            this.$set(file, 'isHovered', false);
          }
        });
        // Then, set the current item as hovered.
        this.$set(hoveredFile, 'isHovered', true);
      } else {
        // On mouseleave, simply set its state to not hovered.
        this.$set(hoveredFile, 'isHovered', false);
      }
    },
    async getFileList(limit = 99999, hideLoading = false, checkPPC = false) {
      if (this.$refs.fileBox?.$el) {
        this.$refs.fileBox.$el.removeEventListener('scroll', this.handlePageScroll, true);
      }
      this.isLoading = true;

      this.selectIds = [];
      try {
        const promises = [
          this.reqGetInfo('/file/simple/web?' + this.getFlipNum(limit) + this.getConditions()),
        ];
        const [data] = await Promise.all(promises);
        let { data_file_list, data_file_total } = data;

        this.infoAllList = data_file_list.map((item) => ({
          ...item,
          isHovered: false,
          dropDownVisible: false,
        }));
        setTimeout(() => {
          if (this.$refs.fileBox?.$el) {
            this.$refs.fileBox.$el.addEventListener('scroll', this.handlePageScroll, true);
          }
        }, 0);
        this.isLoading = false;

        // this.isWholeLoading = false;

        // // 进入文件详情，文件块滚动到可视区域
        // parseHash('file_detail', () => {
        //   let dataStr = getQueryParam(window.location.href, 'data');
        //   let file = JSON.parse(dataStr);
        //   this.scrollFileIntoView(file.id);
        // });
        // 处理合并文件逻辑
        this.handleMergeFile();
      } catch (error) {
        console.error('Error fetching file list:', error);
        this.infoAllList = [];
        // this.isWholeLoading = false;
      }
    },
    getFlipNum(limit) {
      let skip = this.pageNumber * this.pagesize;
      // limit = this.pagesize;
      return `skip=${skip}&limit=${limit}`;
    },
    getConditions() {
      let cond = '&is_trash=2';
      cond += `&sort_by=${this.sort_by}`;
      cond += `&is_desc=true`;
      return cond;
    },
    // 文件hover操作选择
    handleMenuCommand(command, file) {
      console.log(`${command}:`, file);
      const ids = [file.id];
      if (command === 'trash') {
        this.handleMoveToTrash(ids);
        return;
      }
      if (command === 'restore') {
        this.handleRestore(ids);
        return;
      }
      if (command === 'delete') {
        this.handleDelete(ids);
      }
      if (command === 'rename') {
        this.showRenameDialog = true;
        this.currentRenameFile = { ...file };
      }
    },
    handleRenameConfirm(value) {
      const newName = value.trim();
      if (!newName) {
        this.$customMessage.warning(this.$t('home_list_file_name_empty_toast'));
        return;
      }
      if (newName === this.currentRenameFile.filename) {
        this.$customMessage.warning(this.$t('home_list_file_name_not_changed_toast'));
        return;
      }

      const id = this.currentRenameFile.id;
      const params = { filename: newName };
      this.reqPatchInfo('/file/' + id, params)
        .then((data) => {
          // 本地同步更新文件名
          this.infoAllList = this.infoAllList.map((item) => {
            if (item.id === id) {
              return { ...item, filename: newName };
            }
            return item;
          });
          this.$customMessage.success({
            message: this.$t('home_list_rename_file_success'),
          });
          this.handleRenameClose();
        })
        .catch(() => {});
    },
    handleDropdownVisibleChange(visible, file) {
      console.log('visible', visible);
      file.dropDownVisible = visible; // 更新下拉菜单状态
    },
    handleMoveToFolder(folder, fileOrIds) {
      // 判断 fileOrIds 是数组还是对象
      let ids = [];
      if (Array.isArray(fileOrIds)) {
        ids = fileOrIds;
      } else {
        ids = [fileOrIds];
      }
      console.log(ids, 'ids');
      this.infoAllList = this.infoAllList.map((item) => {
        if (ids.includes(item.id)) {
          // 返回新对象，确保响应式
          return { ...item, filetag_id_list: [folder.id], dropDownVisible: false };
        }
        return item;
      });
    },
    // 页面滚动
    handlePageScroll(event) {
      // console.log('handlePageScroll fired!', event.target.scrollTop);
      // This is now correctly fired by the RecycleScroller itself.
      if (event && event.target) {
        this.savedScrollTop = event.target.scrollTop;
      }

      this.isScrolling = true;
      // 滚动时重置所有文件的 hover 状态
      this.infoAllList.forEach((item) => {
        if (item.isHovered) {
          this.$set(item, 'isHovered', false);
        }
      });
      if (this.scrollTimer) clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.isScrolling = false;
        // 滚动停止后，检查当前鼠标位置，更新 hover 状态
        this.updateHoverState();
      }, 150); // 缩短延迟以提高响应性
    },
    updateHoverState() {
      if (!this.$refs.fileBox?.$el) return;
      // 使用 querySelector 找到当前被 hover 的元素
      const hoveredElements = this.$refs.fileBox.$el.querySelectorAll('.group:hover');
      if (hoveredElements.length > 0) {
        // 通常只应有一个，但为保险起见取最后一个
        const topHoveredElement = hoveredElements[hoveredElements.length - 1];
        const fileId = topHoveredElement.dataset.fileId;
        if (fileId) {
          const file = this.infoAllList.find((item) => item.id === fileId);
          if (file) {
            this.$set(file, 'isHovered', true);
          }
        }
      }
    },
    // 文件列表筛选
    handleFilterCommand(command) {
      if (!command || !command.type) return;

      if (command.type === 'reset') {
        this.activeFilters.source = null;
        this.activeFilters.category = null;
      } else if (command.type === 'source') {
        this.activeFilters.source = this.activeFilters.source === command.key ? null : command.key;
      } else if (command.type === 'category') {
        this.activeFilters.category =
          this.activeFilters.category === command.key ? null : command.key;
      }
      this.selectIds = [];
    },
    // 文件列表排序
    handleSortCommand(cmd) {
      this.currentSort = cmd;
      this.sort_by = cmd; // 你的 getFileList 里用的就是 sort_by
      this.getFileList();
    },
    // 文件选中/取消选择
    toggleSelect(e, id) {
      // console.log('toggleSelect', e.target, id);
      if (!this.selectIds.includes(id)) {
        this.selectIds.push(id);
        return;
      }
      // 如果 ID 已存在，则移除它
      const index = this.selectIds.indexOf(id);
      if (index > -1) {
        this.selectIds.splice(index, 1);
      }
    },
    //全选
    toggleSelectAll() {
      // 如果当前选中的数量等于文件列表数量，说明是全选状态，则取消全选
      if (this.selectIds.length === this.infoList.length) {
        this.selectIds = [];
      } else {
        // 否则，将所有文件的 ID 添加到 selectIds 中
        this.selectIds = this.infoList.map((file) => file.id);
      }
    },
    moverToTrash(delIDs) {
      console.log('moverToTrash');
      this.infoAllList = this.infoAllList.map((item) => {
        if (delIDs.includes(item.id)) {
          return { ...item, is_trash: true };
        }
        return item;
      });
    },
    handleMoveToTrash(delIDs = this.selectIds) {
      console.log('handleMoveToTrash');
      // 先调用接口
      this.reqPostInfo('/file/trash/', delIDs).then((data) => {
        // 将对应文件的 is_trash 设置为 true
        this.moverToTrash(delIDs);
        this.$customMessage.success({
          message: this.getTranslateStr(
            this.$t('home_list_move_file_to_trash'),
            '%s',
            delIDs.length,
          ),
        });
        // 清空选中
        this.exitSelectMode();
      });
    },
    // 获取文件夹列表
    getFolders() {
      console.log('getFolders');
      this.reqGetInfo('/filetag/').then((data) => {
        let { data_filetag_list } = data;
        this.folders = [...data_filetag_list];
        this.getCurrentFolderName();
      });
    },
    // 将某文件夹内容其他文件移动到Trash
    removeFolder(id) {
      // 找到所有属于该文件夹的文件
      const fileIds = this.infoAllList
        .filter((item) => item.filetag_id_list && item.filetag_id_list.includes(id))
        .map((item) => item.id);

      if (fileIds.length > 0) {
        this.handleMoveToTrash(fileIds);
      }
    },
    // 新建文件夹
    handleCreateNewFolder(ids) {
      if (event) event.stopPropagation();
      EventBus.$emit('crate-folder');
    },
    // 获取当前文件所在文件夹名称
    getFileFolderName(file) {
      if (file.filetag_id_list) {
        return file.filetag_id_list
          .map((id) => {
            const folder = this.folders.find((folder) => folder.id === id);
            return folder ? folder.name : '';
          })
          .filter(Boolean) // 过滤掉空字符串
          .join(', ');
      }
      return '';
    },
    // 获取当前所在文件夹名称
    getCurrentFolderName() {
      const tagId = this.$route.query?.tagId;
      const categoryId = this.$route.query?.categoryId;
      if (!tagId || this.folders.length === 0) {
        this.currentFolderName = '';
      }
      if (tagId && this.folders.length > 0) {
        const folder = this.folders.find((f) => String(f.id) === String(tagId));
        this.currentFolderName = folder ? folder.name : '';
      }
      if (categoryId && categoryId === 'trash') {
        this.currentFolderName = this.$t('side_trash_btn');
      }
    },
    exitSelectMode() {
      // 清空选中的文件ID
      this.selectIds = [];

      // 重置所有文件的hover状态
      this.infoAllList.forEach((item) => {
        this.$set(item, 'isHovered', false);
        this.$set(item, 'dropDownVisible', false);
      });
    },
    handleMerge() {
      // 处理 Merge 事件
      this.doMergeFile();
    },
    handleMove(folder) {
      // 处理 Move 事件
      this.handleMoveToFolder(folder, this.selectIds);
      this.exitSelectMode();
    },
    handleImport() {
      this.$refs.uploadDialog.handlerVisible(true);
    },
    handleRecord() {
      // 处理 Record 事件
      this.$emit('show-downLoad');
    },
    // 处理Trash内恢复
    handleRestore(ids = this.selectIds) {
      this.reqPostInfo('/file/untrash/', ids).then((data) => {
        // 本地同步更新
        this.infoAllList = this.infoAllList.map((item) => {
          if (ids.includes(item.id)) {
            return { ...item, is_trash: false };
          }
          return item;
        });
        this.$customMessage.success({
          message: `Restore ${ids.length} file`,
        });
        // 清空选中
        this.exitSelectMode();
      });
    },
    // 处理Trash内永久删除
    handleDelete(ids = this.selectIds) {
      this.setGALogEvent('web_trash_delete');
      this.setConfirm({
        title: this.$t('Sidebar_trash_delete_title'),
        msg: this.getTranslateStr('Sidebar_trash_deleted_content'),
        okname: this.$t('Sidebar_trash_deleted_confirm'),
        ok: () => {
          this.reqDeleteInfo('/file/', ids).then((data) => {
            // 本地同步移除
            this.infoAllList = this.infoAllList.filter((item) => !ids.includes(item.id));
            this.exitSelectMode();
            this.$customMessage.success({
              message: this.getTranslateStr('home_list_delete_file_tip', '%s', ids.length),
            });
          });
        },
      });
    },
    getPPCStatus() {
      this.reqGetInfo('/user/me/settings').then((data) => {
        console.log('data', data);
        if (data && !data.ppc_status) {
          if (!localStorage.getItem('CHECK_PPC')) {
            this.showPPCDialog = true;
          }
          this.PPCStatus = data.ppc_status;
        }
      });
    },
    onPPCDialog() {
      console.log('onPPCDialog');
      this.showPPCDialog = !this.showPPCDialog;
    },
    // 指定文件滚动到可视区域
    scrollFileIntoView(fileId) {
      // this.scrollTimer && window.clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        const index = this.infoList.findIndex((item) => item.id === fileId);
        console.log(index, 'index');
        console.log(this.$refs.fileBox, 'this.$refs.fileBox');
        this.$refs.fileBox.scrollToPosition(Math.max(0, index) * 72);
      }, 1000);
    },
    handleFileClick(file) {
      if (this.selectIds.length > 0) {
        this.toggleSelect(null, file.id);
      } else {
        // 进入详情页 - 根据当前路径判断跳转路径
        const targetPath = this.$route.path.includes('new')
          ? `/new/file/${file.id}`
          : `/file/${file.id}`;
        this.$router.push(targetPath);
      }
    },
    // AI 助手相关方法
    toggleAIPanel() {
      console.log('toggleAIPanel');
      let memberType = this.userInfo.membership_type;
      console.log('memberType:', memberType);
      this.authUser = ['pro', 'unlimited', 'backer'].includes(memberType);
      if (!this.authUser) {
        // this.$nextTick(() => {
        this.$emit('member-check', 5);
        // });
      } else {
        // this.isCenterStatus = 20;
        // this.middleStatus = false;
        // this.pageType = 'chat';
        this.showAIPanel = true;
        // this.$emit('show-ai-panel');
        console.log('showAIPanel:', this.showAIPanel);
      }
      this.$pldAnalytics.reportCustomEvent('sidebar_ask_ai');
    },
    handleChatChange(event, param) {
      console.log(event, param, 'event');
      if (event === 'close') {
        this.showAIPanel = false;
        return;
      }
      if (event === 'open-url') {
        const targetPath = this.$route.path.includes('new')
          ? `/new/file/${param.id}`
          : `/file/${param.id}`;
        this.$router.push(targetPath);
      }
      console.log('handleChatChange');
    },
    handleFileIconClick(file) {
      this.$emit('tran-summary-show', file);
    },
    // 列表上需要获取文件转写状态，用于显示文件转写状态
    getTaskStatusList() {
      if (this.statusTimer != null) {
        clearTimeout(this.statusTimer);
      }
      this.reqGetInfo('/ai/status').then((data) => {
        this.statusInfo = data;
        this.statusTimer = setTimeout(this.getTaskStatusList, 10000);
      });
    },
    handleListStatus() {
      this.reqGetInfo('/ai/status').then((data) => {
        this.statusInfo = data;
      });
    },
    fileIsLoading(fileId) {
      const {
        data_processing,
        data_processing_chatllm,
        data_processing_transsumm,
        data_processing_ai,
        data_processing_chatllm_ai,
        data_processing_transsumm_ai,
      } = this.statusInfo;

      const includesFileId = (list, key) => list && list[key] && list[key].includes(fileId);
      const someFileId = (list) => list && list.some((item) => item.file_id === fileId);

      const isProcessing =
        (data_processing && data_processing.includes(fileId)) ||
        includesFileId(data_processing_transsumm, 'files_trans') ||
        includesFileId(data_processing_transsumm, 'files_summ') ||
        someFileId(data_processing_chatllm);

      const isProcessingAI =
        (data_processing_ai && data_processing_ai.includes(fileId)) ||
        includesFileId(data_processing_transsumm_ai, 'files_trans') ||
        includesFileId(data_processing_transsumm_ai, 'files_summ') ||
        someFileId(data_processing_chatllm_ai);
      if (isProcessing) {
        return isProcessingAI ? 'fileFinshed' : 'fileLoading';
      }

      return '';
    },
  },
  watch: {
    '$route.query': {
      handler(newQuery, oldQuery) {
        this.getCurrentFolderName();
        // Only reset and refetch if the folder or category actually changes,
        // and we are not coming back from a detail page.
        if (newQuery.tagId !== oldQuery.tagId || newQuery.categoryId !== oldQuery.categoryId) {
          console.log('Route changed, refetching files...');
          // Reset selections and filters
          this.selectIds = [];
          this.activeFilters.source = null;
          this.activeFilters.category = null;
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped>
.file-filter-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 12px !important;
  height: auto !important;
  line-height: normal !important;
  min-width: 0 !important; /* 允许子元素收缩 */
}

.file-filter-item > span {
  flex: 1 !important;
  min-width: 0 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.clear-item {
  justify-content: flex-end !important;
}

.clear-item > span {
  text-align: right !important;
}

.file-filter-item:hover {
  background-color: #f3f3f3 !important;
}

.dropdown-header {
  padding: 8px 12px;
  font-size: 14px;
  color: #000;
  font-weight: 500;
  user-select: none;
}
/* 使用 :deep() 修改 el-dropdown-menu 和 el-dropdown-item 的样式 */
:deep(.el-dropdown-menu) {
  /* 设置下拉面板宽度 */
  min-width: 400px !important; /* 固定最小宽度 */
  /* 或使用 max-width 自适应 */
  /* max-width: 250px !important; */
  padding: 0; /* 可选：移除默认内边距 */
}

/* 调整 el-dropdown-item 的悬停和选中样式 */
:deep(.el-dropdown-menu__item) {
  padding: 0 16px; /* 调整内边距 */
  height: 40px; /* 调整高度 */
  line-height: 40px; /* 确保文本垂直居中 */
  transition: background-color 0.2s; /* 添加平滑过渡 */
}

/* 悬停样式 */
:deep(.el-dropdown-menu__item:hover) {
  background-color: #f3f3f3 !important;
  color: #000 !important;
}

/* 选中样式（使用 :focus 模拟选中状态，因为 el-dropdown-item 默认没有选中类） */
:deep(.el-dropdown-menu__item:focus),
:deep(.el-dropdown-menu__item.is-active) {
  background-color: transparent !important;
  color: #000 !important;
}

/* 确保 "Move to trash" 的红色样式优先级 */
:deep(.el-dropdown-menu__item.text-red-500) {
  color: #ff5449 !important; /* 保持红色 */
}

/* 悬停时保持 "Move to trash" 的红色 */
:deep(.el-dropdown-menu__item.text-red-500:hover) {
  background-color: #fff3f2 !important; /* 稍微浅的红色 */
}

.header-border::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 22px;
  right: 0;
  height: 1px;
  background-color: #e5e7eb; /* Corresponds to border-gray-200 */
}
</style>
