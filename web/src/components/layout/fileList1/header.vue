<template>
  <div class="flex items-center justify-between mb-[40px] h-[40px]">
    <template v-if="selectIds.length">
      <!-- 已选几条 -->
      <div class="flex-1 text-[14px] ml-[36px]">
        {{ $t('home_list_selected') }} ( {{ selectIds.length }} )
      </div>
      <div class="flex items-center space-x-3 text-[14px]">
        <template v-if="categoryId !== 'trash'">
          <!-- 合并按钮 -->
          <button
            class="h-10 px-9 flex items-center border border-[#E5E7EB] rounded-[6px] bg-white hover:bg-[#E5E7EB]"
            @click="$emit('merge')"
          >
            <svgIcon name="merge" class="mr-[6px] w-[18px] h-[18px]" />
            {{ $t('merge_btn') }}
          </button>

          <!-- 移动按钮 -->
          <el-dropdown trigger="click" placement="bottom-end">
            <button
              class="h-10 px-9 flex items-center border border-[#E5E7EB] rounded-[6px] bg-white hover:bg-[#E5E7EB] text-[#000]"
            >
              <svgIcon name="move" class="mr-[6px] w-[18px] h-[18px]" />
              {{ $t('move_btn') }}
            </button>
            <el-dropdown-menu slot="dropdown" class="w-[248px] rounded-[6px] p-0">
              <div>
                <!-- 搜索框 -->
                <div class="p-4 h-[66px]">
                  <el-input v-model="folderSearchQuery" :placeholder="$t('search')" size="small">
                    <template #prefix>
                      <svg-icon name="new-search" class="w-4 h-4 text-gray-400" />
                    </template>
                  </el-input>
                </div>
                <div class="folder-list max-h-60 overflow-y-auto" v-if="filteredFolders.length > 0">
                  <el-dropdown-item
                    :command="'multimove-folder-' + folder.id"
                    v-for="folder in filteredFolders"
                    :key="folder.id"
                    @click.native="handleMoveToFolder(folder)"
                    class="flex items-center cursor-pointer hover:bg-[#f3f3f3] hover:text-black h-[38px]"
                  >
                    <svg-icon name="folder" class="w-[18px] h-[18px] mr-2 flex-shrink-0" />
                    <span class="truncate flex-1">{{ folder.name }}</span>
                  </el-dropdown-item>
                </div>
                <div v-else class="text-gray-400 text-center py-2">
                  {{ $t('home_list_folder_empty') }}
                </div>
                <!-- 分割线 -->
                <div class="mx-4 h-px bg-[#E5E7EB] my-2"></div>
                <div
                  class="px-4 h-[38px] mb-4 flex items-center cursor-pointer hover:bg-[#f3f3f3] text-black text-[14px]"
                  @click.stop="handleCreateNewFolder"
                >
                  <svg-icon name="folderAdd" class="w-4 h-4 mr-2" />
                  <span>{{ $t('home_list_add_folder') }}</span>
                </div>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
          <span class="ml-2">
            <el-dropdown trigger="hover" placement="bottom-end">
              <span class="ml-2 text-xl">
                <iconButton name="more" class="w-[18px] h-[18px]" />
              </span>
              <el-dropdown-menu slot="dropdown" class="w-[248px] rounded-[6px]">
                <el-dropdown-item
                  class="flex items-center text-red-500"
                  @click.native="$emit('moveToTrash')"
                >
                  <svgIcon name="new-trash" class="w-4 h-4 mr-2" />
                  {{ $t('move_trash_btn') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </template>
        <template v-else>
          <button
            class="h-10 px-9 flex items-center border border-[#E5E7EB] rounded-[6px] bg-white hover:bg-[#E5E7EB]"
            @click="$emit('restore')"
          >
            <svgIcon name="restore" class="mr-[6px] w-[18px] h-[18px]" />
            {{ $t('restore_btn') }}
          </button>
          <button
            class="h-10 px-9 flex items-center border text-[#FF5449] border-[#FF5449] rounded-[6px] bg-white hover:bg-[#FFF3F2]"
            @click="$emit('delete')"
          >
            <svgIcon name="delete" class="mr-[6px] w-[18px] h-[18px]" />
            {{ $t('delete_btn') }}
          </button>
        </template>
      </div>
    </template>
    <template v-else>
      <div class="flex items-center w-full">
        <!-- 左侧标题 - 占用剩余空间并超出显示省略号 -->
        <div class="text-[18px] font-medium ml-[36px] truncate flex-1 min-w-0">
          {{ currentFolderName ? currentFolderName : greetingText }}
        </div>
        <!-- 右侧按钮 - 不收缩 -->
        <div
          class="flex items-center space-x-3 text-[14px] flex-shrink-0"
          v-if="!$route.query.tagId && !$route.query.categoryId"
        >
          <button
            class="h-10 px-9 flex items-center border border-[#E5E7EB] rounded-[6px] bg-white hover:bg-[#E5E7EB]"
            @click="$emit('import')"
          >
            <svgIcon name="upload" class="mr-[6px] w-[18px] h-[18px] flex-shrink-0" />
            <span>{{ $t('upload_audio_btn') }}</span>
          </button>
          <button
            class="h-10 px-9 flex items-center rounded-[6px] bg-black text-white hover:bg-[#323135]"
            @click="$emit('record')"
          >
            <svgIcon name="record" class="mr-[6px] w-[18px] h-[18px] flex-shrink-0" />
            {{ $t('start_recording_btn') }}
          </button>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import SvgIcon from '../../common/svg-icon/index.vue';
import IconButton from '../../common/icon-button/index.vue';
// import Templates from '../templates.vue';

export default {
  name: 'FileListHeader',
  emits: ['import', 'record', 'move', 'create-new-folder'],
  components: {
    SvgIcon,
    IconButton,
    // Templates,
  },
  props: {
    selectIds: {
      type: Array,
      default: () => [],
    },
    folders: { type: Array, required: true },
    currentFolderName: String,
    greetingText: String,
  },
  data() {
    return {
      folderSearchQuery: '',
    };
  },
  computed: {
    filteredFolders() {
      if (!this.folderSearchQuery) return this.folders;
      return this.folders.filter((folder) =>
        folder.name.toLowerCase().includes(this.folderSearchQuery.toLowerCase()),
      );
    },
    categoryId() {
      return this.$route.query.categoryId || null;
    },
  },
  methods: {
    handleMoveToFolder(folder) {
      event.stopPropagation();
      // 这里添加移动文件的逻辑
      this.folderSearchQuery = '';
      this.reqPostInfo('/file/update-tags', {
        file_id_list: this.selectIds,
        filetag_id: folder.id,
      })
        .then((data) => {
          this.$emit('move', folder);
          this.$customMessage.success({
            // this.getTranslateStr('Manage_logins_device_logout_content', this.selectIds.length, folder.name)
            message: this.getTranslateStr(
              'home_list_file_move_success',
              this.selectIds.length,
              folder.name,
            ),
            btnText: this.$t('visit_btn'),
            onBtnClick: () => {
              // 你的跳转或其他逻辑
              let prefix = '/';
              if (this.$router.currentRoute.path.includes('/new')) {
                prefix = '/new/';
              }
              // 你的跳转或其他逻辑
              this.$router.push({
                path: `${prefix}fileList?tagId=${folder.id}`,
              });
            },
          });
        })
        .catch((err) => {
          this.$customMessage.error({
            message: this.$t('home_list_file_move_fail'),
          });
        })
        .finally(() => {});
    },
    handleCreateNewFolder() {
      this.$emit('create-new-folder');
    },
  },
};
</script>

<style scoped>
/* 使用 :deep() 修改 el-dropdown-menu 和 el-dropdown-item 的样式 */
:deep(.el-dropdown-menu) {
  /* 设置下拉面板宽度 */
  min-width: 400px !important; /* 固定最小宽度 */
  /* 或使用 max-width 自适应 */
  /* max-width: 250px !important; */
  padding: 0 !important; /* 可选：移除默认内边距 */
}

/* 调整 el-dropdown-item 的悬停和选中样式 */
:deep(.el-dropdown-menu__item) {
  padding: 0 16px; /* 调整内边距 */
  height: 40px; /* 调整高度 */
  line-height: 40px; /* 确保文本垂直居中 */
  transition: background-color 0.2s; /* 添加平滑过渡 */
  color: #000 !important;
}

/* 悬停样式 */
:deep(.el-dropdown-menu__item:hover) {
  background-color: #f3f3f3 !important;
  color: #000 !important;
}

/* 选中样式（使用 :focus 模拟选中状态，因为 el-dropdown-item 默认没有选中类） */
:deep(.el-dropdown-menu__item:focus),
:deep(.el-dropdown-menu__item.is-active) {
  background-color: #f3f3f3 !important;
  color: #000 !important;
}

/* 悬停时保持 "Move to trash" 的红色 */
:deep(.el-dropdown-menu__item.text-red-500:hover) {
  background-color: #fff3f2 !important; /* 稍微浅的红色 */
  color: #ff5449 !important; /* 保持红色 */
}

/* 悬停时让 SVG 图标变红色 */
:deep(.el-dropdown-menu__item.text-red-500:hover svg) {
  fill: #ff5449 !important;
  color: #ff5449 !important;
}

/* 添加这个样式为嵌套下拉菜单设置背景 */
:deep(.folder-dropdown .el-dropdown-menu) {
  background-color: #fff !important;
  box-shadow: 2px 2px rgba(0, 0, 0, 0.1);
}

/* 确保下拉菜单项也有背景色 */
:deep(.folder-dropdown .el-dropdown-menu__item) {
  background-color: inherit !important;
}

/* 调整嵌套下拉菜单位置 */
.folder-dropdown {
  /* margin-left: 8px; */
  position: absolute;
  left: 0;
  top: 0;
}

/* ... 其他样式 ... */
:deep(.folder-dropdown .el-dropdown-menu__item:hover) {
  background-color: #f3f3f3 !important;
  color: #000 !important;
}

:deep(.folder-dropdown .el-input__inner) {
  border-color: #e5e7eb !important;
}

/* 设置搜索框前缀图标垂直居中 */
:deep(.el-input__prefix) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  left: 10px !important;
}
/* 修改 el-input 聚焦时的边框颜色 */
:deep(.el-input__inner:focus) {
  border-color: #00d0ff !important;
  border-width: 2px !important;
}
</style>
