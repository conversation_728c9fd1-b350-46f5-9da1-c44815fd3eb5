<template>
  <div class="app-wrap">
    <template v-if="compFlag === 1">
      <Home
        v-if="webVersion === 1"
        :isFromDesktop="isFromDesktop"
        :desktopPosition="desktopPosition"
      />
      <Home v-else :isFromDesktop="isFromDesktop" :desktopPosition="desktopPosition" />
    </template>
    <component
      :is="curPage.componentName"
      v-bind="curPage.propsData"
      v-on="curPage.eventHandlers"
      v-else
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { redirectToRedirectParam, urlHasRedirect } from '@/util/frill';
import { getAndRemoveQueryParam, launchDesktop } from '@/util/query';
import { getQueryParam, getHashBeforeQuery } from '@/util/common';
import { notLoggedInPages, loggedInPages } from '@/util/pageConfig';
import qs from 'qs';
export default {
  components: {
    Home,
    Auth: () => import('./page/Auth'),
    APIDoc: () => import('./page/APIDoc'),
    // Layout: () => import('./page/Layout'), // 暂时注释掉，使用Home组件
  },
  data() {
    return {
      compFlag: -1,
      changeTimer: null,
      curPage: {},
      timer: null,
      isFromDesktop: false,
      desktopPosition: '',
      webVersion: 1,
    };
  },
  computed: {
    ...mapState({
      currentLoginType: (state) => state.auth.currentLoginType, // 登录类型
      frillSsoToken: (state) => state.sso.frillSsoToken, // Frill ssoToken
    }),
  },
  mounted() {
    // console.log(this.$route.path, 'this.$route.path');
    console.log(location.href, 'location.href');
    let tokenstr = localStorage.getItem('tokenstr');
    if (tokenstr) {
      this.loginSuccess();
    } else {
      this.initNotLoggedInPage();
    }

    this.$nextTick(() => {
      this.initFBApp();
      let clientWidth = window.innerWidth;
      if (clientWidth < 1050) {
        this.setGALogEvent('web_open_low1050');
      } else if (clientWidth >= 1050 && clientWidth < 1300) {
        this.setGALogEvent('web_open_middle');
      } else {
        this.setGALogEvent('web_open_high1300');
      }
    });
    // window.addEventListener('resize', this.resizePage, false);
    this.initLoginTrack();
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizePage, false);
    if (this.changeTimer) {
      clearTimeout(this.changeTimer);
    }
    this.timer && clearTimeout(this.timer);
  },
  methods: {
    initLoginTrack() {
      this.$pldAnalytics.startTracking('login');
    },
    resizePage() {
      if (this.changeTimer) {
        clearTimeout(this.changeTimer);
      }
      this.changeTimer = setTimeout(() => {
        this.$nextTick(() => {
          let clientWidth = window.innerWidth;
          if (clientWidth < 1050) {
            this.setGALogEvent('web_pagesize_change_stop_low1050');
          } else if (clientWidth >= 1050 && clientWidth < 1300) {
            this.setGALogEvent('web_pagesize_change_stop_middle');
          } else {
            this.setGALogEvent('web_pagesize_change_stop_high1300');
          }
        });
      }, 300);
    },
    async loginSuccess() {
      const from = getAndRemoveQueryParam('from');
      const position = getAndRemoveQueryParam('position');
      this.desktopPosition = position;
      if (from === 'desktop') {
        this.isFromDesktop = true;
        launchDesktop(0, position)
          .then(() => {
            console.log('launchDesktop成功');
          })
          .catch(() => {
            console.log('launchDesktop失败');
          });
      }
      // this.compFlag = 1;
      this.determineComponent();
      this.initLoggedInPage();
      this.reportLoginTrackData();
    },
    reportLoginTrackData() {
      const additionalData = {
        method: this.currentLoginType,
      };
      this.$pldAnalytics.stopTracking('login');
      this.$pldAnalytics.reportCustomTimeEvent('login', additionalData);
    },
    // 已登陆状态包含redirect参数的跳转
    redirectToFrill() {
      if (urlHasRedirect()) {
        let tokenstr = localStorage.getItem('tokenstr');
        tokenstr && window.localStorage.removeItem('tokenstr');
        // tokenstr && redirectToRedirectParam(this.frillSsoToken);
      }
    },
    /**
     * 解析url中的flag参数
     * flag 存在，则展示flag对应pageData的页面
     * # 存在，则展示#对应的pageData的页面
     * flag 不存在，则展示Home
     */
    initLoggedInPage() {
      let currentURL = window.location.href;
      let flag = getQueryParam(currentURL, 'flag');
      if (flag && loggedInPages.flag.includes(flag)) {
        this.compFlag = flag;
        this.curPage = this.pageData[flag];
      } else if (currentURL.includes('#')) {
        let hashName = getHashBeforeQuery(currentURL);
        if (loggedInPages.hash.includes(hashName)) {
          this.compFlag = hashName;
          this.curPage = this.pageData[hashName];
        } else {
          this.compFlag = 1;
          // 设置默认的Home组件
          this.curPage = {
            componentName: 'Home',
            propsData: {
              isFromDesktop: this.isFromDesktop,
              desktopPosition: this.desktopPosition
            },
            eventHandlers: {}
          };
        }
      } else {
        this.compFlag = 1;
        // 设置默认的Home组件
        this.curPage = {
          componentName: 'Home',
          propsData: {
            isFromDesktop: this.isFromDesktop,
            desktopPosition: this.desktopPosition
          },
          eventHandlers: {}
        };
      }
    },
    initNotLoggedInPage() {
      let flag = getQueryParam(window.location.href, 'flag');
      if (notLoggedInPages.includes(flag)) {
        this.compFlag = flag;
        this.curPage = this.pageData[flag];
      } else {
        console.log('login from mainBody');
        this.$router.replace({
          path: '/login',
          query: { from_url: window.location.href },
        });
      }
    },
    async determineComponent() {
      // 后续去掉，后面没有/new了
      if (location.href.includes('new')) {
        this.webVersion = 2;
        window.version = this.webVersion;
        return;
      }
      // ********后续以这里为判断准则********
      this.webVersion = 1;
      window.version = this.webVersion;
      // try {
      //   const data = await this.reqGetInfo('/user/me');
      //   const state = data.data_state;
      //   if (state.is_inner) {
      //     const storeVersion = localStorage.getItem('version');
      //     // 尊重用户切换的版本
      //     if (storeVersion == 1) {
      //       this.webVersion = 1;
      //     } else {
      //       this.webVersion = 2;
      //     }
      //     window.version = this.webVersion;
      //   }
      // } catch (err) {
      //   this.webVersion = 1;
      // }
    },
  },
};
</script>
<style lang="scss" scoped>
.app-wrap {
  height: 100%;
  overflow: hidden;
}
</style>
