<!--
- @desc:类型 成功：success 警告：warning 异常：error 消息：info
-->
<template>
  <div class="infoTips" :class="[type]">
    <div
      class="tipcloseBtn plaud icon-close"
      style="transform: scale(0.9)"
      @click="closeTip"
      v-if="!autoClose"
    ></div>
    <div class="iconfont icon-ok1" v-if="type == 'success'"></div>
    <span v-html="message"></span>
  </div>
</template>

<script>
export default {
  name: 'Message',
  props: {
    message: {
      type: String,
      default: ''
    },
    moremsg: {
      type: String,
      default: ''
    },
    duration: {
      type: Number,
      default: 2000
    },
    type: {
      type: String,
      default: 'info'
    },
    autoClose: {
      type: Boolean,
      default: true
    },
    onClose: {
      type: Function,
      default: null
    }
  },
  mounted() {
    this.startTimer();
    document.addEventListener('keydown', this.keydown);
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.keydown);
  },
  methods: {
    keydown(e) {
      if (e.keyCode === 27) {
        // esc关闭消息
        if (!this.closed) {
          this.closeTip();
        }
      }
    },
    startTimer() {
      if (this.duration > 0 && this.autoClose) {
        this.timer = setTimeout(() => {
          this.closeTip();
        }, this.duration);
      }
    },
    closeTip() {
      this.onClose();
    },
    destroyEL() {
      // In Vue 3, $destroy is no longer available
      // The cleanup will be handled by the main.js file
      if (this.onClose) {
        this.onClose();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.infoTips {
  position: fixed;
  left: 50%;
  top: 108px;
  background: #1d2939;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transform: translate(-50%, -50%);
  z-index: 999999999;
  padding: 10px 16px;
  box-sizing: border-box;
  min-height: 52px;
  min-width: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 14px;
  text-align: center;
  &.success {
    justify-content: flex-start;
    .icon-ok1 {
      color: #30d158;
      margin-right: 16px;
      font-size: 22px;
    }
  }
  &.warning {
    border-color: #e6a23c;
  }
  &.error {
    border-color: #f56c6c;
  }
  &.info {
  }
  .tipcloseBtn {
    position: absolute;
    right: 3px;
    top: 3px;
    font-size: 12px;
    cursor: pointer;
  }
}
</style>
