<template>
  <div class="cfmPopBg">
    <div class="cfmWin standard-font" :style="{ width: width }">
      <div class="titleDialogBox">
        <div class="text-base font-medium">{{ title }}</div>
<!--        <div class="iconfont icon-Close" @click="cancelEvt()" v-if="hasClose"></div>-->
      </div>
      <div class="msgCont" :style="msgStyle" v-html="msg"></div>
      <div  class="flex justify-end" :style="btnBoxStyle">
        <div
          class=" popButton cancel-btn"
          :class="{ gray: !backCancel }"
          :style="cancelbtnStyle"
          @click.stop="cancelEvt"
        >
          {{ cancelname }}
        </div>
        <div
          class="popButton ok-btn"
          :class="{ red: redok }"
          :style="okbtnStyle"
          @click.stop="okEvt"
          v-if="ok"
        >
          {{ okname }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Confirm',
  props: {
    title: {
      type: String,
      default: '提示'
    },
    cancel: {
      type: Function,
      default: null
    },
    cancelname: {
      type: String,
      default: ''
    },
    msgStyle: {
      type: Object,
      default: () => ({})
    },
    ok: {
      type: Function,
      default: null
    },
    redok: {
      type: Boolean,
      default: true
    },
    hasClose: {
      type: Boolean,
      default: true
    },
    okname: {
      type: String,
      default: 'Confirm'
    },
    backCancel: {
      type: Boolean,
      default: false
    },
    okbtnStyle: {
      type: Object,
      default: () => ({})
    },
    cancelbtnStyle: {
      type: Object,
      default: () => ({})
    },
    btnBoxStyle: {
      type: Object,
      default: () => ({})
    },
    msg: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: ''
    },
    onDestroy: {
      type: Function,
      default: null
    }
  },
  methods: {
    okEvt() {
      if (this.ok != null) {
        this.ok();
      }
      this.destroyEL();
    },
    cancelEvt() {
      if (this.cancel != null) {
        this.cancel();
      }
      this.destroyEL();
    },
    destroyEL() {
      // In Vue 3, cleanup is handled by the main.js file
      if (this.onDestroy) {
        this.onDestroy();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.cfmPopBg {
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.4);
  .cfmWin {
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -50%);
    z-index: 100000;
    background: #fff;
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: none;
    padding: 24px;
    width: 416px;
  }
  .popButton{
    @apply h-[40px] min-w-[98px] px-3 rounded-[6px] cursor-pointer flex justify-center items-center ml-3 text-sm font-normal;
    &.cancel-btn{
      @apply border border-[#D5D6D7] text-[#060606];
    }
    &.ok-btn{
      @apply bg-[#1C1B1E] text-[#fff];
      &.red{
        @apply bg-[#FF5449];
      }
    }
  }
  .msgCont {
    padding: 16px 0px;
    font-weight: 400;
    font-size: 14px;
    color: #646A73;
    line-height: 22px;
    text-align: left;
    word-break: break-word;
    min-height: 100px;
    b {
      color: #1f1f1f;
    }
  }
}
</style>
