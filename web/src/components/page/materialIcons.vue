<!-- materialIcons 预览
     预览方式：把名字改为Home.vue -->
<template>
  <div class="icons fixed top-0 left-0 w-full h-full overflow-y-auto flex-col">
    <div class="flex flex-wrap" v-for="icon in allIcons" :key="icon">
      <span class="material-symbols-rounded">{{ icon }}</span>
      <span>{{ icon }}</span>
    </div>
  </div>
</template>
<script>
import { MATERIAL_ICONS } from '@/data/material_icons';
import { categoryIcons } from '@/util/material-icons-collection';

export default {
  data() {
    return {
      allIcons: [],
    };
  },
  mounted() {
    this.getAllIcons();
  },
  methods: {
    getAllIcons() {
      // 展示所有图标
      // this.allIcons = Object.keys(MATERIAL_ICONS);
      // 展示特定图标
      this.allIcons = Object.values(categoryIcons).flat();
    },
  },
  beforeDestroy() {},
};
</script>
<style></style>
