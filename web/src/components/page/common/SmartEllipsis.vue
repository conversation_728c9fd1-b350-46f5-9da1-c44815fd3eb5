<template>
  <div ref="containerRef" class="smart-ellipsis">
    <div ref="titleRef" class="title text-[14px] font-semibold text-[#1f1f1f] mb-1 line-clamp-3">
      {{ textOne }}
    </div>
    <template v-if="type === 'system'">
      <div
        class="flex items-center flex-wrap"
        v-if="!isTitleOverflow && textThree && textThree.length > 0"
        :style="{ WebkitLineClamp: descClamp }"
      >
        <div
          v-if="textThree"
          class="rounded-[4px] whitespace-nowrap py-[2px] px-1 mr-2 mb-1 bg-[#f2f4f7] text-[#667085] text-[10px] font-medium leading-[10px]"
          v-for="(w, index) in textThree"
          :key="index"
        >
          {{ w }}
        </div>
      </div>
    </template>
    <template v-else>
      <div
        v-if="!isTitleOverflow"
        ref="descRef"
        class="text-[12px] text-[#98a2b3] leading-[18px]"
        :style="{ WebkitLineClamp: descClamp }"
      >
        {{ textTwo }}
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'SmartEllipsis',
  props: {
    type: {
      type: String,
      default: 'community',
    },
    textOne: {
      type: String,
      default: '',
    },
    textTwo: {
      type: String,
      default: '',
    },
    textThree: {
      type: Array,
      default: () => [],
    },
    lineHeight: {
      type: Number,
      default: 20,
    },
    maxLines: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      isTitleOverflow: false,
      descClamp: 0,
    };
  },
  mounted() {
    this.updateClamp();
  },
  watch: {
    textOne() {
      this.$nextTick(this.updateClamp);
    },
    textTwo() {
      this.$nextTick(this.updateClamp);
    },
    textThree() {
      this.$nextTick(this.updateClamp);
    },
    lineHeight() {
      this.$nextTick(this.updateClamp);
    },
    maxLines() {
      this.$nextTick(this.updateClamp);
    },
  },
  methods: {
    updateClamp() {
      this.$nextTick(() => {
        const titleEl = this.$refs.titleRef;
        const titleHeight = titleEl ? titleEl.clientHeight : 0;
        const titleLines = Math.round(titleHeight / this.lineHeight);

        if (titleLines >= this.maxLines) {
          this.isTitleOverflow = true;
        } else {
          this.isTitleOverflow = false;
          this.descClamp = this.maxLines - titleLines;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.smart-ellipsis {
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: hidden;
}
.title,
.description {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  line-height: 20px;
}
.title {
  -webkit-line-clamp: 3;
}
.description {
  -webkit-line-clamp: 1; /* 默认先1，js计算后覆盖 */
}
</style>
