<template>
  <div class="userCenterBox userSet" style="margin-top: 23px">
    <!-- <div class="h4 flex-middle"> -->
    <!--      <div-->
    <!--        class="iconfont icon-right flex-center backSetBtn"-->
    <!--        @click="$parent.goTo(0)"-->
    <!--        style="margin-left: 0px; margin-right: 10px; font-weight: normal"-->
    <!--      ></div>-->
    <!-- {{ $t('Me_account_title') }}
    </div> -->
    <h6 style="margin-top: 32px">{{ $t('Me_account_picture') }}</h6>
    <div style="margin-top: 26px" class="uNameIcon uNameIconBig">
      <img :src="userInfo.avatar" v-if="userInfo.avatar" />
      <span v-else>{{ userInfo.nickname ? userInfo.nickname.substring(0, 1) : '' }}</span>
      <div class="edit iconfont icon-edit file-btn">
        <input
          type="file"
          class="my-file"
          accept="image/*"
          @change="updateAvater"
          ref="fileInput"
        />
      </div>
    </div>
    <div class="uline"></div>
    <h6>{{ $t('Me_account_name') }}</h6>
    <input type="text" class="iptName" v-model.lazy="nickname" @input="errorMsg = ''" />
    <div class="formError visible">{{ errorMsg }}</div>
    <div class="formSmallBtns" style="margin-top: 5px">
      <div
        class="smallComBtn active"
        :class="{ disable: nickname == userInfo.nickname }"
        @click="saveNickName()"
      >
        {{ $t('Filelist_selected_rename_button_right') }}
      </div>
      <div
        class="smallComBtn white active"
        :class="{ disable: nickname == userInfo.nickname }"
        @click="cancelNickName()"
      >
        {{ $t('Filelist_selected_rename_button_left') }}
      </div>
    </div>

    <div class="uline"></div>
    <h6 style="margin-bottom: 16px">{{ $t('Web_setting') }}</h6>
    <div class="h7" style="margin-bottom: 8px">
      {{ $t('Web_setting_language') }}
    </div>
    <el-select v-model="selectLang" placeholder="please select" style="width: 448px">
      <el-option v-for="item in languageList" :key="item.id" :label="item.name" :value="item.id">
      </el-option>
    </el-select>
    <div class="formSmallBtns" style="margin-top: 8px; padding-bottom: 20px">
      <div
        class="smallComBtn"
        :class="{
          active: mySelectLang != selectLang,
          disable: mySelectLang == selectLang,
        }"
        @click="saveLang(true)"
      >
        {{ $t('Web_setting_button_left') }}
      </div>
      <div
        class="smallComBtn white"
        :class="{
          active: mySelectLang != selectLang,
          'cursor-desabled': mySelectLang == selectLang,
        }"
        @click="saveLang(false)"
      >
        {{ $t('Web_setting_button_right') }}
      </div>
    </div>
    <div class="h7" style="margin-bottom: 8px">
      {{ $t('Web_setting_language_trans') }}
    </div>
    <el-select
      v-model="selectTransLang"
      :placeholder="$t('Web_setting_language_transnone')"
      style="width: 448px"
    >
      <el-option v-for="item in transLangList" :key="item.id" :label="item.name" :value="item.id">
      </el-option>
    </el-select>
    <div class="transDesc" style="width: 448px">
      {{ $t('Web_setting_language_transdesc') }}
    </div>
    <div class="formSmallBtns">
      <div
        class="smallComBtn"
        :class="{
          active: mySelectTransLang != selectTransLang,
          disable: mySelectTransLang == selectTransLang,
        }"
        @click="saveTransLang(true)"
      >
        {{ $t('Web_setting_button_left') }}
      </div>
      <div
        class="smallComBtn white"
        :class="{
          active: mySelectTransLang != selectTransLang,
          'cursor-desabled': mySelectTransLang == selectTransLang,
        }"
        @click="saveTransLang(false)"
      >
        {{ $t('Web_setting_button_right') }}
      </div>
    </div>
    <div class="uline sline" ref="integration"></div>
    <!-- zapier 入口：暂时隐藏，等zapier integration 审核通过再开放 -->
    <h6 style="margin-bottom: 0">
      {{ $t('WEB_my_industry_glossary') }}
      <img :src="proAndUnlimited" class="w-[72px] inline relative top-[-4px]" />
    </h6>
    <div class="transDesc">
      {{ $t('WEB_my_industry_glossary_tip') }}
    </div>

    <div class="flex items-center w-[448px] h-[50px] mt-6 px-4 rounded-lg bg-[#F9FAFB]">
      <span class="flex-1 text-[#1F1F1F] text-[15px] font-medium">{{
        $t('WEB_enhance_transcription')
      }}</span>
      <el-divider direction="vertical" class="mx-4"></el-divider>
      <el-switch
        :value="settings.is_correct"
        @input="onSwitch()"
        :width="36"
        active-color="#1F1F1F"
        inactive-color="#D0D5DD"
      ></el-switch>
    </div>

    <div class="h7 mt-6 mb-2">{{ $t('WEB_vocabulary') }}</div>
    <div class="w-[448px] h-[100px] rounded-lg bg-[#F9FAFB] vocabulary">
      <div class="flex items-center h-[50px] relative">
        <span class="absolute px-4 flex-1 text-[#1F1F1F] text-[15px] font-medium">{{
          $t('WEB_industry_preference')
        }}</span>
        <el-select
          class="w-full industry-select"
          v-model="settings.industry"
          clearable
          :placeholder="$t('WEB_please_select')"
          popper-class="vocabulary-select"
          @change="onIndustryChange"
        >
          <el-option
            v-for="item in industryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="flex items-center h-[50px] px-4" @click="onEdit(true)">
        <span class="flex-1 text-[#1F1F1F] text-[15px] font-medium">{{
          $t('WEB_custom_terms')
        }}</span>
        <div class="max-w-[227px] overflow-hidden text-ellipsis whitespace-nowrap">
          {{ settings.words }}
        </div>
        <div v-if="settings.words.length">({{ words.length }})</div>
        <div v-else class="text-[#858C9B] text-[13px]">{{ $t('WEB_please_add_your_terms') }}</div>
        <img :src="renameImg" class="cursor-pointer w-5 h-5 ml-2" />
      </div>
    </div>

    <div class="uline sline" ref="integration"></div>
    <!-- zapier 入口：暂时隐藏，等zapier integration 审核通过再开放 -->
    <h6 style="margin-bottom: 0">{{ $t('integrations_title') }}</h6>

    <ul class="loginList integration-list">
      <li class="loginItem integration-item" v-for="item in integrationInfo" :key="item.id">
        <component
          :is="item.componentName"
          v-if="item.componentName"
          v-bind="item.propsData"
          v-on="item.eventHandlers"
        ></component>
      </li>
    </ul>
    <div class="uline sline"></div>

    <h6 style="margin-bottom: 16px">{{ $t('Web_setting_share_ideas') }}</h6>
    <div class="share-box">
      <div class="share-btn" @click="skipToFrill">
        {{ $t('Web_setting_share_ideas_button') }}
        <div class="circle-red" v-if="frillRedDotShow"></div>
      </div>
    </div>

    <div class="uline"></div>
    <h6>{{ $t('account_sso_manage_selection_button_title') }}</h6>
    <div v-if="isEmailShow" class="emailManagerWrap border-radius-8">
      <div class="emailManager flexSpaceBetween">
        <div class="titleWrap flexSpaceBetween">
          <i class="iconfont icon-icon_email"></i>
          <div>{{ $t('Me_account_email') }}</div>
        </div>
        <div class="emailWrap">
          <div class="lightFont">{{ userInfo.email }}</div>
        </div>
      </div>
      <div class="separatorLine"></div>
      <div class="passwordManager flexSpaceBetween">
        <div class="titleWrap flexSpaceBetween">
          <i class="iconfont icon-icon_password"></i>
          <div class="hand" @click="changePwd()">
            {{ $t('Me_account_changepassword') }}
          </div>
        </div>
      </div>
    </div>

    <div class="ssoManagerWrap border-radius-8 mt14">
      <template v-if="!ssoRenderList.length">
        <div class="skeleton">
          <div class="skeleton-part">
            <div v-for="(item, index) in 2" class="skeleton-part-common" :key="index"></div>
          </div>
        </div>
      </template>
      <template v-else v-for="(item, index) in ssoRenderList" :key="item.name">
        <div class="ssoManager flexSpaceBetween">
          <div class="titleWrap flexSpaceBetween">
            <img class="logo" :src="getImagePath(item.icon)" />
            <div>{{ item.name }}</div>
          </div>
          <div class="actionWrap">
            <div v-if="item.loading">
              <i class="el-icon-loading ml6"></i>
              <div :class="{ ssoLoadingMask: item.loading }"></div>
            </div>
            <div
              v-else-if="isSSOReadonly && item.action == 'Remove'"
              class="lightFont cursor-default gray"
            >
              {{ handleSSOActionTrans(item.action) }}
            </div>
            <div
              v-else
              class="lightFont hand"
              @click="handleSSOAction(item)"
              :class="item.action == 'Add' ? 'blue' : 'red'"
            >
              {{ handleSSOActionTrans(item.action) }}
            </div>
            <div
              v-if="item.name == 'Google' && item.action == 'Add'"
              ref="googleCoverBtn"
              class="ssoCoverBtn hand flex-center"
            >
              <div ref="googleOriginBtn" class="googleOriginBtn"></div>
            </div>
            <div v-if="item.name == 'Apple' && item.action == 'Add'" class="ssoCoverBtn hand">
              <div
                id="appleid-signin"
                data-type="sign in"
                ref="appleOriginBtn"
                class="appleOriginBtn"
              ></div>
            </div>
            <!-- <div
              v-if="item.name == 'Microsoft'  && item.action == 'Add'"
              class="ssoCoverBtn hand"
              >
                <div
                  id="microsoftid-signin"
                  data-type="sign in"
                  ref="microsoftOriginBtn"
                  class="microsoftOriginBtn"
                  @click="handleMicrosoftCallback"
                >
                </div>
            </div> -->
          </div>
        </div>
        <div v-if="index < ssoRenderList.length - 1" class="separatorLine" :key="index"></div>
      </template>
      <div class="ssoManagerCoverWrap"></div>
    </div>
    <div class="formError" :class="{ visible: formError.network && formError.network.status }">
      {{ formError.network ? formError.network.msg : '' }}
    </div>

    <div class="uline mt0"></div>
    <h6>{{ $t('Manage_logins') }}</h6>
    <div class="loginList">
      <div class="loginItem" v-for="info in processedLoginList" :key="info.id">
        <div class="flex-middle">
          <div class="title">
            {{ info.displayName }}
            {{ info.count }}
          </div>
          <div class="current" v-if="info.is_current">
            {{ $t('Manage_logins_device_current') }}
          </div>
        </div>
        <div class="desc" style="margin-top: 12px">
          {{ info.translatedDesc }}
        </div>
        <div class="desc" style="margin-top: 4px">
          {{ info.formattedTime }}
        </div>
        <div class="flex" style="margin-top: 12px">
          <div class="logoutBtn" @click="removeToken(info)">
            {{ info.accountLogout }}
          </div>
        </div>
      </div>
    </div>

    <div class="uline"></div>
    <div class="formSmallBtns" style="margin-top: 14px; padding-bottom: 20px">
      <div class="smallComBtn red hand" @click="doLogout(true)">
        {{ $t('Me_account_logout') }}
      </div>
    </div>
    <Loading :status="isLoading" />
    <ChangePwd ref="changePwd" />
    <!-- 添加气泡提示 -->
    <bubble-alert
      v-if="showBubbleAlert"
      :message="bubbleAlertMessage"
      type="error"
      @close="handleBubbleClose"
      :auto-close="true"
      custom-class="fixed top-12 left-1/2 transform  z-50  -translate-x-1/2"
    >
      <!-- showBubbleAlert = false -->
      <template #action>
        <div
          class="view-link text-[#007AFF] cursor-pointer text-[14px] text-ellipsis overflow-hidden font-semibold bg-[#F6F8FA] px-4 py-1.5 rounded-full text-center ml-2"
          @click="handleRetry"
        >
          <span>{{ $t('upload_user_avatar_retry') }}</span>
        </div>
      </template>
      <!-- showRetryAction -->
    </bubble-alert>
    <SettingDialog
      v-if="showSettingDialog"
      :onClose="onEdit"
      :onChange="onWordsChange"
      :data="words"
      :onShowAddDialog="onShowAddDialog"
      :text="text"
    />
    <AddDialog
      v-if="showAddDialog"
      :onClose="onShowAddDialog"
      :data="words"
      :onChange="onWordsChange"
    />
  </div>
</template>
<script>
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex';
import langList from '@/data/sys_language.js';
import transLangList from '@/data/trans_language.js';
import i18n, { setLocale } from '../../../language';
import { getFrillUrl, FRILL_DATA } from '@/util/frill';

import ChangePwd from './ChangePwd';
import Cookies from 'js-cookie';
import { changeZELang } from '@/util/zendesk';
import { parseHash, removeURLParams } from '@/util/pageConfig';
import BubbleAlert from '../../common/BubbleAlert.vue';
// import FreeTrial from '@/components/page/common/membertip/FreeTrial.vue';
import SettingDialog from './SettingDialog.vue';
import AddDialog from './AddDialog.vue';

import renameImg from '@/images/rename-new.png';

import proAndUnlimited from '@/images/pro-unlimited.png';

export default {
  name: 'UserSet',
  components: {
    ChangePwd,
    // FreeTrial,
    SettingDialog,
    AddDialog,
    IntegrationBlock: () => import('./integration/IntegrationBlock.vue'),
    BubbleAlert,
  },
  data() {
    return {
      proAndUnlimited,
      text: '',
      showAddDialog: false,

      showSettingDialog: false,
      renameImg,
      industryOptions: [],
      value: '',
      userInfo: this.$root.userInfo,
      nickname: '',
      loginList: [],
      ssoList: {}, // sso列表，执行initSSOData声明初始状态
      ssoRenderList: [], // sso渲染列表，根据ssoList内容序号no排序
      ssoCount: 0, // sso绑定账号数量，关联action状态
      isEmailShow: false, // 是否展示邮箱
      formError: {},
      errorMsg: '',
      selectLang: 'en',
      mySelectLang: '',
      languageList: langList,
      transLangList: transLangList.filter((item) => item.id != 'auto'),
      enableSummaryAuto: false,
      selectTransLang: '',
      mySelectTransLang: '',
      integrationInfo: [
        {
          id: 'Zapier',
          componentName: 'IntegrationBlock',
          propsData: {
            name: this.$t('zapier_text'),
            type: 'Zapier',
            iconName: 'zapier_logomark',
          },
          eventHandlers: {
            loadingStatus: (status) => {
              this.isLoading = status;
            },
          },
        },
        {
          id: 'More',
          componentName: 'IntegrationBlock',
          propsData: {
            name: '',
            type: 'More',
          },
          eventHandlers: {},
        },
      ],
      showBubbleAlert: false,
      bubbleAlertMessage: '',
      settings: {
        is_correct: false, // 是否提升转写质量
        industry: '', // 行业偏好
        words: '', // 自定义词汇
      },
    };
  },
  computed: {
    ...mapState({
      authInfo: (state) => state.auth.authInfo, // 登录信息
      currentLoginType: (state) => state.auth.currentLoginType, // 登录类型
      frillSsoToken: (state) => state.sso.frillSsoToken, // Frill ssoToken
    }),
    ...mapGetters('sso', ['microsoftModel']), // microsoft实例
    ...mapGetters('auth', ['redDotShow', 'frillRedDotShow']),
    isSSOReadonly() {
      return this.ssoCount == 1 && !this.isEmailShow; // sso只读状态
    },
    // 公测白名单
    // isOuter() {
    //   return this.$root?.userStateInfo?.is_outer;
    // },
    words() {
      return this.settings.words ? this.settings.words.split(',').map((item) => item.trim()) : [];
    },
    processedLoginList() {
      return this.loginList.map((info) => ({
        ...info,
        displayName: info.client_id === 'web' ? 'PLAUD WEB' : 'PLAUD APP',
        translatedDesc: this.getTranslateStr(
          'Manage_logins_device_login',
          '********',
          this.getBowerName(info),
        ),
        formattedTime:
          this.$t('Manage_logins_device_time') + this.getDateStr(info.active_time * 1000),
        accountLogout: this.$t('Me_account_logout'),
      }));
    },
  },
  async mounted() {
    this.mySelectLang = this.selectLang =
      window.localStorage.getItem('pweblang') || this.$i18n.locale || 'en_US';

    this.nickname = this.$root.userInfo.nickname;
    this.getLoginList();
    this.getSSOList();
    this.initEmailShow();

    const settingData = (await this.reqGetInfo('/user/me/settings')) || {};
    let { language, language_is_default } = settingData;
    if (language_is_default === 1) {
      //用户自定义
    } else {
      //兜底
      language = '';
    }
    this.selectTransLang = language;
    this.mySelectTransLang = language;
    this.errorMsg = '';

    const languageMap = {
      en_US: 'en',
      es_ES: 'es',
      fr_FR: 'fr',
      de_DE: 'de',
      it_IT: 'it',
      ja_JP: 'ja',
      ko_KR: 'ko',
      pt_PT: 'pt',
      zh_CN: 'zh-cn',
      zh_TW: 'zh-tw',
    };
    const list = await this.reqGetInfo(
      `/others/config/industry?language=${languageMap[this.selectLang] || 'en'}`,
    );

    this.industryOptions = list.map((item) => {
      console.log(item, 'item');
      return {
        label: item[Object.keys(item)[0]],
        value: Object.keys(item)[0],
      };
    });
    console.log(list, settingData, '222222222', this.industryOptions);
    this.settings = {
      is_correct: settingData.is_correct || false,
      industry: settingData.industry || '',
      words: settingData.words || '',
    };
    this.$nextTick(() => {
      this.reqGetInfo(`/config/init?platform=web&version=1.1.1`).then((data) => {
        this.enableSummaryAuto = !!data.enable_auto_language;
        // 初始化的时候如果用户之前有选择Auto,非灰度情况也要把Auto展示出来
        if (this.enableSummaryAuto || this.mySelectTransLang == 'auto') {
          this.transLangList = transLangList;
        }
      });
    });
  },
  watch: {
    settings: {
      handler(n, o) {
        setTimeout(() => {
          this.reqPostInfo('/user/me/settings', n).catch((e) => {
            if (e.status === -1) {
              // 埋点：词数到达上限
              this.$pldAnalytics.reportCustomEvent('custom_terms_limit');
            }
          });
        });
      },
      deep: true,
    },
  },
  beforeDestroy() {
    removeURLParams();
  },
  methods: {
    ...mapActions('sso', ['initAppleSSO', 'initGoogleSSO', 'initMicrosoftSSO']),
    ...mapMutations('auth', ['AUTH_SET_REDDOTSHOW', 'AUTH_SET_LOCALE']),
    initEmailShow() {
      if (this.currentLoginType === 'email') {
        this.isEmailShow = true;
      }
    },
    initSSO() {
      const { ssoRenderList } = this;
      const ssoInitMap = {
        Google: this.initGoogleLogin,
        Apple: this.initAppleLogin,
        // Microsoft: this.initMicrosoftLogin,
      };

      for (const sso of ssoRenderList) {
        if (sso.action === 'Add' && ssoInitMap[sso.name]) {
          ssoInitMap[sso.name].call(this);
        }
      }
    },
    initMicrosoftLogin() {
      this.initMicrosoftSSO();
    },
    async handleMicrosoftCallback() {
      const loginRequest = {
        scopes: ['user.read'],
      };
      this.setSSORenderListItemLoading('Microsoft', true);

      try {
        const response = await this.microsoftModel.loginPopup(loginRequest);
        const payload = {
          id_token: response.idToken,
          sso_type: 'microsoft',
          sso_from: 'web',
        };

        const data = await this.reqPostInfo('/auth/sso-bind', payload);
        this.handleSSOBindResponse(data, 'microsoft');
      } catch (error) {
        console.log('handleMicrosoftCallback', error);
        this.setNoticeConfirm(this.$t('Network_faile'));
      } finally {
        this.setSSORenderListItemLoading('Microsoft', false);
      }
    },
    initAppleLogin() {
      this.initAppleSSO();
      this.initAppleSSOListener();
    },
    initAppleSSOListener() {
      try {
        document.addEventListener('AppleIDSignInOnSuccess', this.handleAppleSuccessCallback);
        document.addEventListener('AppleIDSignInOnFailure', this.handleAppleFailureCallback);
      } catch (error) {
        console.log('apple sso', error);
      }
    },
    async handleAppleSuccessCallback(event) {
      const { id_token } = event.detail.authorization;
      const payload = {
        id_token,
        sso_type: 'apple',
        sso_from: 'web',
      };
      this.setSSORenderListItemLoading('Apple', true);

      try {
        const data = await this.reqPostInfo('/auth/sso-bind', payload);
        this.handleSSOBindResponse(data, 'apple');
      } catch (error) {
        console.log('handleAppleSuccessCallback', error);
        this.setNoticeConfirm(this.$t('Network_faile'));
      } finally {
        this.setSSORenderListItemLoading('Apple', false);
      }
    },
    async handleAppleFailureCallback(event) {
      console.log(event.detail.error);
      this.setSSORenderListItemLoading('Apple', false);
    },
    initGoogleLogin() {
      const { handleGoogleCallback } = this;
      const googleOriginBtnElement = this.$refs.googleOriginBtn[0];
      const googleCoverBtnElement = this.$refs.googleCoverBtn[0];
      this.initGoogleSSO({
        googleOriginBtnElement,
        googleCoverBtnElement,
        googleLoginCallback: handleGoogleCallback,
      });
    },
    async handleGoogleCallback(response) {
      this.setSSORenderListItemLoading('Google', true);
      const payload = {
        sso_from: 'web',
        sso_type: 'google',
        id_token: response.credential,
      };

      try {
        const data = await this.reqPostInfo('/auth/sso-bind', payload);
        this.handleSSOBindResponse(data, 'google');
      } catch (error) {
        console.log('handleGoogleCallback', error);
        this.setNoticeConfirm(this.$t('Network_faile'));
      } finally {
        this.setSSORenderListItemLoading('Google', false);
      }
    },
    async getSSOList() {
      try {
        const data = await this.reqGetInfo('/auth/sso-list');

        this.updateSSOListStatus(data);
        this.$nextTick(() => {
          this.initSSO();
        });
      } catch (error) {
        console.error('getSSOList', error);
      }
    },
    updateSSOListStatus(data) {
      // 处理ssoList的状态
      this.initSSOData();
      let isUnbindAccount = false;

      const ssoActions = {
        microsoft: 'SSO_SET_MICROSOFT_STATUS',
        google: 'SSO_SET_GOOGLE_STATUS',
        apple: 'SSO_SET_APPLE_STATUS',
      };

      for (const sso of data) {
        if (ssoActions[sso.sso_type]) {
          this.ssoList[sso.sso_type].action = 'Remove';
          this.ssoList[sso.sso_type].no += 10;
          this.ssoCount++;
          this.$store.commit(`sso/${ssoActions[sso.sso_type]}`, 'bind');
        }

        if (sso.is_account) {
          isUnbindAccount = true;
        }
      }

      // 如果是非绑定账号匿名登录，隐藏邮箱
      if (!isUnbindAccount) {
        this.isEmailShow = true;
      }

      // 把ssoList的item根据序号大小倒叙筛进ssoRenderList
      this.ssoRenderList = Object.values(this.ssoList).sort((a, b) => b.no - a.no);
    },
    initSSOData() {
      this.ssoList = {
        /* microsoft: {
          type: 'Microsoft',
          name: this.$t('UserCenter_dict_microsoft'),
          action: 'Add',
          icon: 'microsoft',
          no: 3,
          loading: false,
        }, */
        google: {
          type: 'Google',
          name: this.$t('UserCenter_dict_google'),
          action: 'Add',
          icon: 'google',
          no: 2,
          loading: false,
        },
        apple: {
          type: 'Apple',
          name: this.$t('UserCenter_dict_apple'),
          action: 'Add',
          icon: 'apple',
          no: 1,
          loading: false,
        },
      };
      this.ssoCount = 0;
    },
    handleSSOAction(item) {
      const { name, action } = item;

      switch (action) {
        case 'Add':
          this.handleSSOBind(name);
          break;
        case 'Remove':
          this.handleSSOUnbind(name);
          break;
      }
    },
    handleSSOActionTrans(action) {
      return action === 'Add' ? this.$t('UserCenter_dict_add') : this.$t('UserCenter_dict_remove');
    },
    handleSSOUnbind(name) {
      const ssoTypes = {
        Microsoft: 'microsoft',
        Google: 'google',
        Apple: 'apple',
      };
      const ssoStatus = {
        Microsoft: 'SSO_SET_MICROSOFT_STATUS',
        Google: 'SSO_SET_GOOGLE_STATUS',
        Apple: 'SSO_SET_APPLE_STATUS',
      };

      if (ssoTypes[name]) {
        this.handleSSOUnbindRequest(ssoTypes[name]);
        this.$store.commit(`sso/${ssoStatus[name]}`, 'unbind');
      }
    },
    handleSSOUnbindRequest(ssoType) {
      const payload = {
        sso_type: ssoType,
      };
      const type = ssoType.charAt(0).toUpperCase() + ssoType.slice(1);

      this.setSSORenderListItemLoading(type, true);

      this.reqPostInfo('/auth/sso-unbind', payload).then(
        (data) => {
          console.log('success', data);
          this.setSSORenderListItemLoading(type, false);
          this.getSSOList();
        },
        (data) => {
          console.log('fail', data);
          this.setSSORenderListItemLoading(type, false);
        },
      );
    },
    handleSSOBind(name) {
      const ssoTypes = {
        Microsoft: 'microsoft',
        Google: 'google',
        Apple: 'apple',
      };
      const ssoStatus = {
        Microsoft: 'SSO_SET_MICROSOFT_STATUS',
        Google: 'SSO_SET_GOOGLE_STATUS',
        Apple: 'SSO_SET_APPLE_STATUS',
      };

      if (ssoTypes[name]) {
        this.handleSSOBindRequest(ssoTypes[name]);
        this.$store.commit(`sso/${ssoStatus[name]}`, 'bind');
      }
    },
    handleSSOBindRequest(ssoType) {
      const payload = {
        sso_type: ssoType,
        sso_from: 'web',
      };

      this.reqPostInfo('/auth/sso-bind', payload).then(
        (data) => {
          console.log('success', data);
          this.doLogout(true);
        },
        (data) => {
          console.log('fail', data);
        },
      );
    },
    getLoginList() {
      this.reqGetInfo('/auth/access-token-list').then((data) => {
        let app = 0,
          web = 0;
        data
          .sort((a, b) => {
            return b.active_time - a.active_time;
          })
          .forEach((item) => {
            if (item.client_id == 'web') {
              web++;
              item.count = web;
            } else {
              app++;
              item.count = app;
            }
          });
        // this.loginList = data.slice(0, 10);
        this.loginList = data;
        parseHash('integration', () => {
          this.scrollIntoViewByRef('integration');
        });
      });
    },
    // 过滤 user_agent
    // getLoginList() {
    //   this.reqGetInfo('/auth/access-token-list').then((data) => {
    //     let app = 0, web = 0;
    //     console.log('data$$$$$:', data);
    //     let filteredData = data.reduce((acc, item) => {
    //         acc[item.user_agent] = item;
    //         return acc;
    //     }, {});
    //     let result = Object.values(filteredData);
    //     result.forEach((item) => {
    //       if (item.client_id == 'web') {
    //         web++;
    //         item.count = web;
    //       } else {
    //         app++;
    //         item.count = app;
    //       }
    //     });
    //     console.log('result$$$$$:', result);
    //     let ua = result.map(item => item.user_agent)
    //     console.log('ua$$$$$:', ua);
    //     this.loginList = result;
    //   });
    // },
    getBowerName({ client_id, user_agent_show }) {
      if (client_id == 'ios') {
        return 'iPhone';
      } else if (client_id == 'android') {
        return 'Android';
      } else {
        return user_agent_show;
      }
    },
    saveNickName() {
      if (this.nickname != this.$root.userInfo.nickname) {
        let name = this.nickname.trim();
        if (name == '') {
          this.errorMsg = this.$t('Me_account_name_noname');
          return false;
        }
        if (name.length > 32) {
          this.errorMsg = this.$t('Me_account_name_toolong');
          return false;
        }
        this.isLoading = true;
        this.errorMsg = '';
        this.reqPatchInfo('/user/me', { nickname: this.nickname }).then((data) => {
          this.setGALogEvent('web_edit_username');
          this.isLoading = false;
          this.$root.userInfo.nickname = this.nickname;
        });
      }
    },
    cancelNickName() {
      if (this.nickname != this.$root.userInfo.nickname) {
        this.nickname = this.$root.userInfo.nickname;
      }
      this.errorMsg = '';
    },
    updateAvater(e) {
      let files = e.target.files,
        limitFileSize = 5,
        hasError = false,
        formData = new FormData();
      for (let i = 0; i < files.length; i++) {
        let file = files[i],
          filename = file.name;
        let filefix = filename.substring(filename.lastIndexOf('.') + 1);
        filefix = filefix.toLowerCase();
        if (file.size > 1024 * 1024 * limitFileSize) {
          e.target.value = '';
          this.showBubbleAlert = true;
          this.bubbleAlertMessage = this.$t('upload_user_avatar_size_limit');
          hasError = true;
          break;
        }
        formData.append('file', file);
      }
      e.target.value = '';
      if (hasError) {
        return false;
      }

      this.isLoading = true;
      this.reqPostInfo('/user/me/avatar', formData)
        .then((data) => {
          this.setGALogEvent('web_edit_profilepicture');
          if (data.data_user) {
            this.$root.userInfo.avatar = data.data_user.avatar;
          }
          this.isLoading = false;
        })
        .catch((error) => {
          console.log(error, 'error');
          if (error.status === -2) {
            this.showBubbleAlert = true;
            this.bubbleAlertMessage = this.$t('upload_user_avatar_size_limit');
            return;
          }
          if (error.status === -1) {
            this.showBubbleAlert = true;
            this.bubbleAlertMessage = this.$t('upload_user_avatar_type_limit');
          }
        });
    },
    logoutRemove() {
      this.setGALogEvent('web_logout');
      localStorage.removeItem('tokenstr');
      Cookies.remove('noteemail');
      Cookies.remove('notepwd');
      window.location.reload();
    },
    doLogout(status = false) {
      let myOut = () => {
        this.logoutRemove();

        // this.isLoading = true;
        // this.reqPostInfo('/auth/access-token-logout').then((data) => {
        //   this.isLoading = false;
        //   this.logoutRemove();
        // });
      };
      if (status) {
        this.setConfirm({
          title: this.$t('Me_account_logout_title'),
          msg: this.getTranslateStr('Me_account_logout_content'),
          okname: this.$t('Me_account_logout_button_right'),
          ok: () => {
            myOut();
          },
        });
      } else {
        myOut();
      }
    },
    changePwd() {
      this.$refs.changePwd.handlerVisible(true);
    },
    removeToken(info) {
      let tokenName = `${info.client_id == 'web' ? 'WEB' : 'APP'} ${info.count}`;
      this.setConfirm({
        title: this.$t('Me_account_logout_title'),
        msg: this.getTranslateStr('Manage_logins_device_logout_content', '********', tokenName),
        okname: this.$t('Manage_logins_device_logout_button_right'),
        cancelname: this.$t('Manage_logins_device_logout_button_left'),
        ok: () => {
          this.isLoading = true;
          this.reqPostInfo('/auth/access-token-remove', {
            access_token_time: info.auth_time,
          }).then((data) => {
            this.isLoading = false;
            this.showSuccessMsg(
              this.getTranslateStr('Manage_logins_device_logedout', '%s', tokenName),
            );
            if (info.is_current) {
              this.logoutRemove();
            } else {
              this.setGALogEvent('web_logout_other');
              this.getLoginList();
            }
          });
        },
      });
    },
    setFormError(field, message) {
      this.formError = {
        [field]: {
          status: true,
          msg: message,
        },
      };
    },
    setSSORenderListItemLoading(name, status) {
      this.ssoRenderList.forEach((item) => {
        if (item.name === name) {
          item.loading = status;
        }
      });
    },
    getBindedNotice(data) {
      const { status } = data;

      switch (status) {
        case -1:
          this.setNoticeConfirm(this.$t('Network_sso_bind_err_1'));
          break;
        case -4:
          this.setNoticeConfirm(this.$t('Network_sso_bind_err_2'));
          break;
        case -10:
          this.setNoticeConfirm(this.$t('Network_sso_bind_err_3'));
          break;
        default:
          this.setNoticeConfirm(this.$t('Network_faile'));
      }
    },
    setNoticeConfirm(data) {
      this.setConfirm({
        title: 'Notice',
        msg: data,
        okname: 'Got it',
        cancelname: '',
        okbtnStyle: {
          'background-color': '#1F1F1F',
          color: '#FFFFFF',
        },
        cancelbtnStyle: {
          display: 'none',
        },
        btnBoxStyle: {
          'padding-bottom': '0px',
        },
        ok: () => {
          return;
        },
      });
    },
    getImagePath(icon) {
      // 直接返回静态路径，避免动态require导致的Promise问题
      return `/static/${icon}.png`;
    },
    handleSSOBindResponse(data, ssoType) {
      const { status } = data;

      if (status === 0) {
        this.$store.commit('auth/AUTH_SET_CURRENT_LOGIN_TYPE', ssoType);
        this.getSSOList();
      } else {
        this.getBindedNotice(data);
      }
    },
    async saveLang(save) {
      if (this.mySelectLang != this.selectLang) {
        if (save) {
          // 使用 setLocale 切换语言
          await setLocale(this.selectLang);
          this.setGALogEvent('web_me_language');
          // window.localStorage.setItem('pweblang', this.selectLang);
          this.mySelectLang = this.selectLang;
          // this.$i18n.locale = this.selectLang;
          // document.documentElement.setAttribute('lang', this.selectLang);
          Cookies.remove('plaudfamoustxt');
          window.location.reload();
        } else {
          this.selectLang = this.mySelectLang;
        }
      }
    },
    saveTransLang(save) {
      if (this.mySelectTransLang != this.selectTransLang) {
        if (save) {
          this.reqPostInfo('/user/me/settings', {
            language: this.selectTransLang,
            language_is_default: 1,
          });
          this.mySelectTransLang = this.selectTransLang;
          changeZELang(this.selectTransLang);
        } else {
          this.selectTransLang = this.mySelectTransLang;
        }
      }
    },
    async skipToFrill() {
      this.setGALogEvent('web_shareideas_click');
      // let isJP = this.locale === 'ja_JP'
      let frillURL = FRILL_DATA.ideas;
      // let url = isJP? FRILL_DATA.line : getFrillUrl(frillURL, this.frillSsoToken)
      let url = getFrillUrl(frillURL, this.frillSsoToken);
      // if (this.redDotShow) {
      const data = {
        name: this.userInfo.email,
        data: { frill: false },
      };
      this.AUTH_SET_REDDOTSHOW(data);
      // await this.updateRedDotShow()
      // }
      window.open(url);
    },
    scrollIntoViewByRef(refName) {
      this.$nextTick(() => {
        const element = this.$refs[refName];
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      });
    },
    handleRetry() {
      // 获取文件输入框的 DOM 元素
      const fileInput = this.$refs.fileInput;

      if (fileInput) {
        // 触发文件输入框的点击事件
        fileInput.click();
        // 关闭气泡提示
        this.showBubbleAlert = false;
      }
    },
    handleBubbleClose() {
      this.showBubbleAlert = false;
    },
    onEdit(type) {
      this.showSettingDialog = type;
      if (type) {
        // 埋点：打开自定义专业词汇弹窗
        this.$pldAnalytics.reportCustomEvent('custom_terms_view');
      }
    },
    onWordsChange(data, str) {
      this.settings.words = data;
      this.text = str;
    },
    onShowAddDialog(data) {
      this.showAddDialog = data;
    },
    onSwitch() {
      const membership_type = this.$root.userStateInfo.membership_type;
      if (!this.settings.is_correct) {
        if (
          membership_type != 'pro' &&
          membership_type != 'unlimited' &&
          membership_type != 'backer'
        ) {
          this.$parent.$parent.handleMemberCheck(-1);
          return;
        }
        if (this.settings.words.length === 0 && !this.settings.industry) {
          this.setConfirm({
            title: this.$t('WEB_my_industry_glossary'),
            backCancel: true,
            cancelbtnStyle: { 'max-width': '200px' },
            msgStyle: { 'padding-left': '0px', 'padding-right': '0px' },
            msg: this.$t('WEB_my_industry_glossary_no_data_tip'),
            cancelname: this.$t('Web_filedetail_submit_insufficienttime_button_got'),
          });
          return;
        }
        this.settings.is_correct = !this.settings.is_correct;
      } else {
        this.settings.is_correct = !this.settings.is_correct;
      }

      // 埋点：专业词库开关
      this.$pldAnalytics.reportCustomEvent('industry_glossary_toggle', {
        status: this.settings.is_correct ? 'on' : 'off',
      });
    },
    onIndustryChange(value) {
      console.log('onIndustryChange', value);
      // 埋点：专业词库选择
      this.$pldAnalytics.reportCustomEvent('industry_glossary_preference', {
        industry: value ? value : 'cancel',
      });
    },
  },
};
</script>
<style scoped lang="scss">
.subtitle {
  color: #858c9b;
  font-size: 12px;
  line-height: 18px;
}
.iptName {
  width: 448px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  box-sizing: border-box;
  padding: 0px 16px;
  font-size: 14px;
  &.disable {
    background: #f7f8fa;
    border-color: #f7f8fa;
    color: #858c9b;
  }
  &:not(.disable):focus {
    border: 1px solid #1f1f1f;
  }
}
.emailManagerWrap {
  width: 448px;
  background-color: #f9fafb;
  padding-left: 16px;
  font-size: 15px;
  font-weight: 500;
  color: #1f1f1f;
  .emailManager {
    height: 50px;
    i {
      margin-right: 7px;
    }
    .emailWrap {
      padding-right: 14px;
      font-size: 13px;
      font-weight: 400;
      color: #858c9b;
    }
  }
  .passwordManager {
    height: 50px;
    i {
      margin-right: 7px;
    }
  }
}
.ssoManagerWrap {
  width: 448px;
  height: 100px;
  position: relative;
  background-color: #f9fafb;
  padding-left: 16px;
  font-size: 15px;
  font-weight: 500;
  color: #1f1f1f;
  .ssoManager {
    height: 50px;
    position: relative;
    img {
      width: 20px;
      height: 20px;
      margin-right: 7px;
    }
    .actionWrap {
      padding-right: 14px;
      font-size: 13px;
      font-weight: 400;
      color: #858c9b;
    }
  }
}
.ssoManagerCoverWrap {
  width: 150px;
  height: 100px;
  position: absolute;
  top: 0;
  right: -150px;
  z-index: 100;
}
.separatorLine {
  width: 100%;
  height: 1px;
  visibility: visible;
  border-bottom: 1px solid rgba(227, 226, 224, 0.5);
}
.flexSpaceBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.lightFont {
  font-weight: 400;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    'SF Pro Text',
    Helvetica Neue,
    HelveticaNeue,
    Helvetica,
    Arial,
    sans-serif;
  &.blue {
    color: #007aff;
  }
  &.red {
    color: #f14349;
  }
  &.gray {
    color: #858c9b;
  }
}
.ssoCoverBtn {
  width: 76px;
  height: 40px;
  position: absolute;
  top: 5px;
  right: 0;
  opacity: 0;
  .microsoftOriginBtn,
  .appleOriginBtn,
  .googleOriginBtn {
    width: 100% !important;
    height: 100% !important;
  }
}
.ssoListLoading {
  position: relative;
  .el-icon-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -8px;
    margin-left: -8px;
    z-index: 100;
  }
}
.ssoLoadingMask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.4);
  z-index: 10;
}
.formError {
  width: 448px;
  text-align: left;
  min-height: 20px;
  max-height: 20px;
  opacity: 0;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}
.formError.visible {
  max-height: 80px;
  opacity: 1;
}
.skeleton {
  width: 104%;
  height: 50px;
  position: relative;
  opacity: 0.3;
  left: -16px;
  height: auto;
  overflow-y: auto;
  box-sizing: border-box;
  display: block;
  border-radius: 4px;
}
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
.skeleton-part {
  width: 100%;
  background-color: #eee;
}
.skeleton-part-common {
  width: 100%;
  height: 50px;
  margin-bottom: 2px;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.06) 25%,
    rgba(0, 0, 0, 0.15) 37%,
    rgba(0, 0, 0, 0.06) 63%
  );
  background-size: 400% 100%;
  border-radius: 4px;
  animation-name: skeleton-loading;
  animation-duration: 1.4s;
  animation-timing-function: ease;
  animation-iteration-count: infinite;
}
.integration-list {
  margin-top: 24px;
  display: flex;
  .integration-item {
    padding-right: 0;
    position: relative;
  }
}
.share-box {
  display: flex;
  position: relative;
  .share-btn {
    position: relative;
    min-width: 143px;
    padding: 9px 20px;
    margin-bottom: 16px;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    border-radius: 6px;
    background: #ffff;
    border: 1px solid #e4e7ec;
    cursor: pointer;
    &:hover {
      background: rgba(228, 231, 236, 0.65);
    }
    &:active {
      background: #e4e7ec;
    }
  }
  .circle-red {
    position: absolute;
    top: 8px;
    right: 8px;
  }
}
.transDesc {
  margin: 8px 0px;
  color: #858c9b;
  font-size: 12px;
  font-weight: 400;
}
</style>
<style lang="scss">
.vocabulary {
  .el-select {
    padding-right: 8px;
  }
  .el-input__inner {
    border: 0 !important;
    background: transparent !important;
    padding-left: 200px;
    text-align: right;
    color: #1f1f1f;
    font-size: 12px;
    text-overflow: ellipsis;
  }
}
.vocabulary-select {
  .popper__arrow {
    display: none;
  }
}

.el-select {
  input {
    text-overflow: ellipsis;
  }
}

.el-select-dropdown__item {
  color: #475467 !important;
}
.el-select-dropdown__item.selected {
  color: #1f1f1f !important;
  background: #f4f4f6 !important;
}

.el-select {
  .el-input__inner {
    padding-right: 36px;
    &::placeholder {
      color: #858c9b;
    }
  }

  .el-input__icon::before {
    color: #667085;
    font-weight: 800;
    font-size: 18px;
  }
}
</style>
