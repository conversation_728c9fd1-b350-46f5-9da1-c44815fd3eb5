<template>
  <MyDialog
    width="400px"
    class="z-[99999999] fixed"
    :visible="true"
    @close="onClose(false)"
    :modal="true"
  >
    <template v-slot:content>
      <div class="z-[99999999] fixed">
        <h2 class="mt-[-28px] mb-2 text-left text-[20px] font-medium text-[#1F1F1F] height-[30px]">
          {{ $t('WEB_add_term') }}
        </h2>
      </div>
      <div class="input-field pt-6 h-[104px]">
        <el-input
          :placeholder="$t('WEB_custom_terms_placeholder')"
          v-model="value"
          maxlength="50"
        ></el-input>
        <div :class="{ ['hidden']: !showTip }" class="text-red-600 text-[12px] mt-2 pl-2">
          {{ $t('WEB_repeat_entry') }}
        </div>
      </div>

      <div class="flex justify-end">
        <button
          @click="onClose(false)"
          class="rounded-lg bg-[#E4E7EC] font-semibold text-[14px] px-8 py-2 text-[#1F1F1F] mr-2"
        >
          {{ $t('BindAccount_dict_later') }}
        </button>
        <button
          @click="onAdd()"
          :disabled="!status"
          :class="{ ['opacity-40']: !status }"
          class="rounded-lg bg-[#2A2A2A] font-semibold text-[14px] px-8 py-2 text-white hover:bg-[#333333]"
        >
          {{ $t('save') }}
        </button>
      </div>

      <!-- Description -->
    </template>
    <!-- Bottom Button -->
    <!-- <template v-slot:footer>
      
    </template> -->
  </MyDialog>
</template>

<script>
import MyDialog from '../../common/MyDialog2.vue';
// import { ElMessage } from 'element-plus';
export default {
  name: 'SettingDialog',
  components: { MyDialog },
  data() {
    return {
      value: '',
      showTip: false,
    };
  },
  props: {
    data: {
      type: Array,
      default: [],
    },
    onClose: {
      type: Function,
      default: () => {},
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  mounted() {
    // this.list = this.classifyByUppercase(this.data);
    // console.log(this.list, '222');
  },
  computed: {
    status() {
      return !!this.value;
    },
  },

  methods: {
    onAdd() {
      if (!this.value) {
        this.onClose(false);
        return;
      }
      if (this.data.includes(this.value)) {
        this.showTip = true;
        setTimeout(() => {
          this.showTip = false;
        }, 3000);
        return;
      }
      this.data.push(this.value);
      this.onChange(this.data.join(','), this.value);
      // 埋点：添加自定义词汇
      this.$pldAnalytics.reportCustomEvent('custom_terms_operate', {
        operate: 'add'
      });
      this.onClose(false);
    },
  },
};
</script>

<style lang="scss">
.input-field {
  .el-input__inner {
    border: 1px solid #e4e7ec;
  }
}
</style>
