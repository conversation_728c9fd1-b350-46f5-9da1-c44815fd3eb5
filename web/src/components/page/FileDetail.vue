<template>
  <div class="fileDetailBox">
    <div class="fileTop">
      <div class="leftDesc">
        <div v-if="getStrLength(info.filename) <= 100">
          {{ info.filename }}
        </div>
        <template v-else>
          <el-tooltip effect="dark" :content="info.filename" placement="bottom">
            <div>{{ getNameEllipse(info.filename, 100) }}</div>
          </el-tooltip>
        </template>
      </div>
      <div class="rightIcons" v-if="isTrashFile">
        <el-tooltip effect="dark" :content="$t('Web_sidebar_trash_restore')" placement="bottom">
          <div
            class="iconfont myIcon icon-recover"
            style="font-size: 24px"
            @click="$parent.recoverFile(true)"
          ></div>
        </el-tooltip>
        <el-tooltip effect="dark" :content="$t('Web_sidebar_trash_delete')" placement="bottom">
          <div
            class="iconfont icon-delete myIcon"
            style="font-size: 24px"
            @click="$parent.emptyFile(true)"
          ></div>
        </el-tooltip>
      </div>
      <FileShare @doMenu="doMenu" v-else />
    </div>
    <div class="fileContent empty" v-if="isEmpty">
      <img src="/static/notrans.png" alt="" />
      <div class="commonBtn" @click="doMenu()" style="width: 200px">
        <div class="cseTxt flex-between">
          <div class="iconfont icon-generate" style="margin-right: 5px"></div>
          {{ $t('Filedetail_nottranscribed_transcribe_button') }}
        </div>
      </div>
    </div>
    <div class="fileContent has" v-else>
      <div
        class="leftCont relative"
        ref="leftCont"
        :style="leftStyle"
        :class="{ whole: !showTrans }"
      >
        <div class="leftContFlow" @mouseover="showMdEdit = true" @mouseleave="showMdEdit = false">
          <AmLoad
            :finshed="fileTsFinshed"
            type="summary"
            :summaryType="summaryLoadType"
            v-if="isLoading"
          />
          <div class="summaryFail" v-else-if="summaryFail < 0">
            <img src="/static/fail.png" alt="" />
            <div class="desc" v-html="getErrorMsg(summaryFail)"></div>
          </div>
          <template v-else>
            <div class="titleBoxWrap flex-center" v-if="leftShowStatus">
              <div class="titleBox">
                <div class="flex-center">
                  <div
                    class="titleBtnBox"
                    style="margin-right: 25px"
                    @click="switchSummary(0)"
                    :class="{ active: summaryFlag === 0 }"
                  >
                    <div class="title">{{ $t('Filedetail_nottranscribed_tabtwo') }}</div>
                  </div>
                  <div
                    class="titleBtnBox"
                    @click="switchSummary(1)"
                    :class="{ active: summaryFlag === 1 }"
                  >
                    <div class="title">{{ $t('Filedetail_nottranscribed_tabthree') }}</div>
                  </div>
                  <div
                    class="titleBtnBox trans"
                    v-if="!showTrans && !isLoading"
                    @click="changeShowTrans(true, true)"
                  >
                    <div class="title">
                      {{ $t('Filedetail_nottranscribed_tabone') }}
                    </div>
                  </div>
                </div>
                <!-- <div class="editBox" v-if="!isTrashFile && summaryFlag === 0">
                  <div class="editTranMenu" v-if="mdEdit">
                    <div class="eidtMenu cancel" @click.stop="cancalEditMd(1, true)">
                      {{ btnTxt.cancel }}
                    </div>
                    <div class="eidtMenu save" @click.stop="saveEditMd(true)">
                      {{ btnTxt.save }}
                    </div>
                  </div>
                  <div class="editTranMenu" v-else-if="showMdEdit">
                    <div class="eidtMenu iconfont icon-edit1" @click.stop="doMdClick()"></div>
                  </div>
                </div> -->
              </div>
            </div>
            <PerfectScrollbar
              class="moduleContBox ps relative"
              ref="summaryScrollBox"
              style="flex: 1"
              @scroll="summaryLoad(false)"
            >
              <div class="moduleCont" id="moduleCont" :class="{ mind: summaryFlag === 1 }">
                <div v-show="summaryFlag === 0 && showEditor">
                  <!-- <div
                    style="
                      font-size: 36px;
                      color: #1f1f1f;
                      line-height: 44px;
                      font-weight: bold;
                      /* margin-bottom: 16px; */
                      word-break: break-all;
                    "
                  >
                    {{ info.filename }}
                  </div> -->
                  <!-- <textarea
                    id="name-edit"
                    :value="info.filename"
                    @input="onNameEdit"
                    style="
                      font-size: 36px;
                      color: #1f1f1f;
                      line-height: 44px;
                      font-weight: 700;
                      font-family: inherit;
                      margin-bottom: 16px;
                      word-break: break-all;
                      width: 100%;
                      border: none;
                      outline: none;
                      resize: none;
                      overflow: hidden;
                      box-sizing: border-box;
                      min-height: 47px;
                      word-break: break-all;
                    "
                    :maxlength="200"
                  /> -->
                  <Vue3Wrapper
                    v-if="showEditor"
                    :onUpdate="onUpdate"
                    :onReady="onReady"
                    :defaultValue="defaultValue"
                    :name="info.filename"
                    :onNameChange="onNameChange"
                  />

                  <!-- <MyMdEditor
                    :class="{ hidden: isLoading }"
                    v-model="summaryAllData"
                    :editFlag="mdEdit"
                  />
                   -->
                  <div class="rateScore mt0" v-if="!beforeLoading">
                    <div
                      class="scorePop"
                      v-if="summaryRateTip && hasSummaryRateTip && showSummaryTip"
                    >
                      {{ $t('Score_dialog_summary_tip') }}
                    </div>
                    <div class="likeBtns" :class="['active_' + rateInfo.summary]">
                      <div class="btx good" @click="sendRate(1, 1)">
                        <div
                          class="iconfont"
                          :class="[rateInfo.summary === 1 ? 'icon-a-thumbfill' : 'icon-thumb']"
                        ></div>
                        {{ $t('Score_dialog_btn_like') }}
                      </div>
                      <div class="btx bad" @click="sendRate(1, 0)">
                        <div
                          class="iconfont"
                          :class="[
                            rateInfo.summary === 0 ? 'icon-a-thumbupfill' : 'icon-a-thumbup',
                          ]"
                        ></div>
                        {{ $t('Score_dialog_btn_dislike') }}
                      </div>
                    </div>
                  </div>
                </div>
                <div style="height: 100%; width: 100%" v-show="summaryFlag === 1">
                  <svg class="mindmapBox" :class="{ hidden: isLoading }" id="mapBox"></svg>
                </div>
              </div>
            </PerfectScrollbar>
          </template>
        </div>
      </div>
      <div
        class="rightCont relative"
        ref="rightCont"
        :style="rightStyle"
        :class="{ whole: !leftShowStatus, dragPad: leftMinStatus }"
        v-if="showTrans"
      >
        <div
          class="dragBox"
          ref="dragBox"
          :class="{ move: moveInfo.status }"
          @mousedown.stop="doMoveStart($event)"
        >
          <div class="line"></div>
          <div class="bar">
            <div class="iconfont icon-right"></div>
            <div class="iconfont icon-next"></div>
          </div>
        </div>
        <div class="titleBox flex-between">
          <div class="title flex-center">
            {{ $t('Filedetail_nottranscribed_tabone') }}
            <div
              class="speakerBtnBox"
              @click="editBatchSpeaker()"
              v-if="hasSpeaker && loadFlag != 0"
            >
              <div class="iconfont icon-speaker"></div>
              {{ $t('Name_speaker_dialog_title') }}
            </div>
          </div>
          <div
            class="iconfont icon-Tab_disclosure iconHide"
            @click="changeShowTrans(false, true)"
            v-if="loadFlag != 0"
          ></div>
        </div>

        <AmLoad :finshed="fileTsFinshed" v-if="loadFlag === 0" style="margin-top: -50px" />
        <div class="summaryFail" v-else-if="transFail < 0">
          <img src="/static/fail.png" alt="" />
          <div class="desc" v-html="getErrorMsg(transFail)"></div>
        </div>
        <template v-else>
          <DynamicScroller
            class="moduleContBox relative pb120 custom-scrollbar"
            :items="transInfo"
            :min-item-size="30"
            :prerender="10"
            :key-field="'start_time'"
            @mouseover="hoverIndex = 1"
            @mouseout="hoverIndex = -1"
            v-slot="{ item, index, active }"
          >
            <DynamicScrollerItem
              :item="item"
              :active="active"
              :size-dependencies="[item.content]"
              :data-index="index"
            >
              <div :class="{ hidden: loadFlag === 0 }">
                <div
                  class="transItem"
                  :class="{ active: playIndex === index }"
                  @click="changePlayTime(index)"
                  @mouseenter="hoverSpeakerName(index, item)"
                >
                  <div class="transTitle">
                    <div class="timeInfo" :class="['tran_time_' + index]">
                      <div class="circle" v-if="!item.speaker"></div>
                      <div class="speaker" :style="{ '--color': speakerMap[item.speaker] }" v-else>
                        <div class="mySign"></div>
                        <el-popover
                          v-if="
                            popSpeakerIndex == index ||
                            speakerTranInfo.start_time == item.start_time
                          "
                          popper-class="elPopoverClass"
                          :value="!!popSpeakerTime['trans_' + item.start_time]"
                          @show="editSpeakerName(item)"
                          placement="bottom-start"
                          width="320"
                          :offset="-10"
                          :visible-arrow="false"
                          trigger="click"
                        >
                          <div class="singleModifySpeaker">
                            <input
                              type="text"
                              class="nameIpt"
                              v-model="speakerName"
                              :placeholder="speakerHolder"
                              @keydown.enter="saveSpeakerName()"
                            />
                            <div class="disTitle">{{ $t('Edit_speaker_dialog_recent') }}</div>
                            <ul class="nameUl">
                              <li v-for="name in useSpeakerList()" @click.stop="speakerName = name">
                                {{ name }}
                              </li>
                              <li class="emptyTxt" v-if="useSpeakerList().length === 0">
                                {{ $t('Edit_speaker_dialog_recent_empty') }}
                              </li>
                            </ul>
                            <div class="flex-middle lsBox">
                              <div
                                class="lsCheck"
                                :class="{ active: onlyCurrentSpeaker }"
                                @click="onlyCurrentSpeaker = true"
                              ></div>
                              {{ $t('Edit_speaker_dialog_apply_only') }}
                            </div>
                            <div class="flex-middle lsBox">
                              <div
                                class="lsCheck"
                                :class="{ active: !onlyCurrentSpeaker }"
                                @click="onlyCurrentSpeaker = false"
                              ></div>
                              {{ $t('Edit_speaker_dialog_apply_all') }}
                            </div>
                            <div class="desc">
                              {{ $t('Edit_speaker_dialog_apply_all_explain') }}
                            </div>
                            <div
                              class="commonBtn"
                              style="margin-top: 10px"
                              @click="saveSpeakerName()"
                            >
                              {{ $t('Edit_speaker_dialog_save') }}
                            </div>
                          </div>
                          <template v-slot:reference>
                            <div class="sname" @click="clickSpeakerPop(item)" v-vstop>
                              {{ item.speaker }}
                            </div>
                          </template>
                        </el-popover>
                        <div class="sname" v-vstop v-else>{{ item.speaker }}</div>
                      </div>
                      <div class="time">{{ getTimeStr(item.start_time, true) }}</div>
                      <div class="iconfont icon-play"></div>
                    </div>
                    <template v-if="!isTrashFile">
                      <div
                        class="editTranMenu menuHidden"
                        v-if="editIndex == -1 || editIndex != index"
                      >
                        <div
                          class="eidtMenu iconfont icon-content_copy"
                          @click.stop="doTransEvt('copy', index, true)"
                        ></div>
                        <div
                          class="eidtMenu iconfont icon-edit1"
                          @click.stop="doTransEvt('edit', index, true)"
                        ></div>
                      </div>
                      <div class="editTranMenu editStatus" v-else-if="editIndex == index">
                        <div
                          class="eidtMenu cancel"
                          @click.stop="doTransEvt('cancel', index, true)"
                        >
                          {{ btnTxt.cancel }}
                        </div>
                        <div class="eidtMenu save" @click.stop="doTransEvt('save', index, true)">
                          {{ btnTxt.save }}
                        </div>
                      </div>
                    </template>
                  </div>
                  <div class="cont">
                    <el-input
                      type="textarea"
                      v-vstop
                      autosize
                      placeholder="input..."
                      v-model="editValue"
                      class="ipt"
                      v-if="editIndex === index"
                    ></el-input>
                    <template v-else>{{ item.content }}</template>
                  </div>
                </div>
              </div>
            </DynamicScrollerItem>
            <div
              v-if="!beforeLoading && index == transInfo.length - 1"
              class="rateScore trans width-default flex-center pl0 mt30"
            >
              <div class="scorePop" v-if="transRateTip && hasTransRateTip && showTransTip">
                {{ $t('Score_dialog_transcription_tip') }}
              </div>
              <div class="likeBtns" :class="['active_' + rateInfo.transcription]">
                <div class="btx good" @click="sendRate(0, 1)">
                  <div
                    class="iconfont"
                    :class="[rateInfo.transcription === 1 ? 'icon-a-thumbfill' : 'icon-thumb']"
                  ></div>
                  {{ $t('Score_dialog_btn_like') }}
                </div>
                <div class="btx bad mr0" @click="sendRate(0, 0)">
                  <div
                    class="iconfont"
                    :class="[
                      rateInfo.transcription === 0 ? 'icon-a-thumbupfill' : 'icon-a-thumbup',
                    ]"
                  ></div>
                  {{ $t('Score_dialog_btn_dislike') }}
                </div>
              </div>
            </div>
          </DynamicScroller>
        </template>
      </div>
    </div>
    <TranSummary ref="TranSummary" @retran="reTranInfo" />
    <Loading :status="beforeLoading" />
    <div class="audioWrap" :class="{ disable: !hasLoad }">
      <div class="content">
        <template v-if="hasLoad">
          <div class="btn iconfont icon-forward15s" @click="changeTime(-15)"></div>
          <div
            class="playBtn iconfont"
            :class="[playStatus ? 'icon-Icon_pause' : 'play icon-Icon_play']"
            @click="audioPlay()"
          ></div>
          <div class="btn iconfont icon-playback15s" @click="changeTime(15)"></div>
        </template>
        <template v-else>
          <div class="btn iconfont icon-forward15s"></div>
          <div
            class="playBtn iconfont play icon-Icon_play"
            @click="getWave()"
            v-if="!loadFileStatus"
          ></div>
          <div class="playBtn" v-else>
            <AmLoad type="loading" style="width: 80%; height: 80%" />
          </div>
          <div class="btn iconfont icon-playback15s"></div>
        </template>
        <div class="a_time">{{ getTimeStr(curTime, false, false, true) }}</div>
        <div class="audioBox wavLine" @click.stop="audioCk" :class="{ none: !hasLoad }">
          <div class="noneLine" v-if="!hasLoad"></div>
        </div>
        <div class="a_time" style="margin-right: 0px">
          {{ getTimeStr(totalTime - curTime, false, false, true) }}
        </div>
        <el-popover
          popper-class="elPopoverClass"
          placement="bottom-end"
          width="326"
          :disabled="!hasLoad"
          :visible-arrow="false"
          trigger="click"
        >
          <div class="speedBox">
            <div class="title">{{ $t('Filedetail_speed_subtitle') }}</div>
            <div class="speedBarBox">
              <div class="bar">
                <div class="pro" :style="{ width: posWidth }"></div>
                <div class="circle" :style="{ left: `calc(${posWidth} - 6px)` }"></div>
                <div
                  class="barHand"
                  :class="['hand' + i, i < speedIdx + 1 ? 'hidden' : '']"
                  v-for="i in 4"
                  @click="changeSpeed(i - 1)"
                ></div>
              </div>
            </div>
            <div class="barTxt">
              <div
                class="txt"
                v-for="(s, index) in speeds"
                @click="changeSpeed(index)"
                :class="{ active: index <= speedIdx }"
              >
                {{ s }}x
              </div>
            </div>
            <!-- <div class="line"></div> -->
            <!-- <div class="skipBox"> -->
            <!-- <div class="left">{{$t('Filedetail_speed_button')}}</div> -->
            <!-- <el-switch v-model="skipStatus" active-color="#1F1F1F" inactive-color="#1F1F1F"></el-switch> -->
            <!-- </div> -->
          </div>
          <template v-slot:reference>
            <div
              class="iconfont speedBtn"
              :class="
                [
                  'icon-a-playspeed05x',
                  'icon-playspeed1x',
                  'icon-a-playspeed15x',
                  'icon-playspeed2x',
                ][speedIdx]
              "
            ></div>
          </template>
        </el-popover>
      </div>
    </div>
    <NameSpeakers ref="NameSpeakers" />
    <DoScore ref="DoScore" />
    <UserUpgrade ref="UserUpgrade" @close="showUpgradeModel(false)" />
    <TranSummary
      ref="TranSummary"
      @retran="reTranInfo"
      @preview="handlePreviewTemplate"
      @createTemplate="handleCreateTemplate"
    />
  </div>
</template>

<script>
import WaveMixins from '@/util/WaveMixins';
import TransMove from '@/util/TransMove';
import FileRate from '@/util/FileRate';
import FileShare from './common/FileShare.vue';
import TranSummary from './common/TranSummary';
// import MyMdEditor from '../common/MyMdEditor';
import { PerfectScrollbar } from 'vue3-perfect-scrollbar';
import 'vue3-perfect-scrollbar/style.css';
import 'jsmind/style/jsmind.css';
import '@/styles/jsmind.scss';
import plaudMind from '@/util/plaudMind';
import { autoResize } from '@/util/dom';

import AmLoad from '../common/AmLoad.vue';
import NameSpeakers from './common/NameSpeakers.vue';
import DoScore from './common/DoScore';
import { markdownSpeakerEditFormat, mindFormat } from '@/common/markdown';
import Vue3Wrapper from '../common/vue3-wrapper';
// import emitter from '../../util/event-bus';
import UserUpgrade from './common/membertip/UserUpgrade.vue';

export default {
  name: 'FileDetail',
  mixins: [WaveMixins, TransMove, FileRate],
  components: {
    FileShare,
    TranSummary,
    // MyMdEditor,
    PerfectScrollbar,
    AmLoad,
    NameSpeakers,
    DoScore,
    Vue3Wrapper,
    UserUpgrade,
  },
  props: {
    fileInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      showEditor: true,
      defaultValue: '',
      // emitter,
      editorReady: false,
      postId: new Date().getTime(),
      info: this.fileInfo,
      summaryFlag: 0,
      showTrans: false,
      summaryAllData: '',
      mdEdit: false,
      showMdEdit: false,
      transInfo: [],
      myTimer: null,
      jmObj: null,
      isEmpty: false,
      reTryCount: 0,
      loadFlag: -1,
      editIndex: -1,
      hoverIndex: -1,
      editValue: '',
      showTranNum: 50,
      summaryFail: 0,
      transFail: 0,
      beforeLoading: false,
      summaryLoadType: 1,
      btnTxt: {
        save: this.$t('Filelist_selected_rename_button_right'),
        cancel: this.$t('Filelist_selected_rename_button_left'),
        edit: this.$t('Filedetail_transcription_textfunction_buttontwo'),
        copy: this.$t('Filedetail_transcription_textfunction_buttonone'),
      },
      speakerHolder: '',
      speakerName: '',
      speakerTranInfo: {},
      historySpeakerList: [],
      onlyCurrentSpeaker: true,
      popSpeakerTime: {},
      popSpeakerIndex: -1,
      rateInfo: {},
      fileTsFinshed: false,
    };
  },
  watch: {
    fileInfo: {
      handler(n, o) {
        if (n.id != o.id) {
          if (this.mdEdit && this.info.id) {
            //切换文件总结自动保存
            this.reqPatchInfo('/file/' + this.info.id, { ai_content: this.summaryAllData }).then(
              (data) => {
                this.info.ai_content = this.summaryAllData;
                this.initFileDetail();
              },
            );
          } else {
            this.initFileDetail();
          }
        }
      },
      deep: true,
    },
  },
  computed: {
    isTrashFile() {
      return this.$parent.selectMenu == 'trash';
    },
  },
  mounted() {
    // emitter.on('onUpdate', (data) => {
    //   this.summaryAllData = data;
    //   this.saveMd(null, this.summaryAllData);
    // });

    // emitter.on('ready', (data) => {
    //   this.editorReady = data;
    //   this.saveMd(null, this.summaryAllData);
    // });

    this.initFileDetail();
    let speakerNames = window.localStorage.getItem('speakerNames');
    if (speakerNames) {
      this.historySpeakerList = JSON.parse(speakerNames);
    }
    // const resizeObserver = new ResizeObserver(() => {
    //   autoResize(document.querySelector('#name-edit'));
    // });

    // // 开始监听元素
    // resizeObserver.observe(document.querySelector('.rightBox'));
  },
  beforeUnmount() {
    if (this.myTimer != null) {
      clearTimeout(this.myTimer);
    }
    if (this.jmObj != null) {
      this.jmObj = null;
    }
  },
  methods: {
    onNameChange(value) {
      this.info.filename = value;
      this.reqPatchInfo('/file/' + this.info.id, { filename: value }).then((data) => {});
    },
    onNameEdit(event) {
      const target = event.target;
      const value = event.target.value;
      if (!value) {
        return false;
      }
      this.info.filename = value;
      autoResize(target);
    },

    onUpdate(data) {
      this.summaryAllData = data;
      this.saveMd(null, this.summaryAllData);
    },

    onReady(editor) {
      this.beforeLoading = false;
    },

    useSpeakerList() {
      return this.historySpeakerList.filter((item) => item.indexOf(this.speakerName) > -1);
    },
    getErrorMsg(status) {
      if (status == -7 || status == -111) {
        return this.getTranslateStr('Filedetail_status_tooshort');
      } else if (status == -11 || status == -13) {
        return this.getTranslateStr('Template_error_noright');
      } else if (status == -12) {
        return this.getTranslateStr('Template_error_noexist');
      }
      return '';
    },
    async initFileDetail() {
      this.showEditor = false;
      if (this.myTimer != null) {
        clearTimeout(this.myTimer);
      }
      this.loadFlag = -1;
      this.isLoading = false;
      this.editIndex = -1;
      this.popSpeakerTime = {};
      this.popSpeakerIndex = -1;
      this.mdEdit = false;
      if (this.jmObj != null) {
        this.jmObj = null;
      }
      this.summaryFlag = 0;
      this.summaryFail = 0;
      this.transFail = 0;
      this.info = this.fileInfo;
      this.fileTsFinshed = false;

      let myRate = this.fileRateInfo[this.info.id];
      if (!myRate) {
        myRate = {};
      }
      this.rateInfo = myRate;
      this.totalTime = this.info.duration / 1000;
      this.initMyWave();
      plaudMind.removeMap();
      this.initSpeakerInfo();
      let hasData = () => {
        this.isEmpty = false;
        // this.beforeLoading = false;
        this.initLeftRightWidth();
        this.transInfo = this.info.trans_result;
        this.setSpeakerColor();
        if (typeof this.info.ai_content === 'string') {
          this.showTrans = !this.leftShowStatus;
          this.summaryAllData = markdownSpeakerEditFormat(this.info.ai_content);

          this.summaryLoad(true); //第一次打开的是个未转写的文件，转写后需要执行
        } else {
          if (!this.leftShowStatus) {
            this.leftShowStatus = true;
            this.initStyleInfo();
          }
          this.showTrans = true;
          this.summaryAllData = '';
        }
        this.defaultValue = this.summaryAllData;

        // setTimeout(() => {
        // }, 0);
        // this.$nextTick(() => {
        //   autoResize(document.querySelector('#name-edit'));
        // });
        // this.initEditor();
        this.showEditor = true;
        this.beforeLoading = true;
      };

      this.transInfo = [];
      this.summaryAllData = '';
      this.beforeLoading = true;
      this.isEmpty = false;
      if (!this.info.trans_result) {
        let tempInfo = await this.reqPostInfo('/file/list', [this.info.id]);
        if (tempInfo.status === 0 && tempInfo.data_file_list.length > 0) {
          let { trans_result, ai_content, ori_fullname, ori_location, extra_data } =
            tempInfo.data_file_list[0];
          Object.assign(this.info, { ori_fullname, ori_location, extra_data });
          if (trans_result && trans_result.length > 0) {
            this.info.trans_result = trans_result;
            this.info.ai_content = plaudMind.beforeSetMd(ai_content, this.info.filename);
          }
        }
      }
      let statusInfo = await this.reqGetInfo('/ai/status'),
        fileId = this.info.id;
      if (statusInfo.status === 0) {
        this.beforeLoading = false;
        let {
          data_processing,
          data_processing_chatllm,
          data_processing_transsumm,
          data_processing_ai,
          data_processing_chatllm_ai,
          data_processing_transsumm_ai,
        } = statusInfo;
        if (
          (data_processing_ai && data_processing_ai.includes(fileId)) ||
          (data_processing_transsumm_ai &&
            data_processing_transsumm_ai.files_trans &&
            data_processing_transsumm_ai.files_trans.includes(fileId)) ||
          (data_processing_transsumm_ai &&
            data_processing_transsumm_ai.files_summ &&
            data_processing_transsumm_ai.files_summ.includes(fileId)) ||
          (data_processing_chatllm_ai &&
            data_processing_chatllm_ai.some((item) => item.file_id === fileId))
        ) {
          this.fileTsFinshed = true;
        }
        if (
          data_processing.includes(fileId) ||
          (data_processing_transsumm.files_trans &&
            data_processing_transsumm.files_trans.includes(fileId)) ||
          (data_processing_transsumm.files_summ &&
            data_processing_transsumm.files_summ.includes(fileId))
        ) {
          this.reTranInfo({ flag: 11, type: '', lang: '', subtype: '', transAi: '' });
          return false;
        } else {
          let smInfo = data_processing_chatllm.find((item) => item.file_id == fileId);
          if (smInfo) {
            this.reTranInfo(
              { flag: 22, type: smInfo.type, lang: '', transAi: '', subtype: smInfo.type_type },
              smInfo.post_id,
            );
            if (this.info.trans_result && this.info.trans_result.length > 0) {
              this.transInfo = this.info.trans_result;
            }
            return false;
          }
        }
      }
      if (this.info.trans_result && this.info.trans_result.length > 0) {
        hasData();
      } else {
        this.beforeLoading = false;
        this.isEmpty = true;
      }
    },
    doMenu(menu = {}) {
      if (menu.id == 'move') {
        this.$parent.doMoveTag([this.info.id]);
      } else if (menu.id == 'trash') {
        this.$parent.moveToTrash([this.info.id]);
      } else if (menu.id == 'jpeg') {
        if (this.summaryFlag !== 1) {
          this.setMessage('switch to mind-map');
          return false;
        }
        this.downloadMap();
      } else {
        if (!this.$root.userStateInfo.membership_type) {
          this.$parent.doUpladFiles();
          return;
        }
        let flag = 0;
        if (menu.id == 'retran') {
          flag = 1;
        }
        if (menu.id == 'resum') {
          flag = 2;
        }
        this.$refs.TranSummary.handlerVisible(true, flag, this.info);
      }
    },
    switchSummary(flag) {
      if (this.summaryFlag !== flag) {
        this.summaryFlag = flag;
        if (this.summaryFlag === 1) {
          this.setGALogEvent('web_mindmap');
          this.$nextTick(() => {
            if (!document.querySelector('.mm-toolbar')) {
              this.createMap();
            }
          });
        } else {
          this.setGALogEvent('web_summary');
        }
      }
    },
    getTransInfo(reload = 0, lang, type, subtype, isSpeaker = false, transAi = '') {
      let params = { is_reload: reload },
        pamInfo = { language: lang };
      if (type) {
        params.summ_type = type;
        params.summ_type_type = subtype;
      }
      if (isSpeaker) {
        pamInfo.diarization = 1;
      }
      if (transAi) {
        pamInfo.llm = transAi;
      }
      params.info = JSON.stringify(pamInfo);
      this.reqPostInfo('/ai/transsumm/' + this.info.id, params).then((data) => {
        let status = data.status;
        if (this.myTimer != null) {
          clearTimeout(this.myTimer);
        }
        if (status < 0 && status != -2) {
          this.loadFlag = -1;
          this.isLoading = false;
        }
        if (status == 0) {
          this.myTimer = setTimeout(() => {
            this.getTransInfo(0, lang, type, subtype, isSpeaker, transAi);
          }, 3000);
          if (data.data_result && data.data_result.length > 0) {
            this.summaryLoadType = 1;
          }
        } else if (status == -1) {
          this.setConfirm({
            title: this.$t('Filedetail_submit_insufficienttime_title'),
            backCancel: true,
            cancelbtnStyle: { 'max-width': '200px' },
            msgStyle: { 'padding-left': '0px', 'padding-right': '0px' },
            msg: this.getTranslateStr('Web_filedetail_status_verificationfail'),
            cancelname: this.$t('Web_filedetail_submit_insufficienttime_button_got'),
          });
        } else if (status < 0) {
          //
          if (status === -111 || status === -11 || status === -12 || status === -13) {
            this.summaryFail = status;
            if (data.data_result && data.data_result.length > 0) {
              this.transInfo = data.data_result;
              this.saveTrans();
              this.setSpeakerColor();
            } else {
              this.showTrans = false;
            }
          } else if (status === -7) {
            //内容太短
            this.transFail = status;
          } else if (status == -2 && data.data_post_id) {
            this.reTryCount++;
            if (this.reTryCount < 6) {
              this.myTimer = setTimeout(() => {
                this.getTransInfo(0, lang, type, subtype, isSpeaker, transAi);
              }, 3000);
            } else {
              this.loadFlag = -1;
              this.isLoading = false;
            }
          } else {
            // this.setMessage(data.msg);
            this.loadFlag = -1;
            this.isLoading = false;
          }
        } else if (status == 1) {
          //成功
          this.showTranNum = 50;
          let { data_result, data_result_summ } = data;
          this.transInfo = data_result;
          this.loadFlag = -1;
          this.saveTrans();
          this.setSpeakerColor();
          if (data_result_summ) {
            this.saveMd(this.resloveSummaryData(data_result_summ));
          } else if (this.info.ai_content) {
            this.summaryAllData = markdownSpeakerEditFormat(this.info.ai_content);
            this.defaultValue = this.summaryAllData;

            // this.$nextTick(() => {
            //   autoResize(document.querySelector('#name-edit'));
            // });
          }

          this.isLoading = false;
          this.$parent.getTaskStatusList();
          this.$parent.getUserInfo(); //刷新积分值
          this.fileTsFinshed = false;
        }
      });
    },
    resloveSummaryData(data) {
      if (data && data.length > 0) {
        data = data.replace('$[audio_start_time]', this.getDateStr(this.info.start_time));
      }
      return data;
    },
    getSummaryInfo(type, subtype = 'system', lang, transAi) {
      this.isLoading = true;
      let params = { type: type, post_id: this.postId, type_type: subtype },
        pamInfo = { language: lang };
      if (transAi) {
        pamInfo.llm = transAi;
      }
      params.info = JSON.stringify(pamInfo);
      this.reqPostInfo('/ai/chatllm/' + this.info.id, params).then((data) => {
        let status = data.status;
        if (this.myTimer != null) {
          clearTimeout(this.myTimer);
        }
        if (status != 0 && status != -2) {
          this.isLoading = false;
        }
        if (status == 0) {
          this.myTimer = setTimeout(() => {
            this.getSummaryInfo(type, subtype, lang, transAi);
          }, 3000);
        } else if (status < 0) {
          if (status === -1) {
            this.isEmpty = true;
          } else if (status === -111) {
            this.summaryFail = status;
          } else if (status === -11) {
            //没有使用自定义模板的权限
            this.summaryFail = status;
          } else if (status === -12) {
            //自定义模板不存在
            this.summaryFail = status;
          } else if (status === -13) {
            //无权限使用当前系统模板
            this.summaryFail = status;
          } else if (status == -2 && data.data_post_id) {
            this.postId = data.data_post_id;
            this.reTryCount++;
            if (this.reTryCount < 6) {
              this.myTimer = setTimeout(() => {
                this.getSummaryInfo(type, subtype, lang, transAi);
              }, 3000);
            } else {
              this.isLoading = false;
            }
          } else {
            this.setMessage(data.msg);
            this.isLoading = false;
          }
        } else if (status == 1) {
          this.fileTsFinshed = false;
          //成功
          this.$parent.getTaskStatusList();
          this.saveMd(this.resloveSummaryData(data.data_result));
        }
      });
    },
    changePlayTime(index) {
      if (this.wavesurferObj) {
        this.playIndex = index;
        this.playStatus = true;
        if (this.speakerTimer != null) {
          clearTimeout(this.speakerTimer);
        }
        //不延迟，页面的播放显示状态会有延迟
        this.speakerTimer = setTimeout(() => {
          this.wavesurferObj.play(this.transInfo[index].start_time / 1000);
        }, 100);

        this.setGALogEvent('web_play_transcription');
      }
    },
    getLeftSec() {
      let { seconds_left, membership_id, membership_id_traffic, seconds_left_traffic } =
        this.$parent.userInfo;
      if (!membership_id) {
        seconds_left = seconds_left_traffic;
      } else {
        if (membership_id_traffic) {
          seconds_left += seconds_left_traffic;
        }
      }
      return seconds_left;
    },
    reTranInfo({ flag, lang, type, subtype, isSpeaker, transAi }, postId = new Date().getTime()) {
      let myTranFn = () => {
        this.postId = postId;
        this.reTryCount = 0;
        this.isEmpty = false;
        this.summaryFail = 0;
        this.transFail = 0;
        this.cancalEditMd(1);
        if (flag == 2 || flag == 22) {
          this.summaryLoadType = 1;
          if (!this.leftShowStatus) {
            this.leftShowStatus = true;
            this.initStyleInfo();
          }
          this.setFileTemplateType(this.info.id, type);
          this.getSummaryInfo(type, subtype, lang, transAi);
        } else {
          this.summaryLoadType = 2;
          this.loadFlag = 0;
          this.isLoading = true;
          this.showTrans = true;
          if (!this.showTrans) {
            this.changeShowTrans(true);
          }
          this.setFileTemplateType(this.info.id, type, true);
          this.getTransInfo(flag == 1 ? 1 : 0, lang, type, subtype, isSpeaker, transAi);
        }
        if (flag < 3) {
          //0,1,2  转写或总结刷新列表状态
          this.$parent.getTaskStatusList();
          this.setGALogEvent(flag == 2 ? 'web_summary_summit' : 'web_transcribe_summit', {
            lang,
            scenario: type,
            file: this.info.id,
          });
        }
      };
      if (flag === 0 || flag === 1) {
        this.$parent.getUserInfo().then(() => {
          //转写时长不够
          if (this.info.duration / 1000 > this.getLeftSec()) {
            this.$refs.UserUpgrade.handlerVisible(true, {
              flag: 1,
              duration: this.info.duration,
              left: this.getLeftSec(),
            });
            return false;
          } else {
            myTranFn();
          }
        });
      } else {
        myTranFn();
      }
    },
    saveMd(result, iptValue) {
      let mdValue = '',
        iptStatus = typeof iptValue != 'undefined',
        params = {};
      plaudMind.removeMap();

      if (!iptStatus) {
        if (result == null || typeof result == 'undefined') {
          return false;
        }
        let resultInfo = JSON.parse(result),
          extra_data = this.info.extra_data;
        mdValue = resultInfo.markdown;
        if (!extra_data) {
          extra_data = {};
        }
        if (resultInfo.form) {
          extra_data.aiContentFrom = resultInfo.form;
        } else {
          extra_data.aiContentFrom = {};
        }
        params.extra_data = extra_data;
        this.summaryAllData = markdownSpeakerEditFormat(mdValue);
        this.info.ai_content = mdValue;
        this.summaryLoad(true);

        this.defaultValue = this.summaryAllData;
        this.showEditor = true;

        // this.$nextTick(() => {
        //   autoResize(document.querySelector('#name-edit'));
        // });

        //文件夹是默认的日期的时候才替换
        if (
          !isNaN(new Date(this.info.filename).getTime()) &&
          resultInfo.header &&
          resultInfo.header.headline
        ) {
          params.filename = resultInfo.header.headline;
        }

        if (this.summaryFlag == 1) {
          this.$nextTick(() => {
            this.createMap();
          });
        }
      } else {
        mdValue = plaudMind.beforeSetMd(iptValue, this.info.filename);
      }
      params.ai_content = mdValue;
      this.reqPatchInfo('/file/' + this.info.id, params).then((data) => {
        if (iptStatus) {
          this.info.ai_content = mdValue;
          this.summaryAllData = markdownSpeakerEditFormat(mdValue);
        }
        if (params.filename) {
          this.info.filename = params.filename;
        }
        if (params.extra_data) {
          this.info.extra_data = params.extra_data;
        }
      });
    },
    saveTrans() {
      this.reqPatchInfo('/file/' + this.info.id, { trans_result: this.transInfo });
      this.info.trans_result = this.transInfo;
    },
    cancalEditMd(flag, byCLick = false) {
      if (this.mdEdit) {
        if (flag !== 1) {
          // document.removeEventListener('click',this.cancalEditMd,false);
          this.saveMd(null, this.summaryAllData);
          this.setGALogEvent('web_summary_edit_autosave');
        } else {
          this.summaryAllData = markdownSpeakerEditFormat(this.info.ai_content);

          if (byCLick) {
            this.setGALogEvent('web_summary_edit_cancel');
          }
        }
      }
      this.mdEdit = false;
    },
    saveEditMd(byCLick = false) {
      this.saveMd(null, this.summaryAllData);
      this.mdEdit = false;
      // emitter.emit('editable', false);

      if (byCLick) {
        this.setGALogEvent('web_summary_edit_save');
      } else {
        this.setGALogEvent('web_summary_edit_autosave');
      }
    },
    doMdClick() {
      this.editBlurSave();
      if (this.isTrashFile) {
        return false;
      }
      if (!this.mdEdit) {
        this.mdEdit = true;
        // emitter.emit('editable', true);

        this.setGALogEvent('web_summary_edit');
        // document.addEventListener('click',this.cancalEditMd,false);
      }
    },
    doTransEvt(flag, index, byClick = false) {
      if (this.mdEdit) {
        this.saveEditMd();
      }
      switch (flag) {
        case 'edit':
          if (this.editIndex != -1) {
            this.transInfo[this.editIndex].content = this.editValue;
            this.saveTrans();
          }
          this.editIndex = index;
          this.editValue = this.transInfo[index].content;
          document.addEventListener('click', this.editBlurSave, false);
          byClick && this.setGALogEvent('web_transcription_edit');
          break;
        case 'copy':
          let copyTxt = this.transInfo[index].content;
          this.$copyText(copyTxt).then(
            (e) => {
              this.showSuccessMsg(this.$t('Web_filedetail_share_link_copied_toast'));
            },
            (e) => {
              this.setMessage('copy fail');
            },
          );
          byClick && this.setGALogEvent('web_transcription_copy');
          break;
        case 'save':
        case 'cancel':
          document.removeEventListener('click', this.editBlurSave, false);
          if (flag === 'save') {
            this.transInfo[this.editIndex].content = this.editValue;
            this.saveTrans();
            byClick && this.setGALogEvent('web_transcription_save');
          } else {
            byClick && this.setGALogEvent('web_transcription_cancel');
          }
          this.editIndex = -1;
          this.editValue = '';
          this.domClick();
          break;
      }
    },
    editBlurSave() {
      if (this.editIndex != -1) {
        this.doTransEvt('save');
      }
    },
    summaryLoad(load) {
      if (this.hasSummaryRateTip) {
        if (load) {
          this.$nextTick(() => {
            let ps = this.$refs.summaryScrollBox.$el;
            //初始没有滚动条
            if (Math.abs(ps.scrollHeight - ps.clientHeight) < 3) {
              this.seeSummaryRateTip();
            }
          });
        } else {
          let ps = this.$refs.summaryScrollBox.$el;
          if (ps.scrollTop >= ps.scrollHeight - ps.clientHeight - 20) {
            this.seeSummaryRateTip();
          }
        }
      }
    },
    /* tranLoad(load) {
      if (load) {
        if (this.hasTransRateTip) {
          this.$nextTick(() => {
            let ps = this.$refs.scrollBox.$el;
            if (Math.abs(ps.scrollHeight - ps.clientHeight) < 3) {
              this.seeTranRateTip();
            }
          });
        }
      } else {
        let ps = this.$refs.scrollBox.$el;
        if (ps.scrollTop >= ps.scrollHeight - ps.clientHeight - 5) {
          if (this.showTranNum < this.transInfo.length) {
            this.showTranNum += 30;
          } else if (this.showTranNum >= this.transInfo.length) {
            //所有加载完成滚动到底
            this.seeTranRateTip();
          }
        }
      }
    }, */
    downloadMap() {
      plaudMind.downloadPic(this.info.filename);
    },
    createMap() {
      plaudMind.showMindMapWeb('mapBox', this.summaryAllData, this.info.filename);
    },
    clickSpeakerPop(tran) {
      this.popSpeakerTime = { ['trans_' + tran.start_time]: true };
    },
    editSpeakerName(tran) {
      this.speakerTranInfo = tran;
      this.onlyCurrentSpeaker = true;
      this.speakerHolder = tran.speaker;
      this.speakerName = '';
    },
    hoverSpeakerName(index, tran) {
      if (this.popSpeakerTime[this.speakerTranInfo.start_time]) {
        this.popSpeakerTime = {};
      }
      if (this.popSpeakerIndex != index) {
        this.popSpeakerIndex = index;
      }
    },
    saveSpeakerName(batchMap) {
      if (this.speakerName || batchMap) {
        let setNameHistory = (name) => {
          let idx = this.historySpeakerList.indexOf(name);
          if (idx > -1) {
            this.historySpeakerList.splice(idx, 1);
          }
          this.historySpeakerList.unshift(name);
        };
        let params = { trans_result: this.transInfo };
        if (!this.onlyCurrentSpeaker || batchMap) {
          //更新当前人所有模块
          this.transInfo.forEach((item) => {
            if (batchMap) {
              let myName = batchMap[item.speaker];
              if (myName) {
                item.speaker = myName;
              }
            } else {
              if (item.speaker === this.speakerHolder) {
                item.speaker = this.speakerName;
              }
            }
          });
          let replaceSpeakerName = (oldName, newName, aiContent = this.summaryAllData) => {
            let resultMdData = aiContent;
            //以Speaker开头数字结尾，直接替换；否则匹配括号的
            if (/^Speaker \d+$/.test(oldName)) {
              let escapedSpeaker = oldName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义正则表达式中的特殊字符
              let regex = new RegExp(`\\b${escapedSpeaker}\\b`, 'mg');
              resultMdData = resultMdData.replace(regex, newName);
            } else {
              resultMdData = resultMdData.replace(
                new RegExp(`\\[${oldName}\\]`, 'mg'),
                '[' + newName + ']',
              );
            }
            return resultMdData;
          };
          //对于参会人员名称进行去重
          let uniqueSpeakerName = (resultMdData) => {
            if (this.info.extra_data && this.info.extra_data.aiContentFrom) {
              let { attendees } = this.info.extra_data.aiContentFrom;
              if (attendees) {
                let preTxt = attendees.substring(0, attendees.indexOf('[')),
                  mdTxt = resultMdData.substring(resultMdData.indexOf(preTxt) + preTxt.length);
                let attenTxt = mdTxt.substring(0, mdTxt.indexOf('\n'));
                let users = attenTxt.match(/\[[^\]]+\]/g);
                let newUsers = Array.from(new Set(users));
                resultMdData = resultMdData.replace(attenTxt, newUsers.join(' '));
              }
            }
            return resultMdData;
          };

          if (batchMap) {
            let aiContent = this.summaryAllData;
            for (let kn in batchMap) {
              aiContent = replaceSpeakerName(kn, batchMap[kn], aiContent);
              setNameHistory(batchMap[kn]);
            }
            params.ai_content = uniqueSpeakerName(aiContent);
            this.setSpeakerColor(this.transInfo, true, batchMap);
          } else {
            params.ai_content = uniqueSpeakerName(
              replaceSpeakerName(this.speakerHolder, this.speakerName),
            );
            let nameMap = {};
            nameMap[this.speakerHolder] = this.speakerName;
            this.setSpeakerColor(this.transInfo, true, nameMap);
          }
        } else {
          this.speakerTranInfo.speaker = this.speakerName;
          let nameMap = {};
          nameMap[this.speakerHolder] = this.speakerName;
          this.setSpeakerColor(this.transInfo, true, nameMap, true);
        }
        if (!batchMap) {
          this.domClick();
          this.beforeLoading = true;
          setNameHistory(this.speakerName);
        }
        return this.reqPatchInfo('/file/' + this.info.id, params).then((data) => {
          this.beforeLoading = false;
          this.info.trans_result = this.transInfo;
          if (params.ai_content) {
            this.summaryAllData = params.ai_content;
            this.info.ai_content = this.summaryAllData;
          }
          this.popSpeakerTime = {};
          window.localStorage.setItem('speakerNames', JSON.stringify(this.historySpeakerList));
        });
      } else {
        this.domClick();
      }
    },
    editBatchSpeaker() {
      this.$refs.NameSpeakers.handlerVisible(true, this.speakerTrans);
    },
    showUpgradeModel(status, flag) {
      if (status) {
        this.$refs.TranSummary.upgradeStatus = true;
        this.$refs.UserUpgrade.handlerVisible(true, { flag });
      } else {
        this.$refs.TranSummary.upgradeStatus = false;
      }
    },
  },
};
</script>
