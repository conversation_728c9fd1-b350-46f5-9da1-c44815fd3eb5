// 简单的事件总线实现，替代mitt
class EventBus {
  constructor() {
    this.events = {};
  }

  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  off(event, callback) {
    if (!this.events[event]) return;

    if (callback) {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    } else {
      this.events[event] = [];
    }
  }

  emit(event, ...args) {
    if (!this.events[event]) return;

    this.events[event].forEach(callback => {
      callback(...args);
    });
  }
}

const emitter = new EventBus();

export default emitter;
