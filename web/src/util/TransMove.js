export default {
  data() {
    return {
      initLeftWidth: 0,
      initRightWidth: 0,
      leftStyle: {},
      rightStyle: {},
      moveInfo: {},
      leftShowStatus: true,
      leftMinStatus: false,
      moveLeftWidth: 0,
      moveRightWidth: 0,
      changeTimer: null,
    };
  },
  mounted() {
    this.$root.BUS.on('changeLayout', this.initStyleByChange);
  },
  beforeUnmount() {
    this.$root.BUS.off('changeLayout', this.initStyleByChange);
    if (this.changeTimer != null) {
      clearTimeout(this.changeTimer);
    }
  },
  methods: {
    initLeftRightWidth(init = true) {
      this.$nextTick(() => {
        if (this.$refs.leftCont) {
          this.initLeftWidth = this.$refs.leftCont.clientWidth;
        }
        if (this.$refs.rightCont) {
          this.initRightWidth = this.$refs.rightCont.clientWidth;
        }
        if (this.$refs.scrollBox && init) {
          this.$refs.scrollBox.$el.scrollTop = 0;
        }
      });
    },
    changeShowTrans(status, byClick = false) {
      this.popSpeakerTime = {};
      if (byClick) {
        if (status) {
          this.setGALogEvent('web_open_transcription');
        } else {
          this.setGALogEvent('web_close_transcription');
        }
      }
      this.showTrans = status;
      /* if (status) {
        this.tranLoad(true);
      } */
      if (!status) {
        this.leftShowStatus = true;
        this.initStyleInfo();
      } else {
        this.$parent.resizePage();
      }
      if (this.changeTimer != null) {
        clearTimeout(this.changeTimer);
      }
      this.changeTimer = setTimeout(() => {
        this.initLeftRightWidth(false);
      }, 200);
    },
    initStyleByChange() {
      if (this.leftShowStatus && this.showTrans) {
        this.initStyleInfo();
      }
      if (this.changeTimer != null) {
        clearTimeout(this.changeTimer);
      }
      this.changeTimer = setTimeout(() => {
        this.initLeftRightWidth(false);
      }, 500);
    },
    initStyleInfo() {
      this.leftStyle = {};
      this.rightStyle = {};
    },
    doMoveStart(e) {
      this.editBlurSave();

      let { clientX, clientY } = e;
      this.moveInfo = {
        status: true,
        x: clientX,
        y: clientY,
      };
      this.moveLeftWidth = this.$refs.leftCont.clientWidth;
      this.moveRightWidth = this.$refs.rightCont.clientWidth;
      document.onmousemove = this.mouseMove;
      document.onmouseup = this.mouseUp;
    },
    mouseMove(e) {
      document.onselectstart = function () {
        return false;
      };
      if (!this.moveInfo.status) {
        return false;
      }
      e.preventDefault();

      let { clientX, clientY } = e,
        { x, y } = this.moveInfo;
      let moveX = clientX - x,
        lw = this.moveLeftWidth + moveX,
        rw = this.moveRightWidth - moveX;

      if (moveX < 0) {
        //往左
        if (lw <= this.initLeftWidth * 0.5) {
          lw = 0;
          if (this.leftShowStatus) {
            this.setGALogEvent('web_close_summary');
          }
          this.leftShowStatus = false;
        } else {
          if (!this.leftShowStatus) {
            this.setGALogEvent('web_open_summary');
          }
          this.leftShowStatus = true;
        }
      } else {
        //往右
        if (lw > this.initLeftWidth * 0.5) {
          if (!this.leftShowStatus) {
            this.setGALogEvent('web_open_summary');
          }
          this.leftShowStatus = true;
          this.leftMinStatus = false;
          if (lw >= this.initLeftWidth || rw < this.initRightWidth) {
            this.initStyleInfo();
            return false;
          }
        }
      }
      this.leftStyle = { width: lw + 'px' };
      if (lw == 0) {
        this.leftStyle.overflow = 'hidden';
        this.rightStyle = {};
      } else {
        this.rightStyle = { width: rw + 'px' };
      }

      if (lw < 100) {
        //否则拖拽区域存在被遮住情况
        this.leftStyle.visibility = 'hidden';
        this.leftMinStatus = true;
      } else {
        this.leftMinStatus = false;
      }
    },
    mouseUp() {
      this.moveInfo = {};
      document.onmousemove = null;
      document.onmouseup = null;
      document.onselectstart = null;
    },
  },
};
