import Vue from 'vue';
import BonusApp from './BonusApp';

import RequestMixins from './util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from '@/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from './components/common/message';
// Vue.prototype.setMessage = Message; // This will be handled in main.js for Vue 3

import './util/Directive';
import i18n from './language/bonus/index';
Vue.config.productionTip = false;

import styles from '@/styles/bonus.scss';
new Vue({
  el: '#app',
  i18n,
  template: '<BonusApp/>',
  components: { BonusApp },
  data: function () {
    return {
      BUS: new Vue(),
    };
  },
  mounted() {
    let language = SysTool.GetQueryString('language');
    if (language) {
      language = language.toLowerCase();
      sessionStorage.setItem('infolang', language);
      this.$i18n.locale = language;
    }
  },
});
