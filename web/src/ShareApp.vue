<template>
  <div class="mainBody">
    <div class="middleCont error" v-if="isError">
      <img src="/static/404.png" alt="" />
      <div class="errorTxt">Sorry! <br />Page Not Found</div>
    </div>
    <template v-else-if="isLoading">
      <div class="middleCont">
        <Loading :status="true" />
      </div>
    </template>
    <template v-else>
      <div class="logBox">
        <div class="logo">
          <img src="/static/logo.png" />
          <div class="nowBtn hand" @click="handleExploreBtnClick">Explore Now ></div>
        </div>
        <div class="desc">
          PLAUD NOTE is the AI note-taker that records, transcribes, and organizes meetings, calls,
          and conversations.
        </div>
      </div>
      <div class="contWrap fileInfo">
        <div class="filename">{{ fileInfo.filename }}</div>
        <div class="datetimeBox">
          <div class="dateTime">{{ getDateStr(fileInfo.start_time) }}</div>
          <div class="line"></div>
          <div class="dateTime">{{ getTimeStr(fileInfo.duration, true) }}</div>
        </div>
        <!--                <div class="tagBox">-->
        <!--                    &lt;!&ndash;                <div class="tagcell" :style="getCTagStyle('#6941C6')">aaaaa</div>&ndash;&gt;-->
        <!--                    <div class="tagcell"  v-for="k in fileInfo.keywords">{{k}}</div>-->
        <!--                </div>-->
      </div>
      <div v-if="showId && myInfo.data_file && myInfo.data_file.id" style="padding: 20px">
        {{ myInfo.data_file.id }}
      </div>
      <div class="middleCont">
        <div class="contWrap" style="border: none" v-if="myInfo.is_audio === 1">
          <!--                    <div class="title">Audio</div>-->
          <div class="audioBox">
            <div class="playBtn" :class="{ stop: playStatus }" @click.stop="audioPlay()"></div>
            <div class="time">{{ getTimeStr(curTime, false, false, true) }}</div>
            <div class="audioBars" ref="audiobar">
              <div class="pros" :style="{ width: getProBar() }"></div>
              <div
                class="circle"
                @mousedown.stop="myMove($event)"
                @touchstart.stop="myMove($event, true)"
              ></div>
            </div>
            <div class="time">{{ getTimeStr(totalTime, false, false, true) }}</div>
            <el-popover
              popper-class="elPopoverClass"
              placement="bottom-end"
              width="326"
              :visible-arrow="false"
              trigger="click"
            >
              <template v-slot:reference>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="$t('change_speed')"
                  placement="bottom-start"
                  :disabled="hasLoad"
                >
                  <div class="svg-icon" :class="hasLoad ? '' : 'cursor-not-allowed'">
                    <svg
                      v-if="speeds[speedIdx] === 0.75"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.8376 3.50695C6.44117 3.50695 6.07495 3.71882 5.87734 4.06249L1.63119 11.4471C1.43462 11.789 1.43462 12.2096 1.63119 12.5514L5.87734 19.936C6.07495 20.2797 6.44117 20.4916 6.8376 20.4916H7.42821C7.83605 20.4916 8.16667 20.8222 8.16667 21.23C8.16667 21.6379 7.83605 21.9685 7.42821 21.9685H6.8376C5.91258 21.9685 5.05808 21.4741 4.59699 20.6722L0.350832 13.2876C-0.10783 12.4899 -0.107829 11.5086 0.350832 10.7109L4.59699 3.32629C5.05808 2.52438 5.91258 2.03003 6.8376 2.03003H15.3169C16.2419 2.03003 17.0964 2.52438 17.5575 3.32629L21.8037 10.7109C22.2623 11.5086 22.2623 12.4899 21.8037 13.2876L20.3424 15.8289C20.1391 16.1825 19.6877 16.3043 19.3341 16.101C18.9806 15.8977 18.8588 15.4463 19.0621 15.0927L20.5233 12.5514C20.7199 12.2096 20.7199 11.789 20.5233 11.4471L16.2772 4.06249C16.0795 3.71882 15.7133 3.50695 15.3169 3.50695L6.8376 3.50695Z"
                        fill="currentColor"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M6.64648 11.9993C6.64648 9.55221 8.6302 7.56849 11.0772 7.56849C13.5243 7.56849 15.508 9.55221 15.508 11.9993C15.508 14.4463 13.5243 16.43 11.0772 16.43C8.6302 16.43 6.64648 14.4463 6.64648 11.9993ZM11.0772 9.04541C9.44588 9.04541 8.1234 10.3679 8.1234 11.9993C8.1234 13.6306 9.44588 14.9531 11.0772 14.9531C12.7086 14.9531 14.0311 13.6306 14.0311 11.9993C14.0311 10.3679 12.7086 9.04541 11.0772 9.04541Z"
                        fill="currentColor"
                      />
                      <path
                        d="M20.9399 22.8143C20.9399 23.0036 21.0914 23.1393 21.3028 23.1393C21.4353 23.1393 21.5205 23.0919 21.6183 22.9405L22.3376 21.8773H22.3534L23.0759 22.9531C23.1579 23.0793 23.2431 23.1393 23.3882 23.1393C23.5996 23.1393 23.7637 23.0005 23.7637 22.7922C23.7637 22.7102 23.7353 22.6345 23.6785 22.5524L22.8424 21.3851L23.6754 20.2619C23.7448 20.1704 23.7732 20.0915 23.7732 20C23.7732 19.8044 23.6186 19.6656 23.3977 19.6656C23.2589 19.6656 23.1737 19.7287 23.0727 19.8833L22.3912 20.9055H22.3755L21.6782 19.877C21.5772 19.7192 21.4984 19.6656 21.3438 19.6656C21.1292 19.6656 20.9652 19.8139 20.9652 20.0063C20.9652 20.0978 20.9936 20.1767 21.0504 20.2524L21.8864 21.4072L21.0377 22.5556C20.9715 22.6502 20.9399 22.7228 20.9399 22.8143Z"
                        fill="currentColor"
                      />
                      <path
                        d="M19.0967 23.1677C18.3962 23.1677 17.7905 22.8332 17.5665 22.3379C17.5223 22.2401 17.4939 22.1454 17.4939 22.0287C17.4939 21.8173 17.6296 21.6848 17.8441 21.6848C18.0145 21.6848 18.1186 21.7511 18.2038 21.9183C18.3584 22.2874 18.6613 22.5051 19.0998 22.5051C19.6393 22.5051 20.0211 22.1233 20.0211 21.5838C20.0211 21.0601 19.6393 20.6878 19.103 20.6878C18.8159 20.6878 18.5666 20.8045 18.3931 20.9749C18.2038 21.1642 18.1249 21.2052 17.9388 21.2052C17.6769 21.2052 17.5475 21.0191 17.5538 20.7982C17.5538 20.7793 17.5538 20.7667 17.557 20.7477L17.7021 19.0282C17.7305 18.6686 17.9135 18.5234 18.27 18.5234H20.2325C20.4407 18.5234 20.5764 18.6559 20.5764 18.8547C20.5764 19.0566 20.4407 19.1892 20.2325 19.1892H18.3931L18.2732 20.5364H18.289C18.4814 20.2524 18.8569 20.0757 19.3144 20.0757C20.1852 20.0757 20.8067 20.691 20.8067 21.5617C20.8067 22.5146 20.1063 23.1677 19.0967 23.1677Z"
                        fill="currentColor"
                      />
                      <path
                        d="M14.458 22.808C14.458 23.0005 14.6031 23.1393 14.8303 23.1393C15.007 23.1393 15.108 23.0762 15.1963 22.9026L16.9663 19.47C17.0767 19.2554 17.124 19.1292 17.124 18.9809C17.124 18.6938 16.9253 18.5234 16.635 18.5234H14.2435C14.0416 18.5234 13.8964 18.6528 13.8964 18.8547C13.8964 19.0566 14.0416 19.1892 14.2435 19.1892H16.2974V19.2049L14.5274 22.5493C14.4801 22.6439 14.458 22.7133 14.458 22.808Z"
                        fill="currentColor"
                      />
                      <path
                        d="M13.3215 23.1109C13.0659 23.1109 12.8672 22.9121 12.8672 22.6565C12.8672 22.4041 13.0659 22.2022 13.3215 22.2022C13.5739 22.2022 13.7727 22.4041 13.7727 22.6565C13.7727 22.9121 13.5739 23.1109 13.3215 23.1109Z"
                        fill="currentColor"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M9.02358 20.7951C9.02358 22.2559 9.69245 23.1677 10.8156 23.1677C11.9357 23.1677 12.6014 22.2495 12.6014 20.7919C12.6014 19.3343 11.9294 18.4319 10.8156 18.4319C9.69876 18.4319 9.02358 19.3406 9.02358 20.7951ZM11.7811 20.7951C11.7811 21.8741 11.4309 22.502 10.8156 22.502C10.1973 22.502 9.84389 21.8709 9.84389 20.7919C9.84389 19.7318 10.2036 19.0977 10.8156 19.0977C11.4246 19.0977 11.7811 19.7287 11.7811 20.7951Z"
                        fill="currentColor"
                      />
                    </svg>
                    <svg
                      v-if="speeds[speedIdx] === 1"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.83761 3.50768L15.3169 3.50768C15.7133 3.50768 16.0795 3.71955 16.2772 4.06322L20.5233 11.4478C20.7199 11.7897 20.7199 12.2103 20.5233 12.5521L19.7255 13.9396C19.5222 14.2931 19.644 14.7446 19.9976 14.9479C20.3512 15.1512 20.8026 15.0293 21.0059 14.6758L21.8037 13.2883C22.2623 12.4907 22.2623 11.5093 21.8037 10.7116L17.5575 3.32702C17.0964 2.52511 16.2419 2.03076 15.3169 2.03076H6.83761C5.91258 2.03076 5.05808 2.52511 4.59699 3.32702L0.350832 10.7116C-0.107829 11.5093 -0.10783 12.4907 0.350832 13.2883L4.59699 20.673C5.05808 21.4749 5.91258 21.9692 6.83761 21.9692H13.2502C13.6581 21.9692 13.9887 21.6386 13.9887 21.2308C13.9887 20.8229 13.6581 20.4923 13.2502 20.4923H6.83761C6.44117 20.4923 6.07495 20.2804 5.87734 19.9368L1.63119 12.5521C1.43462 12.2103 1.43462 11.7897 1.63119 11.4478L5.87734 4.06322C6.07495 3.71955 6.44117 3.50768 6.83761 3.50768Z"
                        fill="currentColor"
                      />
                      <path
                        d="M11.0773 7.56922C8.63027 7.56922 6.64655 9.55295 6.64655 12C6.64655 14.447 8.63027 16.4308 11.0773 16.4308C13.5244 16.4308 15.5081 14.447 15.5081 12C15.5081 9.55295 13.5244 7.56922 11.0773 7.56922ZM8.12347 12C8.12347 10.3686 9.44596 9.04615 11.0773 9.04615C12.7087 9.04615 14.0312 10.3686 14.0312 12C14.0312 13.6314 12.7087 14.9538 11.0773 14.9538C9.44596 14.9538 8.12347 13.6314 8.12347 12Z"
                        fill="currentColor"
                      />
                      <path
                        d="M20.4225 22.239C20.1102 22.239 19.887 22.0402 19.887 21.7563C19.887 21.6143 19.9317 21.5007 20.0452 21.3506L21.0634 19.9877L20.0493 18.6044C19.9641 18.4908 19.9236 18.3732 19.9236 18.2353C19.9236 17.9391 20.1669 17.716 20.4915 17.716C20.7308 17.716 20.8565 17.8012 21.0148 18.0527L21.826 19.2737H21.8504L22.6455 18.0608C22.8037 17.8134 22.9294 17.716 23.1485 17.716C23.4811 17.716 23.7042 17.9189 23.7042 18.219C23.7042 18.3651 23.6596 18.4827 23.546 18.6287L22.5522 19.9552L23.5622 21.3506C23.6515 21.4723 23.692 21.5859 23.692 21.7157C23.692 22.0362 23.4486 22.239 23.1282 22.239C22.897 22.239 22.7834 22.1417 22.6495 21.9388L21.7855 20.6448H21.7611L20.9133 21.9186C20.7511 22.1701 20.6334 22.239 20.4225 22.239Z"
                        fill="currentColor"
                      />
                      <path
                        d="M17.0322 21.6265C17.0322 21.9956 17.2796 22.239 17.6407 22.239C18.0017 22.239 18.2451 21.9956 18.2451 21.6265V16.8966C18.2451 16.4747 17.9855 16.2111 17.5514 16.2111C17.2999 16.2111 17.1052 16.2476 16.8212 16.4423L15.6611 17.2455C15.4704 17.3793 15.3974 17.5173 15.3974 17.7039C15.3974 17.9635 15.58 18.142 15.8274 18.142C15.9532 18.142 16.0383 18.1136 16.1519 18.0324L17.0078 17.4361H17.0322V21.6265Z"
                        fill="currentColor"
                      />
                    </svg>
                    <svg
                      v-if="speeds[speedIdx] === 1.25"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M15.3101 3.50695L6.83077 3.50695C6.43433 3.50695 6.06812 3.71882 5.8705 4.06249L1.62435 11.4471C1.42778 11.789 1.42778 12.2096 1.62435 12.5514L5.8705 19.936C6.06812 20.2797 6.43433 20.4916 6.83077 20.4916H8.41657C8.82441 20.4916 9.15503 20.8222 9.15503 21.23C9.15503 21.6379 8.82441 21.9685 8.41657 21.9685H6.83077C5.90575 21.9685 5.05125 21.4741 4.59015 20.6722L0.343996 13.2876C-0.114666 12.4899 -0.114665 11.5086 0.343996 10.7109L4.59015 3.32629C5.05125 2.52438 5.90575 2.03003 6.83077 2.03003H15.3101C16.2351 2.03003 17.0896 2.52438 17.5507 3.32629L21.7968 10.7109C22.2555 11.5086 22.2555 12.4899 21.7968 13.2876L20.3356 15.8289C20.1323 16.1825 19.6809 16.3043 19.3273 16.101C18.9738 15.8977 18.8519 15.4463 19.0552 15.0927L20.5165 12.5514C20.713 12.2096 20.713 11.789 20.5165 11.4471L16.2703 4.06249C16.0727 3.71882 15.7065 3.50695 15.3101 3.50695Z"
                        fill="currentColor"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M6.63963 11.9993C6.63963 9.55224 8.62336 7.56851 11.0704 7.56851C13.5174 7.56851 15.5012 9.55224 15.5012 11.9993C15.5012 14.4463 13.5174 16.4301 11.0704 16.4301C8.62336 16.4301 6.63963 14.4463 6.63963 11.9993ZM11.0704 9.04543C9.43904 9.04543 8.11656 10.3679 8.11656 11.9993C8.11656 13.6306 9.43904 14.9531 11.0704 14.9531C12.7018 14.9531 14.0242 13.6306 14.0242 11.9993C14.0242 10.3679 12.7018 9.04543 11.0704 9.04543Z"
                        fill="currentColor"
                      />
                      <path
                        d="M20.9331 22.3528C20.9331 22.5421 21.0845 22.6778 21.2959 22.6778C21.4284 22.6778 21.5136 22.6304 21.6114 22.479L22.3308 21.4157H22.3465L23.0691 22.4916C23.1511 22.6178 23.2363 22.6778 23.3814 22.6778C23.5928 22.6778 23.7569 22.5389 23.7569 22.3307C23.7569 22.2487 23.7285 22.173 23.6717 22.0909L22.8356 20.9236L23.6685 19.8004C23.7379 19.7089 23.7663 19.63 23.7663 19.5385C23.7663 19.3429 23.6117 19.2041 23.3909 19.2041C23.252 19.2041 23.1669 19.2672 23.0659 19.4218L22.3844 20.444H22.3686L21.6714 19.4154C21.5704 19.2577 21.4915 19.2041 21.3369 19.2041C21.1224 19.2041 20.9583 19.3523 20.9583 19.5448C20.9583 19.6363 20.9867 19.7152 21.0435 19.7909L21.8796 20.9456L21.0309 22.0941C20.9646 22.1887 20.9331 22.2613 20.9331 22.3528Z"
                        fill="currentColor"
                      />
                      <path
                        d="M19.0898 22.7062C18.3894 22.7062 17.7836 22.3717 17.5596 21.8764C17.5154 21.7786 17.4871 21.6839 17.4871 21.5672C17.4871 21.3558 17.6227 21.2233 17.8373 21.2233C18.0076 21.2233 18.1118 21.2895 18.1969 21.4568C18.3515 21.8259 18.6544 22.0436 19.093 22.0436C19.6325 22.0436 20.0142 21.6618 20.0142 21.1223C20.0142 20.5986 19.6325 20.2263 19.0961 20.2263C18.809 20.2263 18.5598 20.343 18.3862 20.5134C18.1969 20.7027 18.1181 20.7437 17.9319 20.7437C17.67 20.7437 17.5407 20.5576 17.547 20.3367C17.547 20.3178 17.547 20.3052 17.5502 20.2862L17.6953 18.5667C17.7237 18.2071 17.9067 18.0619 18.2632 18.0619H20.2256C20.4339 18.0619 20.5695 18.1944 20.5695 18.3932C20.5695 18.5951 20.4339 18.7276 20.2256 18.7276H18.3862L18.2663 20.0748H18.2821C18.4746 19.7909 18.85 19.6142 19.3075 19.6142C20.1783 19.6142 20.7999 20.2294 20.7999 21.1002C20.7999 22.0531 20.0994 22.7062 19.0898 22.7062Z"
                        fill="currentColor"
                      />
                      <path
                        d="M13.8643 22.2708C13.8643 22.4758 13.9968 22.6147 14.2493 22.6147H16.767C16.9815 22.6147 17.114 22.4821 17.114 22.2834C17.114 22.0815 16.9815 21.9489 16.767 21.9489H14.9497V21.9332L15.994 20.9046C16.7481 20.1663 17.0036 19.8067 17.0036 19.2735C17.0036 18.5099 16.3758 17.9704 15.4639 17.9704C14.5774 17.9704 14.0316 18.491 13.9085 18.9832C13.8927 19.0368 13.8833 19.0936 13.8833 19.1536C13.8833 19.3555 14.0158 19.4912 14.2366 19.4912C14.4228 19.4912 14.5174 19.406 14.5995 19.2419C14.7604 18.8318 15.0475 18.6235 15.4576 18.6235C15.8962 18.6235 16.2085 18.9169 16.2085 19.3239C16.2085 19.671 16.0634 19.9013 15.4829 20.4755L14.0915 21.8543C13.918 22.0184 13.8643 22.113 13.8643 22.2708Z"
                        fill="currentColor"
                      />
                      <path
                        d="M12.9423 22.6494C12.6868 22.6494 12.488 22.4506 12.488 22.195C12.488 21.9426 12.6868 21.7407 12.9423 21.7407C13.1947 21.7407 13.3935 21.9426 13.3935 22.195C13.3935 22.4506 13.1947 22.6494 12.9423 22.6494Z"
                        fill="currentColor"
                      />
                      <path
                        d="M10.9224 22.2676C10.9224 22.5137 11.0865 22.6778 11.3231 22.6778C11.5597 22.6778 11.7238 22.5137 11.7238 22.2676V18.4532C11.7238 18.1724 11.5502 17.9988 11.2631 17.9988C11.0928 17.9988 10.9666 18.0272 10.7646 18.1692L9.8339 18.8223C9.69823 18.9169 9.64775 19.0116 9.64775 19.1378C9.64775 19.3176 9.7708 19.4407 9.94117 19.4407C10.0295 19.4407 10.0926 19.4186 10.1715 19.3618L10.9066 18.8507H10.9224V22.2676Z"
                        fill="currentColor"
                      />
                    </svg>
                    <svg
                      v-if="speeds[speedIdx] === 1.5"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M15.3101 3.50695L6.83077 3.50695C6.43433 3.50695 6.06812 3.71882 5.8705 4.06249L1.62435 11.4471C1.42778 11.789 1.42778 12.2096 1.62435 12.5514L5.8705 19.936C6.06812 20.2797 6.43433 20.4916 6.83077 20.4916H11.0704C11.4783 20.4916 11.8089 20.8222 11.8089 21.23C11.8089 21.6379 11.4783 21.9685 11.0704 21.9685H6.83077C5.90575 21.9685 5.05125 21.4741 4.59015 20.6722L0.343996 13.2876C-0.114666 12.4899 -0.114665 11.5086 0.343996 10.7109L4.59015 3.32629C5.05125 2.52438 5.90575 2.03003 6.83077 2.03003L15.3101 2.03003C16.2351 2.03003 17.0896 2.52438 17.5507 3.32629L21.7968 10.7109C22.2555 11.5086 22.2555 12.4899 21.7968 13.2876L20.3356 15.8289C20.1323 16.1825 19.6809 16.3043 19.3273 16.101C18.9738 15.8977 18.8519 15.4463 19.0552 15.0927L20.5165 12.5514C20.713 12.2096 20.713 11.789 20.5165 11.4471L16.2703 4.06249C16.0727 3.71882 15.7065 3.50695 15.3101 3.50695Z"
                        fill="currentColor"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M11.0704 9.04541C9.43905 9.04541 8.11657 10.3679 8.11657 11.9993C8.11657 13.6306 9.43905 14.9531 11.0704 14.9531C12.7018 14.9531 14.0243 13.6306 14.0243 11.9993C14.0243 10.3679 12.7018 9.04541 11.0704 9.04541ZM6.63964 11.9993C6.63964 9.55221 8.62337 7.56849 11.0704 7.56849C13.5175 7.56849 15.5012 9.55221 15.5012 11.9993C15.5012 14.4463 13.5175 16.43 11.0704 16.43C8.62337 16.43 6.63964 14.4463 6.63964 11.9993Z"
                        fill="currentColor"
                      />
                      <path
                        d="M21.2959 22.6777C21.0845 22.6777 20.9331 22.5421 20.9331 22.3528C20.9331 22.2613 20.9646 22.1887 21.0309 22.0941L21.8796 20.9456L21.0435 19.7909C20.9867 19.7152 20.9583 19.6363 20.9583 19.5448C20.9583 19.3523 21.1224 19.204 21.3369 19.204C21.4915 19.204 21.5704 19.2577 21.6714 19.4154L22.3686 20.444H22.3844L23.0659 19.4217C23.1669 19.2671 23.2521 19.204 23.3909 19.204C23.6117 19.204 23.7663 19.3429 23.7663 19.5385C23.7663 19.63 23.7379 19.7088 23.6685 19.8003L22.8356 20.9235L23.6717 22.0909C23.7285 22.1729 23.7569 22.2487 23.7569 22.3307C23.7569 22.5389 23.5928 22.6777 23.3814 22.6777C23.2363 22.6777 23.1511 22.6178 23.0691 22.4916L22.3466 21.4157H22.3308L21.6114 22.479C21.5136 22.6304 21.4284 22.6777 21.2959 22.6777Z"
                        fill="currentColor"
                      />
                      <path
                        d="M19.0898 22.7061C18.3894 22.7061 17.7836 22.3717 17.5596 21.8764C17.5155 21.7786 17.4871 21.6839 17.4871 21.5672C17.4871 21.3558 17.6227 21.2233 17.8373 21.2233C18.0076 21.2233 18.1118 21.2895 18.1969 21.4567C18.3515 21.8259 18.6544 22.0436 19.093 22.0436C19.6325 22.0436 20.0143 21.6618 20.0143 21.1223C20.0143 20.5986 19.6325 20.2263 19.0961 20.2263C18.809 20.2263 18.5598 20.343 18.3862 20.5134C18.1969 20.7027 18.1181 20.7437 17.9319 20.7437C17.67 20.7437 17.5407 20.5576 17.547 20.3367C17.547 20.3178 17.547 20.3052 17.5502 20.2862L17.6953 18.5667C17.7237 18.207 17.9067 18.0619 18.2632 18.0619H20.2256C20.4339 18.0619 20.5695 18.1944 20.5695 18.3932C20.5695 18.5951 20.4339 18.7276 20.2256 18.7276H18.3862L18.2664 20.0748H18.2821C18.4746 19.7909 18.85 19.6142 19.3075 19.6142C20.1783 19.6142 20.7999 20.2294 20.7999 21.1002C20.7999 22.053 20.0994 22.7061 19.0898 22.7061Z"
                        fill="currentColor"
                      />
                      <path
                        d="M16.4641 22.6494C16.2085 22.6494 16.0098 22.4506 16.0098 22.195C16.0098 21.9426 16.2085 21.7407 16.4641 21.7407C16.7165 21.7407 16.9153 21.9426 16.9153 22.195C16.9153 22.4506 16.7165 22.6494 16.4641 22.6494Z"
                        fill="currentColor"
                      />
                      <path
                        d="M14.8448 22.6777C14.6082 22.6777 14.4441 22.5137 14.4441 22.2676V18.8507H14.4284L13.6932 19.3618C13.6144 19.4186 13.5513 19.4407 13.4629 19.4407C13.2926 19.4407 13.1695 19.3176 13.1695 19.1378C13.1695 19.0116 13.22 18.9169 13.3557 18.8223L14.2864 18.1692C14.4883 18.0272 14.6145 17.9988 14.7849 17.9988C15.072 17.9988 15.2455 18.1723 15.2455 18.4531V22.2676C15.2455 22.5137 15.0815 22.6777 14.8448 22.6777Z"
                        fill="currentColor"
                      />
                    </svg>
                    <svg
                      v-if="speeds[speedIdx] === 1.75"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M15.3101 3.50695L6.83077 3.50695C6.43433 3.50695 6.06812 3.71882 5.8705 4.06249L1.62435 11.4471C1.42778 11.789 1.42778 12.2096 1.62435 12.5514L5.8705 19.936C6.06812 20.2797 6.43433 20.4916 6.83077 20.4916H8.41657C8.82441 20.4916 9.15503 20.8222 9.15503 21.23C9.15503 21.6379 8.82441 21.9685 8.41657 21.9685H6.83077C5.90575 21.9685 5.05125 21.4741 4.59015 20.6722L0.343996 13.2876C-0.114666 12.4899 -0.114665 11.5086 0.343996 10.7109L4.59015 3.32629C5.05125 2.52438 5.90575 2.03003 6.83077 2.03003L15.3101 2.03003C16.2351 2.03003 17.0896 2.52438 17.5507 3.32629L21.7968 10.7109C22.2555 11.5086 22.2555 12.4899 21.7968 13.2876L20.3356 15.8289C20.1323 16.1825 19.6809 16.3043 19.3273 16.101C18.9738 15.8977 18.8519 15.4463 19.0552 15.0927L20.5165 12.5514C20.713 12.2096 20.713 11.789 20.5165 11.4471L16.2703 4.06249C16.0727 3.71882 15.7065 3.50695 15.3101 3.50695Z"
                        fill="currentColor"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M11.0704 9.04541C9.43905 9.04541 8.11657 10.3679 8.11657 11.9993C8.11657 13.6306 9.43905 14.9531 11.0704 14.9531C12.7018 14.9531 14.0243 13.6306 14.0243 11.9993C14.0243 10.3679 12.7018 9.04541 11.0704 9.04541ZM6.63964 11.9993C6.63964 9.55221 8.62337 7.56849 11.0704 7.56849C13.5175 7.56849 15.5012 9.55221 15.5012 11.9993C15.5012 14.4463 13.5175 16.43 11.0704 16.43C8.62337 16.43 6.63964 14.4463 6.63964 11.9993Z"
                        fill="currentColor"
                      />
                      <path
                        d="M20.3728 22.6777C20.1615 22.6777 20.01 22.5421 20.01 22.3528C20.01 22.2613 20.0416 22.1887 20.1078 22.0941L20.9565 20.9456L20.1204 19.7909C20.0637 19.7152 20.0353 19.6363 20.0353 19.5448C20.0353 19.3523 20.1993 19.204 20.4139 19.204C20.5685 19.204 20.6473 19.2577 20.7483 19.4154L21.4456 20.444H21.4613L22.1428 19.4217C22.2438 19.2671 22.329 19.204 22.4678 19.204C22.6887 19.204 22.8432 19.3429 22.8432 19.5385C22.8432 19.63 22.8149 19.7088 22.7454 19.8003L21.9125 20.9235L22.7486 22.0909C22.8054 22.1729 22.8338 22.2487 22.8338 22.3307C22.8338 22.5389 22.6697 22.6777 22.4583 22.6777C22.3132 22.6777 22.228 22.6178 22.146 22.4916L21.4235 21.4157H21.4077L20.6884 22.479C20.5905 22.6304 20.5054 22.6777 20.3728 22.6777Z"
                        fill="currentColor"
                      />
                      <path
                        d="M18.1667 22.7061C17.4663 22.7061 16.8606 22.3717 16.6365 21.8764C16.5924 21.7786 16.564 21.6839 16.564 21.5672C16.564 21.3558 16.6996 21.2233 16.9142 21.2233C17.0846 21.2233 17.1887 21.2895 17.2739 21.4567C17.4285 21.8259 17.7313 22.0436 18.1699 22.0436C18.7094 22.0436 19.0912 21.6618 19.0912 21.1223C19.0912 20.5986 18.7094 20.2263 18.1731 20.2263C17.8859 20.2263 17.6367 20.343 17.4632 20.5134C17.2739 20.7027 17.195 20.7437 17.0088 20.7437C16.747 20.7437 16.6176 20.5576 16.6239 20.3367C16.6239 20.3178 16.6239 20.3051 16.6271 20.2862L16.7722 18.5667C16.8006 18.207 16.9836 18.0619 17.3401 18.0619H19.3026C19.5108 18.0619 19.6465 18.1944 19.6465 18.3932C19.6465 18.5951 19.5108 18.7276 19.3026 18.7276H17.4632L17.3433 20.0748H17.3591C17.5515 19.7909 17.927 19.6142 18.3844 19.6142C19.2552 19.6142 19.8768 20.2294 19.8768 21.1002C19.8768 22.053 19.1764 22.7061 18.1667 22.7061Z"
                        fill="currentColor"
                      />
                      <path
                        d="M13.9004 22.6777C13.6732 22.6777 13.5281 22.5389 13.5281 22.3465C13.5281 22.2518 13.5502 22.1824 13.5975 22.0878L15.3675 18.7434V18.7276H13.3136C13.1116 18.7276 12.9665 18.5951 12.9665 18.3932C12.9665 18.1913 13.1116 18.0619 13.3136 18.0619H15.7051C15.9954 18.0619 16.1941 18.2323 16.1941 18.5194C16.1941 18.6677 16.1468 18.7939 16.0364 19.0084L14.2664 22.4411C14.178 22.6146 14.0771 22.6777 13.9004 22.6777Z"
                        fill="currentColor"
                      />
                      <path
                        d="M12.3916 22.6494C12.136 22.6494 11.9372 22.4506 11.9372 22.195C11.9372 21.9426 12.136 21.7407 12.3916 21.7407C12.644 21.7407 12.8427 21.9426 12.8427 22.195C12.8427 22.4506 12.644 22.6494 12.3916 22.6494Z"
                        fill="currentColor"
                      />
                      <path
                        d="M10.7723 22.6777C10.5357 22.6777 10.3716 22.5137 10.3716 22.2676V18.8507H10.3558L9.62071 19.3618C9.54184 19.4186 9.47874 19.4407 9.39039 19.4407C9.22002 19.4407 9.09697 19.3176 9.09697 19.1378C9.09697 19.0116 9.14746 18.9169 9.28312 18.8223L10.2139 18.1692C10.4158 18.0272 10.542 17.9988 10.7124 17.9988C10.9995 17.9988 11.173 18.1723 11.173 18.4531V22.2676C11.173 22.5137 11.0089 22.6777 10.7723 22.6777Z"
                        fill="currentColor"
                      />
                    </svg>
                    <svg
                      v-if="speeds[speedIdx] === 2"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M15.3101 3.50695L6.83077 3.50695C6.43433 3.50695 6.06812 3.71882 5.8705 4.06249L1.62435 11.4471C1.42778 11.789 1.42778 12.2096 1.62435 12.5514L5.8705 19.936C6.06812 20.2797 6.43433 20.4916 6.83077 20.4916H11.0704C11.4783 20.4916 11.8089 20.8222 11.8089 21.23C11.8089 21.6379 11.4783 21.9685 11.0704 21.9685H6.83077C5.90575 21.9685 5.05125 21.4741 4.59015 20.6722L0.343996 13.2876C-0.114666 12.4899 -0.114665 11.5086 0.343996 10.7109L4.59015 3.32629C5.05125 2.52438 5.90575 2.03003 6.83077 2.03003L15.3101 2.03003C16.2351 2.03003 17.0896 2.52438 17.5507 3.32629L21.7968 10.7109C22.2555 11.5086 22.2555 12.4899 21.7968 13.2876L20.3356 15.8289C20.1323 16.1825 19.6809 16.3043 19.3273 16.101C18.9738 15.8977 18.8519 15.4463 19.0552 15.0927L20.5165 12.5514C20.713 12.2096 20.713 11.789 20.5165 11.4471L16.2703 4.06249C16.0727 3.71882 15.7065 3.50695 15.3101 3.50695Z"
                        fill="currentColor"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M11.0704 9.04541C9.43905 9.04541 8.11657 10.3679 8.11657 11.9993C8.11657 13.6306 9.43905 14.9531 11.0704 14.9531C12.7018 14.9531 14.0243 13.6306 14.0243 11.9993C14.0243 10.3679 12.7018 9.04541 11.0704 9.04541ZM6.63964 11.9993C6.63964 9.55221 8.62337 7.56849 11.0704 7.56849C13.5175 7.56849 15.5012 9.55221 15.5012 11.9993C15.5012 14.4463 13.5175 16.43 11.0704 16.43C8.62337 16.43 6.63964 14.4463 6.63964 11.9993Z"
                        fill="currentColor"
                      />
                      <path
                        d="M20.4157 22.7768C20.1033 22.7768 19.8802 22.578 19.8802 22.294C19.8802 22.1521 19.9248 22.0385 20.0384 21.8884L21.0566 20.5254L20.0425 19.1421C19.9573 19.0286 19.9167 18.9109 19.9167 18.773C19.9167 18.4769 20.1601 18.2538 20.4846 18.2538C20.724 18.2538 20.8497 18.339 21.0079 18.5905L21.8192 19.8115H21.8435L22.6386 18.5986C22.7968 18.3511 22.9226 18.2538 23.1416 18.2538C23.4743 18.2538 23.6974 18.4566 23.6974 18.7568C23.6974 18.9028 23.6527 19.0204 23.5392 19.1665L22.5453 20.493L23.5554 21.8884C23.6446 22.0101 23.6852 22.1237 23.6852 22.2535C23.6852 22.5739 23.4418 22.7768 23.1213 22.7768C22.8901 22.7768 22.7765 22.6794 22.6427 22.4766L21.7786 21.1826H21.7543L20.9065 22.4563C20.7442 22.7078 20.6266 22.7768 20.4157 22.7768Z"
                        fill="currentColor"
                      />
                      <path
                        d="M15.1107 22.6916C14.7253 22.6916 14.5347 22.4766 14.5347 22.1683C14.5347 21.9371 14.6239 21.7829 14.8713 21.5557L16.6156 19.8885C17.3377 19.1949 17.5243 18.9109 17.5243 18.489C17.5243 18.0063 17.1511 17.6615 16.6278 17.6615C16.1573 17.6615 15.8327 17.8968 15.6218 18.3673C15.4839 18.6107 15.3419 18.7243 15.0782 18.7243C14.7496 18.7243 14.5549 18.5296 14.5549 18.2254C14.5549 18.1321 14.5712 18.0469 14.5996 17.9617C14.7983 17.3127 15.5326 16.7082 16.64 16.7082C17.8813 16.7082 18.7169 17.406 18.7169 18.412C18.7169 19.1259 18.368 19.5721 17.4107 20.4767L16.1451 21.6937V21.718H18.3518C18.6682 21.718 18.8589 21.9087 18.8589 22.2048C18.8589 22.4928 18.6682 22.6916 18.3518 22.6916H15.1107Z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </el-tooltip>
              </template>
              <div v-if="hasLoad" class="speedBox">
                <div class="title">{{ $t('Filedetail_speed_subtitle') }}</div>
                <div class="speedBarBox">
                  <div class="bar">
                    <div class="pro" :style="{ width: posWidth }"></div>
                    <div class="circle" :style="{ left: `calc(${posWidth} - 6px)` }"></div>
                    <div
                      class="barHand"
                      :class="['hand' + i, i < speedIdx + 1 ? 'hidden' : '']"
                      v-for="i in speeds.length"
                      @click="changeSpeed(i - 1)"
                    ></div>
                  </div>
                </div>
                <div class="barTxt">
                  <div
                    class="txt"
                    v-for="(s, index) in speeds"
                    @click="changeSpeed(index)"
                    :class="{ active: index <= speedIdx }"
                  >
                    {{ s }}x
                  </div>
                </div>
                <!-- <div class="line"></div> -->
                <!-- <div class="skipBox"> -->
                <!-- <div class="left">{{$t('Filedetail_speed_button')}}</div> -->
                <!-- <el-switch v-model="skipStatus" active-color="#1F1F1F" inactive-color="#1F1F1F"></el-switch> -->
                <!-- </div> -->
              </div>
            </el-popover>
          </div>
        </div>

        <div
          class="contWrap bno summary-tab-wap"
          v-if="(myInfo.is_ai_content === 1 || myInfo.is_mindmap === 1) && contentList.length > 0"
        >
          <!-- <div class="summary-tab-title">Summary Dimensions</div> -->
          <div class="summary-tab-list">
            <div
              class="summary-tab-item"
              :class="{ active: item.original_category === activeTab }"
              v-for="item in contentList"
              @click="changeTab(item)"
            >
              {{ item.category }}
            </div>
          </div>
        </div>

        <div
          class="contWrap bno"
          style="padding-bottom: 10px; padding-top: 16px"
          v-if="myInfo.is_ai_content === 1"
        >
          <!--                    <div class="title">Summary</div>-->
          <mavon-editor
            style="min-height: 0px; border: none"
            v-model="summaryData"
            placeholder="输入内容"
            :boxShadow="false"
            :subfield="false"
            :toolbarsFlag="false"
            fontSize="16px"
            :tabSize="8"
            previewBackground="transparent"
            defaultOpen="preview"
          />
        </div>
        <div class="contWrap bno relative" id="moduleCont" v-if="myInfo.is_mindmap === 1">
          <!--                    <div class="title">Mind-map</div>-->
          <!--                    <div class="mindmapBox" id="mapBox"></div>-->
          <svg class="mindmapBox" id="mapBox"></svg>
        </div>
        <div class="contWrap" v-if="myInfo.is_trans === 1">
          <div class="title">Transcription</div>
          <div class="transList">
            <template v-for="(info, index) in fileInfo.trans_result">
              <div
                class="ttime"
                :style="{ '--color': info.speaker ? speakerMap[info.speaker] : '' }"
              >
                <span class="sname" v-if="info.speaker">{{ info.speaker }}</span>
                {{ getTimeStr(info.start_time, true) }} --> {{ getTimeStr(info.end_time, true) }}
                <span class="hide">$br$</span>
              </div>
              <div class="tcont">{{ info.content }}<span class="hide">$br$</span></div>
            </template>
          </div>
        </div>
      </div>
    </template>
    <div class="footerBox">Copyright©2024 PLAUD.Al.All Rights Reserved</div>
  </div>
</template>

<script>
import 'jsmind/style/jsmind.css';
import './styles/jsmind.scss';
import plaudMind from './util/plaudMind';
import { markdownSpeakerEditFormat } from './common/markdown';
import FirebaseMixins from './util/FirebaseMixins';
import qs from 'qs';
import Vue from 'vue';
// Element Plus components are globally registered, no need to import here
export default {
  name: 'ShareApp',
  mixins: [FirebaseMixins],
  data() {
    return {
      myInfo: {},
      summaryData: '',
      myAudio: null,
      audioLoading: false,
      playStatus: false,
      totalTime: 0,
      curTime: 0,
      barWidth: 0,
      moveStatus: false,
      isError: false,
      showId: false,
      speeds: [0.75, 1, 1.25, 1.5, 1.75, 2],
      speedIdx: 1,
      hasLoad: false,
      activeTab: '',
      contentList: [],
    };
  },
  computed: {
    fileInfo() {
      if (this.myInfo.status === 0) {
        return this.myInfo.data_file;
      }
      return {};
    },
    posWidth() {
      return (100 / (this.speeds.length - 1)) * this.speedIdx + '%';
    },
  },
  mounted() {
    let href = document.location.href;
    let token = href.substring(href.lastIndexOf('/') + 1);
    this.getFileInfo(token);
    const search = qs.parse(location.search, { ignoreQueryPrefix: true });
    this.showId = search.show_debug_info === '1';

    const hash = location.hash;
    const queryString = hash.split('?')[1]; // 提取查询字符串部分 => "lang=ja_JP"
    let lang = 'en_US';
    if (queryString) {
      const params = new URLSearchParams(queryString);
      lang = params.get('lang') || lang; // 获取 lang 参数 => "ja_JP"
    } else {
      lang = this.$i18n.locale || lang;
    }
    document.documentElement.setAttribute('lang', lang);

    this.$nextTick(() => {
      this.initFBApp();
      this.setGALogEvent('web_share_page');
    });
  },
  methods: {
    changeTab({ ai_content, original_category, summary_id }) {
      this.activeTab = original_category;
      this.summaryData = markdownSpeakerEditFormat(ai_content.replace(/\\n/g, '\n'));
      if (this.myInfo.is_mindmap) {
        plaudMind.showMindMapWeb('mapBox', this.summaryData, this.fileInfo.filename);
      }
      let template = '',
        model = '';
      // if(this.fileInfo?.extra_data?.tranConfig){
      //   let {type,llm} = this.fileInfo.extra_data.tranConfig;
      //   template = type;
      //   model = llm;
      // }
      this.setGALogEvent('web_share_mulsummary_tab', {
        summaryid: summary_id,
        category: original_category,
        // template,
        // model,
        fileID: this.fileInfo.id,
      });
    },
    getFileInfo(id) {
      this.isLoading = true;
      this.reqGetInfo('/file/share-content/' + id).then(
        (data) => {
          this.isLoading = false;
          this.myInfo = data;
          // this.totalTime = Math.ceil(data.data_file.duration / 1000);

          if (
            (data.is_ai_content || data.is_mindmap) &&
            data.data_file &&
            data.data_file.ai_content
          ) {
            let currentContent = this.fileInfo.ai_content;
            if (data.data_file.ai_content.startsWith('[{')) {
              this.contentList = JSON.parse(data.data_file.ai_content);
              if (this.contentList.length > 0) {
                let { ai_content, original_category } = this.contentList[0];
                currentContent = ai_content;
                this.activeTab = original_category;
              }
              this.setGALogEvent('web_share_page_mulsummary');
            }
            this.summaryData = markdownSpeakerEditFormat(currentContent.replace(/\\n/g, '\n'));
            if (data.is_mindmap) {
              this.$nextTick(() => {
                plaudMind.showMindMapWeb('mapBox', this.summaryData, this.fileInfo.filename);
              });
            }
          }
          if (data.is_audio === 1) {
            this.audioLoading = true;
            let setAudio = (url) => {
              this.audioLoading = false;
              this.$nextTick(() => {
                this.barWidth = this.$refs.audiobar.clientWidth;
                this.init(url);
                this.totalTime = Math.floor(data.data_file.duration / 1000);
              });
            };
            this.reqGetInfo('/file/share-file-temp/' + id).then((audata) => {
              if (audata.status === 0) {
                setAudio(audata.temp_url);
              }
            });
          }
          if (data.is_trans === 1 && data.data_file && data.data_file.trans_result) {
            this.setSpeakerColor(this.fileInfo.trans_result, false);
          }
        },
        (errorData) => {
          this.isError = true;
        },
      );
    },

    init(url) {
      let oldAudio = document.getElementById('myTempAudio');
      if (oldAudio) {
        oldAudio.parentNode.removeChild(oldAudio);
      }

      this.myAudio = new Audio();
      this.myAudio.id = 'myTempAudio';
      let source = document.createElement('source');
      document.body.appendChild(this.myAudio);
      this.myAudio.preload = 'auto';
      source.type = 'audio/mpeg';
      source.src = url;
      this.myAudio.appendChild(source);

      this.myAudio.addEventListener('timeupdate', (e) => {
        this.curTime = Math.floor(this.myAudio.currentTime);
      });
      this.myAudio.addEventListener('ended', (e) => {
        this.audioPlay();
      });
      this.myAudio.addEventListener('loadeddata', (e) => {
        this.hasLoad = true;
      });
    },
    getProBar() {
      if (this.totalTime === 0) {
        return 0;
      }

      let pro = (this.curTime / this.totalTime) * 100 + '%';
      return pro;
    },
    audioPlay(isskip = false) {
      if (this.totalTime <= 0) {
        return false;
      }

      this.playStatus = isskip || !this.playStatus;
      if (this.playStatus) {
        if (isskip) {
          this.myAudio.currentTime = this.curTime;
        }
        this.myAudio.play();
      } else {
        this.myAudio.pause();
      }
    },
    getlastTime(newTime) {
      newTime = Math.max(0, newTime);
      newTime = Math.min(newTime, this.totalTime);
      return newTime;
    },
    changeTime(t) {
      let newTime = this.curTime + t;
      this.myAudio.currentTime = this.getlastTime(newTime);
      this.myAudio.play();
    },
    myMove(e, isMobile = false) {
      let x = e.clientX;
      if (isMobile) {
        x = e.touches[0].pageX;
      }
      // console.log('sss',x)
      this.startInfo = {
        x: x,
        time: this.curTime,
        playStatus: this.playStatus,
        isMobile: isMobile,
      };
      if (this.playStatus) {
        this.myAudio.pause();
        this.playStatus = false;
      }
      this.moveStatus = true;
      document.onmousemove = document.ontouchmove = this.mouseMyMove;
      document.onmousedown = document.onmouseup = document.ontouchend = this.mouseMyUp;
    },
    mouseMyMove(e) {
      if (this.moveStatus) {
        let x = e.clientX;
        if (this.startInfo.isMobile) {
          x = e.touches[0].pageX;
        }
        // console.log('eeee',x,this.barWidth)
        let ma = Math.floor(((x - this.startInfo.x) / this.barWidth) * this.totalTime);
        this.curTime = this.getlastTime(this.startInfo.time + ma);
      }
    },
    mouseMyUp() {
      if (this.moveStatus) {
        this.moveStatus = false;
        this.audioPlay(true);
        this.startInfo = {};
        document.onmousemove = null;
        document.onmouseup = null;
        document.onmousedown = null;

        document.ontouchmove = null;
        document.ontounchend = null;
      }
    },
    handleExploreBtnClick() {
      this.setGALogEvent('web_share_page_explore');
      window.top.location.href = 'https://store.plaud.ai/';
    },
    changeSpeed(index) {
      this.speedIdx = index;
      this.myAudio.playbackRate = this.speeds[index];
    },
  },
};
</script>
