@import url('https://fonts.googleapis.com/css2?family=Material+Icons&display=block');
/* @import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined'); */

/* 默认状态：彻底隐藏所有Material Icons，防止任何闪现 */
.material-symbols-rounded {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  width: 32px;
  height: 32px;
  font-size: 0; /* 文字大小为0，彻底隐藏后备文字 */
  display: inline-block;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  overflow: hidden;
  /* 多重隐藏策略 - 确保万无一失 */
  visibility: hidden; /* 完全隐藏元素 */
  opacity: 0; /* 透明度为0 */
  text-indent: -9999px; /* 将文字推到屏幕外 */
  color: transparent; /* 文字透明 */
  background: transparent; /* 背景透明 */
  border: none; /* 无边框 */
  outline: none; /* 无轮廓 */
}

/* 页面加载完成后显示图标 */
.fonts-ready .material-symbols-rounded,
.material-symbols-rounded.loaded {
  font-size: 32px; /* 恢复图标大小 */
  visibility: visible; /* 显示元素 */
  opacity: 1; /* 恢复透明度 */
  text-indent: 0; /* 恢复文字位置 */
  color: inherit; /* 恢复颜色 */
  transition: opacity 0.2s ease-in; /* 平滑显示 */
}

/* .material-symbols-outlined {
  font-family: 'Material Symbols Outlined';
  font-weight: normal;
  font-style: normal;
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  width: 32px;
  height: 32px;
  margin-right: 8px;
  font-size: 32px;
} */
