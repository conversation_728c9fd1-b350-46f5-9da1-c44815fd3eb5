import Vue from 'vue';
import CommonApp from './CommonApp';

import i18n from './language/index';
Vue.config.productionTip = false;

import RequestMixins from './util/RequestMixins';
Vue.mixin(RequestMixins);

import CommonMixins from '@/util/CommonMixins';
Vue.mixin(CommonMixins);

import Message from './components/common/message';
// Vue.prototype.setMessage = Message; // This will be handled in main.js for Vue 3

import VueClipBoard from 'vue-clipboard2';
Vue.use(VueClipBoard);

import router from './router/common';
import styles from '@/styles/commonapp.scss';

new Vue({
  el: '#app',
  i18n,
  router,
  template: '<CommonApp/>',
  components: { CommonApp },
});
