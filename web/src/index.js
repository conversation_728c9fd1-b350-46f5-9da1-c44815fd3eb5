import { createApp } from 'vue';

// 直接在JavaScript中创建Vue应用，不使用.vue文件
const app = createApp({
  setup() {
    const { ref } = Vue;
    const message = ref('Vue 3 from webpack is working!');
    const buttonText = ref('Click me (webpack version)');

    const updateMessage = () => {
      message.value = 'Webpack Vue 3 reactivity works!';
      buttonText.value = 'Clicked (webpack)!';
    };

    return {
      message,
      buttonText,
      updateMessage
    };
  },
  template: `
    <div>
      <h1>Vue 3 Webpack Test</h1>
      <p>{{ message }}</p>
      <button @click="updateMessage">{{ buttonText }}</button>
    </div>
  `
});

app.mount('#app');
