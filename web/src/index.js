import './common/common';
import './common/date-utils';
import './common/dates';
// localStorage.setItem(
//   'tokenstr',
//   'bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0MzIyMDZiYjJhYTQzYjU3MDZjYWFmYjhkZjQwM2EwNSIsImF1ZCI6IiIsImV4cCI6MTc0OTExMTE4MywiaWF0IjoxNzIzMTkxMTgzLCJjbGllbnRfaWQiOiJ3ZWIifQ.T_1k4YEzqkh97npxOF_f0IsbcYfl_I_FriJ2aXrwiTE',
// );
import * as Sentry from '@sentry/vue';
import { createApp } from 'vue';
import App from './App';
import store from './store';
import router from './router';

// import SvgIcon from './components/common/svg-icon/index.vue';
// Vue.component('SvgIcon', SvgIcon);

// Sentry will be initialized after app creation

// Import mixins and plugins - will be configured after app creation
import RequestMixins from './util/RequestMixins';
import CommonMixins from '@/util/CommonMixins';
import FirebaseMixins from '@/util/FirebaseMixins';
import Message from './components/common/message';
import Confirm from './components/common/confirm';
// vue3-clipboard 暂时移除，后续可以添加
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import './styles/element-variables.scss';

// import mavonEditor from 'mavon-editor';
// import 'mavon-editor/dist/css/index.css';
// Vue.use(mavonEditor);

import './util/Directive';
import i18n, { setLocale, getCurrentLocale } from './language/index';
import analyticsPlugin from './common/tracker-plugin';

import '@/styles/style.scss';
import '@/styles/index.css';
import '@/styles/material-icons.css';

// import vuePerfectScrollbar from 'vue-perfect-scrollbar/index';

import { RecycleScroller, DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
import '@/data/material_icons.js';
import { initMaterialIconsDisplay } from '@/util/material-icons-collection';
// const baseSize = 100;
// function setRem() {
//   let scale = document.documentElement.clientWidth / 1920;
//   scale = Math.min(scale, 1);
//   // scale = Math.max(scale, 0.6);
//   let mySize = baseSize * scale;
//   document.documentElement.style.fontSize = mySize + 'px';
// }
// window.onresize = function () {
//   // setRem();
// };

// Create Vue 3 app
const app = createApp(App);

// Configure app
app.use(store);
app.use(router);
app.use(i18n);
app.use(ElementPlus);
// app.use(VueClipBoard); // 暂时注释掉
app.use(analyticsPlugin, { trackingId: 'G-5N1K2J3M5R' });

// Add mixins
app.mixin(RequestMixins);
app.mixin(CommonMixins);
app.mixin(FirebaseMixins);

// Add global properties
app.config.globalProperties.setMessage = Message;
app.config.globalProperties.setConfirm = Confirm;

// Register global components
app.component('RecycleScroller', RecycleScroller);
app.component('DynamicScroller', DynamicScroller);
app.component('DynamicScrollerItem', DynamicScrollerItem);

// Initialize Sentry
Sentry.init({
  environment: location.hostname.indexOf('plaud.ai') !== -1 ? 'production' : 'test',
  app,
  dsn: 'https://<EMAIL>/3',
  integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
  tracesSampleRate: 1,
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 0.1,
});

// Set locale and mount app
const locale = getCurrentLocale();
await setLocale(locale);

// Provide global data
app.provide('BUS', createApp({}));
app.provide('userInfo', {});
app.provide('userStateInfo', {});
app.provide('myAnalytics', null);

app.mount('#app');

// 初始化 Material Icons 显示控制
initMaterialIconsDisplay();
