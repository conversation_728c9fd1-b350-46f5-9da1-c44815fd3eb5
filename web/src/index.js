import { createApp, ref } from 'vue';

// 暂时回到JavaScript组件，但添加更多功能来测试Vue 3特性
const app = createApp({
  setup() {
    const message = ref('Vue 3 JavaScript Component');
    const buttonText = ref('Test Vue 3 Features');
    const counter = ref(0);

    const updateMessage = () => {
      counter.value++;
      message.value = `Vue 3 works! Clicked ${counter.value} times`;
      buttonText.value = `Click again (${counter.value})`;
    };

    return {
      message,
      buttonText,
      counter,
      updateMessage
    };
  },
  template: `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h1 style="color: #42b883;">{{ message }}</h1>
      <p style="font-size: 18px;">Counter: {{ counter }}</p>
      <button
        @click="updateMessage"
        style="padding: 10px 20px; background: #42b883; color: white; border: none; border-radius: 4px; cursor: pointer;"
      >
        {{ buttonText }}
      </button>
      <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px;">
        <h3>Vue 3 Status: ✅ Working</h3>
        <p>✅ Composition API</p>
        <p>✅ Reactivity System</p>
        <p>✅ Template Compilation</p>
        <p>❌ Single File Components (.vue files)</p>
      </div>
    </div>
  `
});

app.mount('#app');
