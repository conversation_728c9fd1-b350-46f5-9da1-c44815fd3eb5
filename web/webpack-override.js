/**
 * 固定回调，在这里重写webpack的配置
 * @param {*} config webpack的配置,可在此基础上进行修改
 * @param {*} env 环境变了，development｜production
 * @return {*} 必须返回修改后的config
 */
const { name, project } = require('../editor/package.json');

let port = 3000;
const str = name + project;
for (let i of str) {
  port += i.charCodeAt();
}

module.exports = function (config, env) {
  if (env === 'development') {
    // 启用详细的source map用于开发调试
    config.devtool = 'eval-cheap-module-source-map';

    config.devServer.proxy.push({
      context: ['/api'],
      target: 'http://localhost:3000',
      pathRewrite: { '^/api': '' },
    });

    config.devServer.proxy.push({
      context: [`/${name}`],
      target: `http://localhost:${port}`,
      // target: 'http://localhost:3965',
      // pathRewrite: { '^/api': '' },
    });
  }

  // 修复PostCSS配置路径问题
  const path = require('path');
  const postcssConfigPath = path.resolve(__dirname, 'postcss.config.js');

  // 查找并更新PostCSS loader配置
  function updatePostCSSLoader(rules) {
    rules.forEach(rule => {
      if (rule.use && Array.isArray(rule.use)) {
        rule.use.forEach(loader => {
          if (loader.loader && loader.loader.includes('postcss-loader')) {
            if (!loader.options) loader.options = {};
            loader.options.postcssOptions = {
              config: postcssConfigPath
            };
          }
        });
      }
      if (rule.oneOf) {
        updatePostCSSLoader(rule.oneOf);
      }
    });
  }

  if (config.module && config.module.rules) {
    updatePostCSSLoader(config.module.rules);
  }

  // 添加Vue 3 HMR运行时全局变量
  const webpack = require('webpack');

  // 首先确保HMR运行时在全局可用
  const hmrRuntimeCode = `
    window.__VUE_HMR_RUNTIME__ = {
      createRecord: function(id, initialDef) {
        return true;
      },
      reload: function(id, newComp) {
        // 简单的重载实现
      },
      rerender: function(id, newRender) {
        // 简单的重新渲染实现
      },
      updateComponent: function(id, newRecord) {
        // 简单的组件更新实现
      },
      isRecorded: function(id) {
        return false;
      }
    };
  `;

  config.plugins.push(
    new webpack.DefinePlugin({
      __VUE_HMR_RUNTIME__: 'window.__VUE_HMR_RUNTIME__',
      __VUE_OPTIONS_API__: JSON.stringify(true),
      __VUE_PROD_DEVTOOLS__: JSON.stringify(false),
    })
  );

  // 注入HMR运行时代码
  config.plugins.push(
    new webpack.BannerPlugin({
      banner: hmrRuntimeCode,
      raw: true,
      entryOnly: true
    })
  );

  // 添加Vue 3兼容性处理
  config.plugins.push(
    new webpack.NormalModuleReplacementPlugin(
      /vue-resize\/dist\/vue-resize\.esm\.js$/,
      path.resolve(__dirname, 'vue-resize-compat.js')
    )
  );

  // 确保webpack优先使用Vue 3
  config.resolve = config.resolve || {};
  config.resolve.alias = config.resolve.alias || {};

  // 强制使用Vue 3的ESM bundler版本
  config.resolve.alias.vue = path.resolve(__dirname, 'node_modules/vue/dist/vue.runtime.esm-bundler.js');
  config.resolve.alias['vue$'] = path.resolve(__dirname, 'node_modules/vue/dist/vue.runtime.esm-bundler.js');

  // 强制解析Vue 3
  config.resolve.fallback = config.resolve.fallback || {};

  // 确保只使用Vue 3 - 强制所有vue请求都解析到我们的Vue 3
  config.resolve.modules = [
    path.resolve(__dirname, 'node_modules'),
    'node_modules'
  ];

  // 确保正确的模块解析
  config.resolve.mainFields = ['browser', 'module', 'main'];

  // 添加强制解析规则，确保所有vue导入都指向Vue 3
  config.plugins.push(
    new webpack.NormalModuleReplacementPlugin(
      /^vue$/,
      path.resolve(__dirname, 'node_modules/vue/dist/vue.runtime.esm-bundler.js')
    )
  );

  // 添加Vue 3特性标志
  config.plugins.push(
    new webpack.DefinePlugin({
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    })
  );

  config.resolve.alias.vue = 'vue/dist/vue.js';
  config.plugins[1]._options.remotes[name] =
    `${name}@/${name}/remoteEntry.js?${new Date().getTime()}`;
  return config;
};
