// Vue 3 HMR Runtime for development
const records = new Map();

const hmrRuntime = {
  createRecord(id, initialDef) {
    if (records.has(id)) {
      return false;
    }
    records.set(id, {
      instances: new Set(),
      hmrId: id,
      initialDef
    });
    return true;
  },

  updateComponent(id, newRecord) {
    const record = records.get(id);
    if (!record) {
      return;
    }
    
    // Update the record
    record.initialDef = newRecord;
    
    // Trigger re-render for all instances
    record.instances.forEach(instance => {
      if (instance && instance.$forceUpdate) {
        instance.$forceUpdate();
      }
    });
  },

  reload(id, newComp) {
    const record = records.get(id);
    if (!record) {
      return;
    }
    
    // For full reload, we update the component definition
    record.initialDef = newComp;
    
    // Force update all instances
    record.instances.forEach(instance => {
      if (instance && instance.$forceUpdate) {
        instance.$forceUpdate();
      }
    });
  },

  rerender(id, newRender) {
    const record = records.get(id);
    if (!record) {
      return;
    }
    
    // Update render function and force re-render
    if (record.initialDef && newRender) {
      record.initialDef.render = newRender;
    }
    
    record.instances.forEach(instance => {
      if (instance && instance.$forceUpdate) {
        instance.$forceUpdate();
      }
    });
  },

  isRecorded(id) {
    return records.has(id);
  }
};

// Make it globally available
if (typeof window !== 'undefined') {
  window.__VUE_HMR_RUNTIME__ = hmrRuntime;
}

export default hmrRuntime;
