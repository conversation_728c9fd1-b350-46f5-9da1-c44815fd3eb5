<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <title>PLAUD Web</title>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
      <meta name="keywords" content="plaud, audio, transcript">
      <meta name="description" content="PLAUD WEB, powered by PLAUD.AI – Your ultimate tool for seamless file management and text editing. Effortlessly transcribe, summarize, and manage all your meetings, phone calls, and more, directly through your PC browser.">
      <!-- 预加载字体文件，优先级最高 -->
      <link rel="preload" href="https://fonts.googleapis.com/css2?family=Material+Icons&display=block" as="font" type="font/woff2" crossorigin>
      <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Icons&display=block" />
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-5N1K2J3M5R" defer></script>
      <script src="https://accounts.google.com/gsi/client" defer></script>
      <script src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js" defer></script>
      <script src="https://alcdn.msauth.net/browser/2.35.0/js/msal-browser.min.js" defer></script>
      <script src="https://js.stripe.com/v3/" defer></script>
      <% htmlWebpackPlugin.options.styles.forEach(function(item){ %>
        <link rel="stylesheet" href=<%=item %>></script>
      <% }) %>
      <!-- <script>
        // prettier-ignore
        var __ = '{}'
        window.__ENV__ = JSON.parse(__);
      </script> -->
      <% htmlWebpackPlugin.options.scripts.forEach(function(item){ %>
        <script src=<%=item %>></script>
      <% }) %>
      <script>
          window._core__WEBPACK_IMPORTED_MODULE_0__ = {}
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-5N1K2J3M5R');
      </script>
    <% htmlWebpackPlugin.options.styles.forEach(function(item){ %>
      <link rel="stylesheet" href=<%=item %>></script>
    <% }) %>
    <!-- <script>
      // prettier-ignore
      var __ = '{}'
      window.__ENV__ = JSON.parse(__);
    </script> -->
    <% htmlWebpackPlugin.options.scripts.forEach(function(item){ %>
      <script src=<%=item %>></script>
    <% }) %>
    <script src="<%= htmlWebpackPlugin.options.publicPath %>iconfont.js?202410142" defer></script>
  </head>
  <body class="root">
    <div id="app" class="app">
      <h1>Direct Vue 3 Test</h1>
      <p>{{ message }}</p>
      <button @click="updateMessage">{{ buttonText }}</button>
    </div>

    <!-- 直接从CDN加载Vue 3，完全绕过webpack -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
      const { createApp, ref } = Vue;

      createApp({
        setup() {
          const message = ref('Vue 3 is working directly from CDN!');
          const buttonText = ref('Click me to test');

          const updateMessage = () => {
            message.value = 'Vue 3 reactivity works!';
            buttonText.value = 'Clicked!';
          };

          return {
            message,
            buttonText,
            updateMessage
          };
        }
      }).mount('#app');
    </script>

    <script>
      !(function(){const sc=document.createElement('script');sc.id="ze-snippet";sc.src="https://static.zdassets.com/ekr/snippet.js?key=696b9176-2236-4cc6-b5b2-476bdf4fb38c";sc.async=true;sc.defer=true;document.body.appendChild(sc);sc.onload=function(){window.zE('messenger', 'hide');};})()
    </script>
  </body>
</html>
